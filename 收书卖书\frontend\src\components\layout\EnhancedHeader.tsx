import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Layout, 
  Menu, 
  Button, 
  Avatar, 
  Dropdown, 
  Badge, 
  Space, 
  Typography,
  Drawer,
  Grid
} from 'antd';
import {
  UserOutlined,
  ShoppingCartOutlined,
  HeartOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuOutlined,
  BellOutlined,
  SearchOutlined,
  BookOutlined,
  HomeOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';
import { useCartStore } from '../../stores/cartStore';
import SearchSuggestion from '../ui/SearchSuggestion';

const { Header } = Layout;
const { Text } = Typography;
const { useBreakpoint } = Grid;

const StyledHeader = styled(Header)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  padding: 0 24px;
  height: 72px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  
  &.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.12);
  }
  
  @media (max-width: 768px) {
    padding: 0 16px;
    height: 64px;
  }
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  .logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #1677ff, #4096ff);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
  }
  
  .logo-text {
    font-size: 24px;
    font-weight: 800;
    background: linear-gradient(135deg, #1677ff, #4096ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    
    @media (max-width: 768px) {
      display: none;
    }
  }
`;

const NavMenu = styled(Menu)`
  border: none;
  background: transparent;
  font-weight: 500;
  
  .ant-menu-item {
    border-radius: 8px;
    margin: 0 4px;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(22, 119, 255, 0.1);
      color: #1677ff;
    }
    
    &.ant-menu-item-selected {
      background: linear-gradient(135deg, #1677ff, #4096ff);
      color: white;
      
      &::after {
        display: none;
      }
    }
  }
  
  @media (max-width: 992px) {
    display: none;
  }
`;

const SearchContainer = styled.div`
  flex: 1;
  max-width: 500px;
  margin: 0 40px;
  
  @media (max-width: 992px) {
    max-width: 300px;
    margin: 0 20px;
  }
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  
  .action-btn {
    position: relative;
    width: 44px;
    height: 44px;
    border-radius: 12px;
    border: none;
    background: #f8f9fa;
    color: #595959;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:hover {
      background: #e6f4ff;
      color: #1677ff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.2);
    }
    
    &.active {
      background: linear-gradient(135deg, #1677ff, #4096ff);
      color: white;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
    }
  }
  
  .cart-btn {
    .ant-badge {
      .ant-badge-count {
        background: #ff4d4f;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
      }
    }
  }
  
  .mobile-menu-btn {
    display: none;
    
    @media (max-width: 992px) {
      display: flex;
    }
  }
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  
  .user-avatar {
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #1677ff;
      transform: scale(1.1);
    }
  }
  
  .user-info {
    @media (max-width: 768px) {
      display: none;
    }
  }
  
  .auth-buttons {
    display: flex;
    gap: 8px;
    
    .login-btn {
      border-radius: 8px;
      font-weight: 500;
    }
    
    .register-btn {
      border-radius: 8px;
      font-weight: 500;
      background: linear-gradient(135deg, #1677ff, #4096ff);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #0958d9, #1677ff);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
      }
    }
  }
`;

const MobileDrawer = styled(Drawer)`
  .ant-drawer-body {
    padding: 0;
  }
  
  .mobile-menu {
    .ant-menu-item {
      margin: 4px 16px;
      border-radius: 8px;
      
      &.ant-menu-item-selected {
        background: linear-gradient(135deg, #1677ff, #4096ff);
        color: white;
      }
    }
  }
  
  .mobile-search {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .mobile-user {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
  }
`;

interface EnhancedHeaderProps {
  className?: string;
}

const EnhancedHeader: React.FC<EnhancedHeaderProps> = ({ className }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuthStore();
  const { items } = useCartStore();
  const screens = useBreakpoint();
  
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  // 监听滚动
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 计算购物车商品数量
  const cartItemCount = items.reduce((total, item) => total + item.quantity, 0);

  // 获取当前选中的菜单项
  const getSelectedKey = () => {
    const path = location.pathname;
    if (path === '/') return 'home';
    if (path.startsWith('/books')) return 'books';
    if (path.startsWith('/cart')) return 'cart';
    if (path.startsWith('/orders')) return 'orders';
    if (path.startsWith('/profile')) return 'profile';
    if (path.startsWith('/admin')) return 'admin';
    return '';
  };

  // 菜单项
  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '首页',
      onClick: () => navigate('/')
    },
    {
      key: 'books',
      icon: <BookOutlined />,
      label: '图书',
      onClick: () => navigate('/books')
    }
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => navigate('/profile')
    },
    {
      key: 'orders',
      icon: <ShoppingCartOutlined />,
      label: '我的订单',
      onClick: () => navigate('/orders')
    },
    {
      key: 'favorites',
      icon: <HeartOutlined />,
      label: '我的收藏',
      onClick: () => navigate('/profile/favorites')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/profile/settings')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout();
        navigate('/');
      }
    }
  ];

  // 管理员菜单项
  if (user?.role === 'admin' || user?.role === 'super_admin') {
    userMenuItems.unshift({
      key: 'admin',
      icon: <DashboardOutlined />,
      label: '管理后台',
      onClick: () => navigate('/admin')
    });
  }

  const handleSearch = (value: string) => {
    if (value.trim()) {
      navigate(`/books?search=${encodeURIComponent(value.trim())}`);
    }
  };

  return (
    <>
      <StyledHeader className={`${scrolled ? 'scrolled' : ''} ${className || ''}`}>
        <HeaderContent>
          {/* Logo */}
          <Logo onClick={() => navigate('/')}>
            <div className="logo-icon">
              <BookOutlined />
            </div>
            <div className="logo-text">收书卖书</div>
          </Logo>

          {/* 导航菜单 */}
          <NavMenu
            mode="horizontal"
            selectedKeys={[getSelectedKey()]}
            items={menuItems}
          />

          {/* 搜索框 */}
          <SearchContainer>
            <SearchSuggestion
              placeholder="搜索图书、作者、分类..."
              onSearch={handleSearch}
              size="large"
            />
          </SearchContainer>

          {/* 操作按钮 */}
          <ActionButtons>
            {/* 移动端搜索按钮 */}
            {!screens.md && (
              <button className="action-btn">
                <SearchOutlined />
              </button>
            )}

            {/* 通知按钮 */}
            {isAuthenticated && (
              <Badge count={0} size="small">
                <button className="action-btn">
                  <BellOutlined />
                </button>
              </Badge>
            )}

            {/* 购物车按钮 */}
            <Badge count={cartItemCount} size="small" className="cart-btn">
              <button 
                className={`action-btn ${location.pathname === '/cart' ? 'active' : ''}`}
                onClick={() => navigate('/cart')}
              >
                <ShoppingCartOutlined />
              </button>
            </Badge>

            {/* 移动端菜单按钮 */}
            <button 
              className="action-btn mobile-menu-btn"
              onClick={() => setMobileMenuVisible(true)}
            >
              <MenuOutlined />
            </button>
          </ActionButtons>

          {/* 用户区域 */}
          <UserSection>
            {isAuthenticated ? (
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Space style={{ cursor: 'pointer' }}>
                  <Avatar 
                    src={user?.avatar} 
                    icon={<UserOutlined />}
                    size={40}
                    className="user-avatar"
                  />
                  <div className="user-info">
                    <Text strong>{user?.username || user?.phone}</Text>
                  </div>
                </Space>
              </Dropdown>
            ) : (
              <div className="auth-buttons">
                <Button 
                  className="login-btn"
                  onClick={() => navigate('/login')}
                >
                  登录
                </Button>
                <Button 
                  type="primary" 
                  className="register-btn"
                  onClick={() => navigate('/register')}
                >
                  注册
                </Button>
              </div>
            )}
          </UserSection>
        </HeaderContent>
      </StyledHeader>

      {/* 移动端抽屉菜单 */}
      <MobileDrawer
        title="菜单"
        placement="right"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        width={280}
      >
        {/* 移动端搜索 */}
        <div className="mobile-search">
          <SearchSuggestion
            placeholder="搜索图书..."
            onSearch={(value) => {
              handleSearch(value);
              setMobileMenuVisible(false);
            }}
          />
        </div>

        {/* 移动端菜单 */}
        <Menu
          mode="vertical"
          selectedKeys={[getSelectedKey()]}
          className="mobile-menu"
          items={[
            ...menuItems,
            ...(isAuthenticated ? userMenuItems : [])
          ]}
          onClick={() => setMobileMenuVisible(false)}
        />

        {/* 移动端用户信息 */}
        {!isAuthenticated && (
          <div className="mobile-user">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                block 
                onClick={() => {
                  navigate('/login');
                  setMobileMenuVisible(false);
                }}
              >
                登录
              </Button>
              <Button 
                type="primary" 
                block
                onClick={() => {
                  navigate('/register');
                  setMobileMenuVisible(false);
                }}
              >
                注册
              </Button>
            </Space>
          </div>
        )}
      </MobileDrawer>
    </>
  );
};

export default EnhancedHeader;
