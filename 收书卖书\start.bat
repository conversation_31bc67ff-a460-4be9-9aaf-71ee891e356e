@echo off
echo 启动收书卖书平台...
echo.

echo 1. 启动后端服务...
cd backend
start "后端服务" cmd /k "npm run dev"
cd ..

echo 2. 等待后端服务启动...
timeout /t 5 /nobreak > nul

echo 3. 启动前端应用...
cd frontend
start "前端应用" cmd /k "npm start"
cd ..

echo.
echo 服务启动完成！
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:3001
echo.
echo 默认管理员账号:
echo 超级管理员: 13800000000 / admin123456
echo 普通管理员: 13800000001 / admin123456
echo 测试用户: 13800000002-13800000006 / test123456
echo.
pause
