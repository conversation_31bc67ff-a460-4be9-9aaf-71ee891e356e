/* 移动端适配样式 */

/* 基础响应式设置 */
@media (max-width: 768px) {
  /* 全局设置 */
  body {
    font-size: 14px;
    line-height: 1.4;
  }
  
  /* 容器适配 */
  .container {
    padding: 0 16px;
  }
  
  /* 标题适配 */
  h1 {
    font-size: 24px;
  }
  
  h2 {
    font-size: 20px;
  }
  
  h3 {
    font-size: 18px;
  }
  
  /* 按钮适配 */
  .ant-btn {
    height: 44px;
    padding: 0 16px;
    font-size: 16px;
    border-radius: 8px;
  }
  
  .ant-btn-sm {
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
  }
  
  .ant-btn-lg {
    height: 52px;
    padding: 0 20px;
    font-size: 18px;
  }
  
  /* 输入框适配 */
  .ant-input {
    height: 44px;
    padding: 0 12px;
    font-size: 16px;
    border-radius: 8px;
  }
  
  .ant-input-lg {
    height: 52px;
    padding: 0 16px;
    font-size: 18px;
  }
  
  /* 卡片适配 */
  .ant-card {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .ant-card-body {
    padding: 16px;
  }
  
  /* 表格适配 */
  .ant-table {
    font-size: 14px;
  }
  
  .ant-table-thead > tr > th {
    padding: 12px 8px;
    font-size: 14px;
  }
  
  .ant-table-tbody > tr > td {
    padding: 12px 8px;
    font-size: 14px;
  }
  
  /* 模态框适配 */
  .ant-modal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .ant-modal-content {
    border-radius: 12px;
  }
  
  .ant-modal-header {
    padding: 16px 20px;
    border-radius: 12px 12px 0 0;
  }
  
  .ant-modal-body {
    padding: 20px;
  }
  
  .ant-modal-footer {
    padding: 16px 20px;
    border-radius: 0 0 12px 12px;
  }
  
  /* 抽屉适配 */
  .ant-drawer-content {
    border-radius: 16px 16px 0 0;
  }
  
  .ant-drawer-header {
    padding: 16px 20px;
  }
  
  .ant-drawer-body {
    padding: 20px;
  }
  
  /* 导航栏适配 */
  .mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 12px 16px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .back-btn {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        border: none;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: #262626;
      }
      
      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .header-action {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        border: none;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: #262626;
      }
    }
  }
  
  /* 底部导航栏 */
  .mobile-tabbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border-top: 1px solid #f0f0f0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
    display: flex;
    
    .tabbar-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 4px;
      text-decoration: none;
      color: #8c8c8c;
      transition: color 0.3s ease;
      
      &.active {
        color: #1677ff;
      }
      
      .tabbar-icon {
        font-size: 20px;
        margin-bottom: 4px;
      }
      
      .tabbar-label {
        font-size: 12px;
        line-height: 1;
      }
      
      .tabbar-badge {
        position: absolute;
        top: 4px;
        right: 50%;
        transform: translateX(50%);
        background: #ff4d4f;
        color: white;
        border-radius: 10px;
        padding: 2px 6px;
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  
  /* 页面内容适配 */
  .mobile-page {
    padding-top: 64px;
    padding-bottom: calc(60px + env(safe-area-inset-bottom));
    min-height: 100vh;
  }
  
  /* 图书卡片移动端适配 */
  .mobile-book-card {
    display: flex;
    padding: 16px;
    background: white;
    border-radius: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    .book-cover {
      width: 80px;
      height: 100px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;
      margin-right: 12px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    
    .book-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .book-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 4px;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .book-author {
        font-size: 13px;
        color: #8c8c8c;
        margin-bottom: 8px;
      }
      
      .book-meta {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        
        .condition-tag {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 4px;
        }
        
        .book-stats {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .book-price {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: auto;
        
        .price-info {
          display: flex;
          align-items: baseline;
          gap: 6px;
          
          .current-price {
            font-size: 18px;
            font-weight: 700;
            color: #ff4d4f;
          }
          
          .original-price {
            font-size: 13px;
            color: #8c8c8c;
            text-decoration: line-through;
          }
        }
        
        .book-actions {
          display: flex;
          gap: 8px;
          
          .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            border: 1px solid #d9d9d9;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #595959;
          }
        }
      }
    }
  }
  
  /* 搜索框移动端适配 */
  .mobile-search {
    padding: 16px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    
    .search-input {
      width: 100%;
      height: 44px;
      border-radius: 22px;
      border: 1px solid #d9d9d9;
      padding: 0 16px 0 44px;
      font-size: 16px;
      background: #fafafa;
      position: relative;
      
      &::placeholder {
        color: #bfbfbf;
      }
    }
    
    .search-icon {
      position: absolute;
      left: 32px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 16px;
      color: #bfbfbf;
      pointer-events: none;
    }
  }
  
  /* 筛选栏移动端适配 */
  .mobile-filters {
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    gap: 8px;
    overflow-x: auto;
    
    .filter-item {
      flex-shrink: 0;
      padding: 6px 12px;
      border-radius: 16px;
      border: 1px solid #d9d9d9;
      background: white;
      font-size: 14px;
      color: #595959;
      white-space: nowrap;
      
      &.active {
        background: #1677ff;
        border-color: #1677ff;
        color: white;
      }
    }
  }
  
  /* 加载更多按钮 */
  .mobile-load-more {
    padding: 20px 16px;
    text-align: center;
    
    .load-more-btn {
      width: 100%;
      height: 44px;
      border-radius: 8px;
      border: 1px solid #d9d9d9;
      background: white;
      font-size: 16px;
      color: #595959;
    }
  }
  
  /* 购物车移动端适配 */
  .mobile-cart {
    .cart-item {
      display: flex;
      padding: 16px;
      background: white;
      border-bottom: 1px solid #f0f0f0;
      
      .item-checkbox {
        margin-right: 12px;
        align-self: flex-start;
        margin-top: 8px;
      }
      
      .item-image {
        width: 60px;
        height: 75px;
        border-radius: 6px;
        overflow: hidden;
        margin-right: 12px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .item-info {
        flex: 1;
        
        .item-title {
          font-size: 15px;
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
          line-height: 1.3;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .item-author {
          font-size: 13px;
          color: #8c8c8c;
          margin-bottom: 8px;
        }
        
        .item-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .item-price {
            font-size: 16px;
            font-weight: 600;
            color: #ff4d4f;
          }
          
          .quantity-control {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .quantity-btn {
              width: 28px;
              height: 28px;
              border-radius: 6px;
              border: 1px solid #d9d9d9;
              background: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              color: #595959;
            }
            
            .quantity-input {
              width: 40px;
              height: 28px;
              text-align: center;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .cart-summary {
      position: fixed;
      bottom: calc(60px + env(safe-area-inset-bottom));
      left: 0;
      right: 0;
      background: white;
      border-top: 1px solid #f0f0f0;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .summary-left {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .select-all {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
        }
        
        .total-price {
          font-size: 18px;
          font-weight: 700;
          color: #ff4d4f;
        }
      }
      
      .checkout-btn {
        height: 44px;
        padding: 0 24px;
        border-radius: 22px;
        background: #1677ff;
        border: none;
        color: white;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
  
  /* 安全区域适配 */
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  /* 触摸优化 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* 滚动优化 */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }
  
  .ant-card-body {
    padding: 12px;
  }
  
  .mobile-book-card {
    padding: 12px;
    
    .book-cover {
      width: 70px;
      height: 88px;
    }
  }
  
  .mobile-header {
    padding: 8px 12px;
    
    .header-title {
      font-size: 16px;
    }
  }
}
