#!/bin/bash

# 收书卖书平台停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止收书卖书平台服务..."
    
    # 停止后端服务
    if [ -f "logs/backend.pid" ]; then
        BACKEND_PID=$(cat logs/backend.pid)
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            log_info "停止后端服务 (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
            log_success "后端服务已停止"
        else
            log_warning "后端服务进程不存在"
        fi
        rm -f logs/backend.pid
    else
        log_warning "未找到后端服务进程ID文件"
    fi
    
    # 停止前端服务
    if [ -f "logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat logs/frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            log_info "停止前端服务 (PID: $FRONTEND_PID)..."
            kill $FRONTEND_PID
            log_success "前端服务已停止"
        else
            log_warning "前端服务进程不存在"
        fi
        rm -f logs/frontend.pid
    else
        log_warning "未找到前端服务进程ID文件"
    fi
    
    # 强制停止相关进程
    log_info "检查并停止相关进程..."
    
    # 停止 Node.js 进程 (端口 3000, 3001)
    pkill -f "node.*3000" 2>/dev/null || true
    pkill -f "node.*3001" 2>/dev/null || true
    
    # 停止 npm 进程
    pkill -f "npm.*start" 2>/dev/null || true
    pkill -f "npm.*dev" 2>/dev/null || true
    
    log_success "所有服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理临时文件..."
    
    # 清理日志文件 (可选)
    if [ "$1" = "--clean-logs" ]; then
        rm -f logs/*.log
        log_info "日志文件已清理"
    fi
    
    # 清理进程ID文件
    rm -f logs/*.pid
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "收书卖书平台停止脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --clean-logs    同时清理日志文件"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 --clean-logs"
}

# 主函数
main() {
    local clean_logs=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean-logs)
                clean_logs=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo "🛑 收书卖书平台停止脚本"
    echo "================================"
    echo ""
    
    # 停止服务
    stop_services
    
    # 清理资源
    if [ "$clean_logs" = true ]; then
        cleanup --clean-logs
    else
        cleanup
    fi
    
    echo ""
    log_success "收书卖书平台已完全停止！"
}

# 执行主函数
main "$@"
