import React, { useState, useEffect } from 'react';
import { Button, message, Tooltip } from 'antd';
import { HeartOutlined, HeartFilled } from '@ant-design/icons';
import { favoritesService } from '../../services/favorites';
import { useAuthStore } from '../../stores/authStore';

interface FavoriteButtonProps {
  bookId: string;
  size?: 'small' | 'middle' | 'large';
  type?: 'default' | 'primary' | 'text' | 'link';
  showText?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onFavoriteChange?: (isFavorited: boolean) => void;
}

const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  bookId,
  size = 'middle',
  type = 'default',
  showText = false,
  className,
  style,
  onFavoriteChange
}) => {
  const { isAuthenticated } = useAuthStore();
  const [isFavorited, setIsFavorited] = useState(false);
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(true);

  // 检查收藏状态
  useEffect(() => {
    if (isAuthenticated && bookId) {
      checkFavoriteStatus();
    } else {
      setChecking(false);
    }
  }, [isAuthenticated, bookId]);

  const checkFavoriteStatus = async () => {
    try {
      setChecking(true);
      const response = await favoritesService.checkFavorite(bookId);
      if (response.success) {
        setIsFavorited(response.data.is_favorited);
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error);
    } finally {
      setChecking(false);
    }
  };

  const handleToggleFavorite = async () => {
    if (!isAuthenticated) {
      message.warning('请先登录');
      return;
    }

    try {
      setLoading(true);
      
      if (isFavorited) {
        // 取消收藏
        const response = await favoritesService.removeFavorite(bookId);
        if (response.success) {
          setIsFavorited(false);
          message.success('已取消收藏');
          onFavoriteChange?.(false);
        }
      } else {
        // 添加收藏
        const response = await favoritesService.addFavorite(bookId);
        if (response.success) {
          setIsFavorited(true);
          message.success('收藏成功');
          onFavoriteChange?.(true);
        }
      }
    } catch (error: any) {
      console.error('操作收藏失败:', error);
      if (error.response?.status === 409) {
        message.warning('已经收藏过这本图书');
        setIsFavorited(true);
      } else {
        message.error('操作失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  const buttonProps = {
    size,
    type: isFavorited ? 'primary' : type,
    loading: loading || checking,
    onClick: handleToggleFavorite,
    className,
    style,
    icon: isFavorited ? <HeartFilled /> : <HeartOutlined />
  };

  const buttonText = showText ? (isFavorited ? '已收藏' : '收藏') : '';

  if (!isAuthenticated) {
    return (
      <Tooltip title="请先登录">
        <Button
          {...buttonProps}
          disabled
          loading={false}
        >
          {buttonText}
        </Button>
      </Tooltip>
    );
  }

  return (
    <Tooltip title={isFavorited ? '取消收藏' : '收藏图书'}>
      <Button {...buttonProps}>
        {buttonText}
      </Button>
    </Tooltip>
  );
};

export default FavoriteButton;
