import React, { useEffect, useState } from 'react';
import {
  Card,
  Tree,
  Button,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  message,
  Popconfirm,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DragOutlined
} from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';
import { Category } from '../../types';
import { categoriesService } from '../../services/categories';

const { Title } = Typography;
const { TextArea } = Input;

interface CategoryNode extends DataNode {
  category: Category;
  children?: CategoryNode[];
}

const AdminCategories: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [treeData, setTreeData] = useState<CategoryNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [parentCategory, setParentCategory] = useState<Category | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await categoriesService.getCategories();
      if (response.success) {
        const categoryList = response.data || [];
        setCategories(categoryList);
        setTreeData(buildTreeData(categoryList));
      }
    } catch (error) {
      console.error('加载分类失败:', error);
      message.error('加载分类失败');
    } finally {
      setLoading(false);
    }
  };

  const buildTreeData = (categoryList: Category[]): CategoryNode[] => {
    const categoryMap = new Map<string, CategoryNode>();
    const rootNodes: CategoryNode[] = [];

    // 创建所有节点
    categoryList.forEach(category => {
      const node: CategoryNode = {
        key: category.id,
        title: (
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>
              {category.name}
              {!category.is_active && <span style={{ color: '#999', marginLeft: 8 }}>(已禁用)</span>}
            </span>
            <Space size="small">
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddChild(category);
                }}
              >
                添加子分类
              </Button>
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEdit(category);
                }}
              >
                编辑
              </Button>
              <Popconfirm
                title="确定要删除这个分类吗？"
                onConfirm={(e) => {
                  e?.stopPropagation();
                  handleDelete(category.id);
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={(e) => e.stopPropagation()}
                >
                  删除
                </Button>
              </Popconfirm>
            </Space>
          </div>
        ),
        category,
        children: []
      };
      categoryMap.set(category.id, node);
    });

    // 构建树结构
    categoryList.forEach(category => {
      const node = categoryMap.get(category.id)!;
      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children!.push(node);
        }
      } else {
        rootNodes.push(node);
      }
    });

    // 按排序顺序排序
    const sortNodes = (nodes: CategoryNode[]) => {
      nodes.sort((a, b) => a.category.sort_order - b.category.sort_order);
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          sortNodes(node.children);
        }
      });
    };

    sortNodes(rootNodes);
    return rootNodes;
  };

  const handleAdd = () => {
    setEditingCategory(null);
    setParentCategory(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleAddChild = (parent: Category) => {
    setEditingCategory(null);
    setParentCategory(parent);
    form.resetFields();
    form.setFieldsValue({
      parent_id: parent.id
    });
    setModalVisible(true);
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setParentCategory(null);
    form.setFieldsValue({
      name: category.name,
      description: category.description,
      parent_id: category.parent_id,
      sort_order: category.sort_order,
      is_active: category.is_active
    });
    setModalVisible(true);
  };

  const handleDelete = async (categoryId: string) => {
    try {
      const response = await categoriesService.deleteCategory(categoryId);
      if (response.success) {
        message.success('分类删除成功');
        loadCategories();
      }
    } catch (error) {
      console.error('删除分类失败:', error);
      message.error('删除分类失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      let response;
      if (editingCategory) {
        response = await categoriesService.updateCategory(editingCategory.id, values);
      } else {
        response = await categoriesService.createCategory(values);
      }

      if (response.success) {
        message.success(editingCategory ? '分类更新成功' : '分类创建成功');
        setModalVisible(false);
        loadCategories();
      }
    } catch (error) {
      console.error('保存分类失败:', error);
      message.error('保存分类失败');
    }
  };

  const getParentOptions = (excludeId?: string) => {
    const options: { value: string; label: string }[] = [];

    const addOptions = (categoryList: Category[], level = 0) => {
      categoryList.forEach(category => {
        if (category.id !== excludeId) {
          options.push({
            value: category.id,
            label: '　'.repeat(level) + category.name
          });

          const children = categoryList.filter(c => c.parent_id === category.id);
          if (children.length > 0) {
            addOptions(children, level + 1);
          }
        }
      });
    };

    const rootCategories = categories.filter(c => !c.parent_id);
    addOptions(rootCategories);

    return options;
  };

  return (
    <div>
      <Title level={2}>分类管理</Title>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加根分类
          </Button>
        </div>

        <Tree
          treeData={treeData}
          defaultExpandAll
          showLine
          blockNode
          loading={loading}
        />
      </Card>

      {/* 添加/编辑分类模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : (parentCategory ? `添加子分类 - ${parentCategory.name}` : '添加根分类')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            is_active: true,
            sort_order: 0
          }}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[
              { required: true, message: '请输入分类名称' },
              { max: 50, message: '分类名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
            rules={[
              { max: 200, message: '分类描述不能超过200个字符' }
            ]}
          >
            <TextArea
              rows={3}
              placeholder="请输入分类描述（可选）"
            />
          </Form.Item>

          {!parentCategory && (
            <Form.Item
              name="parent_id"
              label="父分类"
            >
              <select
                style={{ width: '100%', padding: '4px 8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
              >
                <option value="">无（根分类）</option>
                {getParentOptions(editingCategory?.id).map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="sort_order"
                label="排序"
                rules={[
                  { required: true, message: '请输入排序值' }
                ]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="数值越小越靠前"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="状态"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminCategories;
