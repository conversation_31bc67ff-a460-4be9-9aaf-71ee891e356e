import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { TagsOutlined, PlusOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const AdminCategories: React.FC = () => {
  return (
    <div>
      <Title level={2}>分类管理</Title>
      
      <Card>
        <div style={{ textAlign: 'center', padding: '64px 0' }}>
          <TagsOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={3}>分类管理功能</Title>
          <Paragraph type="secondary">
            这里可以管理图书分类，包括添加新分类、编辑分类信息、设置分类层级等功能。
          </Paragraph>
          <Space>
            <Button type="primary" icon={<PlusOutlined />}>
              添加分类
            </Button>
            <Button>
              批量管理
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default AdminCategories;
