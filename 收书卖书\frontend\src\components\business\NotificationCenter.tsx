import React, { useState, useEffect } from 'react';
import {
  Drawer,
  List,
  Badge,
  Button,
  Typography,
  Space,
  Tag,
  Avatar,
  Empty,
  Spin,
  Dropdown,
  Menu,
  Divider,
  Tooltip,
  message
} from 'antd';
import {
  BellOutlined,
  SettingOutlined,
  DeleteOutlined,
  CheckOutlined,
  EyeOutlined,
  FilterOutlined,
  ClearOutlined,
  MessageOutlined,
  GiftOutlined,
  SoundOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { notificationsService } from '../../services/notifications';
import { useAuthStore } from '../../stores/authStore';

const { Text, Title } = Typography;

const NotificationContainer = styled.div`
  .notification-trigger {
    position: relative;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    .notification-icon {
      font-size: 18px;
      color: #595959;
    }
  }
  
  .notification-drawer {
    .ant-drawer-header {
      border-bottom: 1px solid #f0f0f0;
      
      .drawer-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .title-left {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .title-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
    
    .notification-filters {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
      
      .filter-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        
        .filter-btn {
          border-radius: 16px;
          height: 32px;
          padding: 0 12px;
          font-size: 12px;
          
          &.active {
            background: #1677ff;
            border-color: #1677ff;
            color: white;
          }
        }
      }
    }
    
    .notification-list {
      .notification-item {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.3s ease;
        
        &:hover {
          background-color: #f8f9fa;
        }
        
        &.unread {
          background-color: #f6ffed;
          border-left: 3px solid #52c41a;
        }
        
        .item-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .item-left {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            flex: 1;
            
            .item-avatar {
              flex-shrink: 0;
              margin-top: 2px;
            }
            
            .item-content {
              flex: 1;
              min-width: 0;
              
              .item-title {
                font-weight: 600;
                color: #262626;
                margin-bottom: 4px;
                line-height: 1.4;
              }
              
              .item-description {
                color: #595959;
                font-size: 14px;
                line-height: 1.5;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }
          }
          
          .item-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }
        
        &:hover .item-actions {
          opacity: 1;
        }
        
        .item-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 8px;
          
          .item-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .item-time {
              color: #8c8c8c;
              font-size: 12px;
            }
            
            .item-type {
              font-size: 11px;
            }
            
            .item-priority {
              font-size: 11px;
              
              &.high {
                color: #ff4d4f;
              }
              
              &.urgent {
                color: #ff4d4f;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
    
    .notification-empty {
      padding: 40px 20px;
      text-align: center;
    }
    
    .notification-footer {
      padding: 16px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
      text-align: center;
      
      .footer-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
      }
    }
  }
`;

interface Notification {
  id: string;
  title: string;
  content: string;
  type: string;
  priority: string;
  status: string;
  action_type?: string;
  action_data?: any;
  created_at: string;
  read_at?: string;
  sender?: {
    id: string;
    username: string;
    avatar?: string;
  };
}

interface NotificationCenterProps {
  className?: string;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ className }) => {
  const { isAuthenticated } = useAuthStore();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [currentFilter, setCurrentFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // 过滤选项
  const filterOptions = [
    { key: 'all', label: '全部', color: 'default' },
    { key: 'unread', label: '未读', color: 'blue' },
    { key: 'system', label: '系统', color: 'purple' },
    { key: 'order', label: '订单', color: 'green' },
    { key: 'book', label: '图书', color: 'orange' },
    { key: 'review', label: '评论', color: 'cyan' },
    { key: 'promotion', label: '促销', color: 'red' }
  ];

  useEffect(() => {
    if (isAuthenticated) {
      loadUnreadCount();
      // 定时刷新未读数量
      const interval = setInterval(loadUnreadCount, 30000);
      return () => clearInterval(interval);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (visible) {
      loadNotifications(true);
    }
  }, [visible, currentFilter]);

  const loadUnreadCount = async () => {
    try {
      const response = await notificationsService.getUnreadCount();
      if (response.success && response.data) {
        setUnreadCount(response.data.count);
      }
    } catch (error) {
      console.error('加载未读数量失败:', error);
    }
  };

  const loadNotifications = async (reset = false) => {
    try {
      setLoading(true);
      const currentPage = reset ? 1 : page;
      
      const response = await notificationsService.getNotifications({
        page: currentPage,
        limit: 20,
        status: currentFilter === 'unread' ? 'unread' : 'all',
        type: ['system', 'order', 'book', 'review', 'promotion'].includes(currentFilter) ? currentFilter : undefined
      });

      if (response.success) {
        const newNotifications = response.data.notifications;
        
        if (reset) {
          setNotifications(newNotifications);
          setPage(2);
        } else {
          setNotifications(prev => [...prev, ...newNotifications]);
          setPage(prev => prev + 1);
        }
        
        setHasMore(response.data.pagination.current_page < response.data.pagination.total_pages);
      }
    } catch (error) {
      message.error('加载通知失败');
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationsService.markAsRead(notificationId);
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId
            ? { ...notif, status: 'read', read_at: new Date().toISOString() }
            : notif
        )
      );
      loadUnreadCount();
    } catch (error) {
      message.error('标记已读失败');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationsService.markAllAsRead();
      setNotifications(prev =>
        prev.map(notif => ({
          ...notif,
          status: 'read',
          read_at: new Date().toISOString()
        }))
      );
      setUnreadCount(0);
      message.success('全部标记为已读');
    } catch (error) {
      message.error('操作失败');
    }
  };

  const handleDelete = async (notificationId: string) => {
    try {
      await notificationsService.deleteNotification(notificationId);
      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
      loadUnreadCount();
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    // 如果未读，标记为已读
    if (notification.status === 'unread') {
      await handleMarkAsRead(notification.id);
    }

    // 处理点击动作
    if (notification.action_type && notification.action_data) {
      switch (notification.action_type) {
        case 'view_order':
          // 跳转到订单详情
          window.location.href = `/orders/${notification.action_data.order_id}`;
          break;
        case 'view_book':
          // 跳转到图书详情
          window.location.href = `/books/${notification.action_data.book_id}`;
          break;
        case 'view_review':
          // 跳转到评论
          window.location.href = `/books/${notification.action_data.book_id}#review-${notification.action_data.review_id}`;
          break;
        default:
          break;
      }
    }
  };

  const getNotificationIcon = (type: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      system: <SettingOutlined />,
      order: <CheckOutlined />,
      book: <EyeOutlined />,
      review: <MessageOutlined />,
      promotion: <GiftOutlined />,
      announcement: <SoundOutlined />
    };
    return iconMap[type] || <BellOutlined />;
  };

  const getTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      system: 'purple',
      order: 'green',
      book: 'orange',
      review: 'cyan',
      promotion: 'red',
      announcement: 'blue'
    };
    return colorMap[type] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colorMap: Record<string, string> = {
      low: 'default',
      normal: 'blue',
      high: 'orange',
      urgent: 'red'
    };
    return colorMap[priority] || 'default';
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;
    
    return date.toLocaleDateString();
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <NotificationContainer className={className}>
      <div className="notification-trigger" onClick={() => setVisible(true)}>
        <Badge count={unreadCount} size="small">
          <BellOutlined className="notification-icon" />
        </Badge>
      </div>

      <Drawer
        title={
          <div className="drawer-title">
            <div className="title-left">
              <BellOutlined />
              <span>通知中心</span>
              {unreadCount > 0 && (
                <Badge count={unreadCount} size="small" />
              )}
            </div>
            <div className="title-actions">
              <Tooltip title="全部标记为已读">
                <Button
                  type="text"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={handleMarkAllAsRead}
                  disabled={unreadCount === 0}
                />
              </Tooltip>
            </div>
          </div>
        }
        placement="right"
        width={400}
        open={visible}
        onClose={() => setVisible(false)}
        className="notification-drawer"
      >
        {/* 过滤器 */}
        <div className="notification-filters">
          <div className="filter-buttons">
            {filterOptions.map(option => (
              <Button
                key={option.key}
                size="small"
                className={`filter-btn ${currentFilter === option.key ? 'active' : ''}`}
                onClick={() => setCurrentFilter(option.key)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>

        {/* 通知列表 */}
        <div className="notification-list">
          {notifications.length > 0 ? (
            <List
              dataSource={notifications}
              loading={loading}
              renderItem={(notification) => (
                <div
                  key={notification.id}
                  className={`notification-item ${notification.status === 'unread' ? 'unread' : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="item-header">
                    <div className="item-left">
                      <Avatar
                        className="item-avatar"
                        size="small"
                        icon={getNotificationIcon(notification.type)}
                        src={notification.sender?.avatar}
                      />
                      <div className="item-content">
                        <div className="item-title">{notification.title}</div>
                        <div className="item-description">{notification.content}</div>
                      </div>
                    </div>
                    <div className="item-actions">
                      {notification.status === 'unread' && (
                        <Tooltip title="标记为已读">
                          <Button
                            type="text"
                            size="small"
                            icon={<CheckOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleMarkAsRead(notification.id);
                            }}
                          />
                        </Tooltip>
                      )}
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(notification.id);
                          }}
                        />
                      </Tooltip>
                    </div>
                  </div>
                  <div className="item-footer">
                    <div className="item-meta">
                      <span className="item-time">{formatTime(notification.created_at)}</span>
                      <Tag color={getTypeColor(notification.type)}>
                        {notification.type}
                      </Tag>
                      {notification.priority !== 'normal' && (
                        <Tag
                          color={getPriorityColor(notification.priority)}
                          className={`item-priority ${notification.priority}`}
                        >
                          {notification.priority}
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              )}
            />
          ) : (
            <div className="notification-empty">
              <Empty
                description="暂无通知"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          )}
        </div>

        {/* 加载更多 */}
        {hasMore && notifications.length > 0 && (
          <div className="notification-footer">
            <Button
              type="link"
              loading={loading}
              onClick={() => loadNotifications(false)}
            >
              加载更多
            </Button>
          </div>
        )}
      </Drawer>
    </NotificationContainer>
  );
};

export default NotificationCenter;
