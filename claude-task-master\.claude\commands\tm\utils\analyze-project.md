Advanced project analysis with actionable insights and recommendations.

Arguments: $ARGUMENTS

## Comprehensive Project Analysis

Multi-dimensional analysis based on requested focus area.

### 1. **Analysis Modes**

Based on $ARGUMENTS:
- "velocity" → Sprint velocity and trends
- "quality" → Code quality metrics
- "risk" → Risk assessment and mitigation
- "dependencies" → Dependency graph analysis
- "team" → Workload and skill distribution
- "architecture" → System design coherence
- Default → Full spectrum analysis

### 2. **Velocity Analytics**

```
📊 Velocity Analysis
━━━━━━━━━━━━━━━━━━━
Current Sprint: 24 points/week ↗️ +20%
Rolling Average: 20 points/week
Efficiency: 85% (17/20 tasks on time)

Bottlenecks Detected:
- Code review delays (avg 4h wait)
- Test environment availability
- Dependency on external team

Recommendations:
1. Implement parallel review process
2. Add staging environment
3. Mock external dependencies
```

### 3. **Risk Assessment**

**Technical Risks**
- High complexity tasks without backup assignee
- Single points of failure in architecture
- Insufficient test coverage in critical paths
- Technical debt accumulation rate

**Project Risks**
- Critical path dependencies
- Resource availability gaps
- Deadline feasibility analysis
- Scope creep indicators

### 4. **Dependency Intelligence**

Visual dependency analysis:
```
Critical Path: 
#12 → #15 → #23 → #45 → #50 (20 days)
         ↘ #24 → #46 ↗

Optimization: Parallelize #15 and #24
Time Saved: 3 days
```

### 5. **Quality Metrics**

**Code Quality**
- Test coverage trends
- Complexity scores
- Technical debt ratio
- Review feedback patterns

**Process Quality**
- Rework frequency
- Bug introduction rate
- Time to resolution
- Knowledge distribution

### 6. **Predictive Insights**

Based on patterns:
- Completion probability by deadline
- Resource needs projection
- Risk materialization likelihood
- Suggested interventions

### 7. **Executive Dashboard**

High-level summary with:
- Health score (0-100)
- Top 3 risks
- Top 3 opportunities
- Recommended actions
- Success probability

Result: Data-driven decisions with clear action paths.