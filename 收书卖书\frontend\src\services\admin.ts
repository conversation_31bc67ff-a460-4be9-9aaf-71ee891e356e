import api from './api';
import { ApiResponse, PaginationResponse, User, Order, AdminStats } from '../types';

export const adminService = {
  // 获取系统统计信息
  async getStats(): Promise<ApiResponse<AdminStats>> {
    const response = await api.get('/admin/stats');
    return response.data;
  },

  // 获取用户列表
  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    role?: string;
    start_date?: string;
    end_date?: string;
  } = {}): Promise<PaginationResponse<User>> {
    const response = await api.get('/admin/users', { params });
    return response.data;
  },

  // 更新用户状态
  async updateUserStatus(userId: string, status: string): Promise<ApiResponse<User>> {
    const response = await api.patch(`/admin/users/${userId}/status`, { status });
    return response.data;
  },

  // 获取订单列表
  async getOrders(params: {
    page?: number;
    limit?: number;
    status?: string;
    payment_status?: string;
    start_date?: string;
    end_date?: string;
    search?: string;
  } = {}): Promise<PaginationResponse<Order>> {
    const response = await api.get('/admin/orders', { params });
    return response.data;
  },

  // 更新订单状态
  async updateOrderStatus(orderId: string, data: {
    status: string;
    delivery_person?: string;
    delivery_person_phone?: string;
  }): Promise<ApiResponse<Order>> {
    const response = await api.patch(`/admin/orders/${orderId}/status`, data);
    return response.data;
  },

  // 创建管理员账户（仅超级管理员）
  async createUser(data: {
    username?: string;
    email?: string;
    phone: string;
    password: string;
    role?: 'admin' | 'super_admin';
  }): Promise<ApiResponse<User>> {
    const response = await api.post('/admin/users', data);
    return response.data;
  }
};
