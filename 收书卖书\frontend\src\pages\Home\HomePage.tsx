import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Carousel,
  Typography,
  Space,
  Tag,
  Statistic,
  List,
  Avatar,
  Rate,
  Divider,
  Skeleton
} from 'antd';
import {
  BookOutlined,
  UserOutlined,
  ShoppingOutlined,
  FireOutlined,
  StarOutlined,
  RightOutlined,
  GiftOutlined,
  TrophyOutlined,
  <PERSON>boltOutlined,
  HeartOutlined,
  SafetyOutlined,
  CustomerServiceOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import EnhancedBookCard from '../../components/business/EnhancedBookCard';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Loading from '../../components/ui/Loading';
import { theme } from '../../styles/theme';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';
import api from '../../services/api';

const { Title, Text, Paragraph } = Typography;

const HomeContainer = styled.div`
  min-height: 100vh;
  background: ${theme.colors.gradients.cool};

  .hero-section {
    background: ${theme.colors.gradients.primary};
    padding: 100px 0 120px 0;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 100px;
      background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
    }
    
    .hero-content {
      position: relative;
      z-index: 3;
      text-align: center;
      max-width: 900px;
      margin: 0 auto;
      padding: 0 ${theme.spacing.lg};

      .hero-title {
        font-size: ${theme.typography.fontSize['4xl']};
        font-weight: ${theme.typography.fontWeight.black};
        margin-bottom: ${theme.spacing[6]};
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        line-height: ${theme.typography.lineHeight['4xl']};
        letter-spacing: -0.02em;

        @media (max-width: 768px) {
          font-size: ${theme.typography.fontSize['4xl']};
        }

        @media (max-width: 480px) {
          font-size: ${theme.typography.fontSize['3xl']};
        }
      }

      .hero-subtitle {
        font-size: ${theme.typography.fontSize['2xl']};
        margin-bottom: ${theme.spacing[10]};
        opacity: 0.95;
        font-weight: ${theme.typography.fontWeight.medium};
        line-height: ${theme.typography.lineHeight['2xl']};

        @media (max-width: 768px) {
          font-size: ${theme.typography.fontSize.xl};
        }

        @media (max-width: 480px) {
          font-size: ${theme.typography.fontSize.lg};
        }
      }
      
      .hero-stats {
        display: flex;
        justify-content: center;
        gap: 48px;
        margin-top: 48px;
        
        @media (max-width: 768px) {
          gap: 24px;
          flex-wrap: wrap;
        }
        
        .stat-item {
          text-align: center;
          
          .stat-number {
            font-size: 32px;
            font-weight: 700;
            display: block;
            margin-bottom: 8px;
          }
          
          .stat-label {
            font-size: 14px;
            opacity: 0.8;
          }
        }
      }
    }
  }
  
  .carousel-section {
    margin: -60px 0 ${theme.spacing[16]} 0;
    position: relative;
    z-index: 4;

    .carousel-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 ${theme.spacing[6]};

      .ant-carousel {
        border-radius: ${theme.borderRadius.xl};
        overflow: hidden;
        box-shadow: ${theme.boxShadow.xl};

        .carousel-item {
          height: 320px;
          display: flex !important;
          align-items: center;
          justify-content: center;
          position: relative;
          background: ${theme.colors.gradients.sunset};

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
          }
          
          &.item-1 {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          }
          
          &.item-2 {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          }
          
          &.item-3 {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
          }
          
          .carousel-content {
            text-align: center;
            color: white;
            
            .carousel-title {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 16px;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            
            .carousel-desc {
              font-size: 16px;
              margin-bottom: 24px;
              opacity: 0.9;
            }
            
            .carousel-btn {
              background: rgba(255, 255, 255, 0.2);
              border: 2px solid white;
              color: white;
              border-radius: 24px;
              height: 48px;
              padding: 0 32px;
              font-weight: 600;
              backdrop-filter: blur(10px);
              
              &:hover {
                background: white;
                color: #667eea;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
              }
            }
          }
        }
      }
    }
  }
  
  .categories-section {
    padding: ${theme.spacing[20]} 0;
    background: white;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, ${theme.colors.primary[200]}, transparent);
    }

    .section-header {
      text-align: center;
      margin-bottom: ${theme.spacing[16]};

      .section-title {
        font-size: ${theme.typography.fontSize['4xl']};
        font-weight: ${theme.typography.fontWeight.black};
        margin-bottom: ${theme.spacing[4]};
        background: ${theme.colors.gradients.primary};
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 4px;
          background: ${theme.colors.gradients.primary};
          border-radius: ${theme.borderRadius.full};
        }
      }

      .section-subtitle {
        font-size: ${theme.typography.fontSize.lg};
        color: ${theme.colors.gray[600]};
        font-weight: ${theme.typography.fontWeight.medium};
      }
    }
    
    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: ${theme.spacing[6]};
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 ${theme.spacing[6]};

      .category-card {
        text-align: center;
        padding: ${theme.spacing[10]} ${theme.spacing[6]};
        border-radius: ${theme.borderRadius.lg};
        background: white;
        border: 1px solid ${theme.colors.gray[200]};
        transition: all ${theme.animation.duration.base} ${theme.animation.easing.easeOut};
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: ${theme.colors.gradients.primary};
          opacity: 0;
          transition: opacity ${theme.animation.duration.base};
        }

        &:hover {
          transform: translateY(-12px) scale(1.02);
          box-shadow: ${theme.boxShadow.xl};
          border-color: ${theme.colors.primary[400]};

          &::before {
            opacity: 0.05;
          }

          .category-icon {
            transform: scale(1.1);
            filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
          }
        }

        .category-icon {
          font-size: ${theme.typography.fontSize['4xl']};
          margin-bottom: ${theme.spacing[4]};
          background: ${theme.colors.gradients.primary};
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          transition: all ${theme.animation.duration.base};
          position: relative;
          z-index: 2;
        }

        .category-name {
          font-size: ${theme.typography.fontSize.xl};
          font-weight: ${theme.typography.fontWeight.bold};
          margin-bottom: ${theme.spacing[2]};
          color: ${theme.colors.gray[800]};
          position: relative;
          z-index: 2;
        }

        .category-count {
          color: ${theme.colors.gray[500]};
          font-size: ${theme.typography.fontSize.sm};
          font-weight: ${theme.typography.fontWeight.medium};
          position: relative;
          z-index: 2;
        }
      }
    }
  }
  
  .recommendations-section {
    padding: ${theme.spacing[20]} 0;
    background: linear-gradient(135deg, #fafafa 0%, #f0f2f5 100%);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 10% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
                  radial-gradient(circle at 90% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    }

    .recommendations-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 ${theme.spacing[6]};
      position: relative;
      z-index: 2;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: ${theme.spacing[10]};

      @media (max-width: 768px) {
        flex-direction: column;
        gap: ${theme.spacing[4]};
        text-align: center;
      }

      .header-left {
        .section-title {
          font-size: ${theme.typography.fontSize['3xl']};
          font-weight: ${theme.typography.fontWeight.black};
          margin-bottom: ${theme.spacing[2]};
          color: ${theme.colors.gray[800]};
          display: flex;
          align-items: center;
          gap: ${theme.spacing[3]};

          .section-icon {
            font-size: ${theme.typography.fontSize['2xl']};
          }
        }

        .section-subtitle {
          color: ${theme.colors.gray[600]};
          font-size: ${theme.typography.fontSize.base};
          font-weight: ${theme.typography.fontWeight.medium};
        }
      }

      .header-right {
        .view-more-btn {
          border-radius: ${theme.borderRadius.full};
          font-weight: ${theme.typography.fontWeight.semibold};
          transition: all ${theme.animation.duration.base};

          &:hover {
            transform: translateX(4px);
            box-shadow: ${theme.boxShadow.md};
          }
        }
      }
    }
    
    .books-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 24px;
    }
  }
  
  .features-section {
    padding: ${theme.spacing[24]} 0;
    background: ${theme.colors.gradients.primary};
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                  radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    }

    .features-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 ${theme.spacing[6]};
      text-align: center;
      position: relative;
      z-index: 2;

      .section-title {
        font-size: ${theme.typography.fontSize['4xl']};
        font-weight: ${theme.typography.fontWeight.black};
        margin-bottom: ${theme.spacing[16]};
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -12px;
          left: 50%;
          transform: translateX(-50%);
          width: 80px;
          height: 4px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: ${theme.borderRadius.full};
        }
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: ${theme.spacing[8]};

        .feature-item {
          padding: ${theme.spacing[10]} ${theme.spacing[6]};
          background: rgba(255, 255, 255, 0.1);
          border-radius: ${theme.borderRadius.xl};
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all ${theme.animation.duration.base};
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity ${theme.animation.duration.base};
          }

          &:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

            &::before {
              opacity: 1;
            }

            .feature-icon {
              transform: scale(1.1);
            }
          }

          .feature-icon {
            font-size: ${theme.typography.fontSize['4xl']};
            margin-bottom: ${theme.spacing[4]};
            color: #ffd700;
            transition: transform ${theme.animation.duration.base};
            filter: drop-shadow(0 4px 8px rgba(255, 215, 0, 0.3));
          }

          .feature-title {
            font-size: ${theme.typography.fontSize.xl};
            font-weight: ${theme.typography.fontWeight.bold};
            margin-bottom: ${theme.spacing[3]};
          }

          .feature-desc {
            opacity: 0.95;
            line-height: ${theme.typography.lineHeight.lg};
            font-size: ${theme.typography.fontSize.base};
          }
        }
      }
    }
  }
`;

interface HomePageProps {}

const HomePage: React.FC<HomePageProps> = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState<any[]>([]);
  const [recommendedBooks, setRecommendedBooks] = useState<any[]>([]);
  const [popularBooks, setPopularBooks] = useState<any[]>([]);
  const [newBooks, setNewBooks] = useState<any[]>([]);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setLoading(true);
      
      const [categoriesRes, recommendedRes, popularRes, newRes] = await Promise.all([
        categoriesService.getCategories(),
        api.get('/recommendations/mixed', { params: { limit: 8 } }),
        booksService.getBooks({ sort_by: 'sales_count', sort_order: 'DESC', limit: 8 }),
        booksService.getBooks({ sort_by: 'created_at', sort_order: 'DESC', limit: 8 })
      ]);

      if (categoriesRes.success && categoriesRes.data) setCategories(categoriesRes.data.slice(0, 8));
      if (recommendedRes.data.success) setRecommendedBooks(recommendedRes.data.data);
      if (popularRes.success) setPopularBooks(popularRes.data.books);
      if (newRes.success) setNewBooks(newRes.data.books);
    } catch (error) {
      console.error('加载首页数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const carouselItems = [
    {
      title: '发现好书',
      description: '海量优质二手图书，让知识传递更有价值',
      buttonText: '立即浏览',
      action: () => navigate('/books'),
      className: 'item-1'
    },
    {
      title: '智能推荐',
      description: 'AI算法为您推荐最适合的图书',
      buttonText: '查看推荐',
      action: () => navigate('/recommendations'),
      className: 'item-2'
    },
    {
      title: '优惠促销',
      description: '限时特价，精选图书低至5折',
      buttonText: '抢购优惠',
      action: () => navigate('/promotions'),
      className: 'item-3'
    }
  ];

  const features = [
    {
      icon: <BookOutlined />,
      title: '海量图书',
      description: '涵盖各个领域的优质二手图书，满足不同读者需求'
    },
    {
      icon: <SafetyOutlined />,
      title: '品质保证',
      description: '严格的图书质量检测，确保每本书都物有所值'
    },
    {
      icon: <ThunderboltOutlined />,
      title: '快速配送',
      description: '高效的物流配送，让您快速收到心仪的图书'
    },
    {
      icon: <CustomerServiceOutlined />,
      title: '贴心服务',
      description: '专业的客服团队，为您提供全程贴心服务'
    }
  ];

  return (
    <HomeContainer>
      {/* 英雄区域 */}
      <section className="hero-section">
        <div className="hero-content">
          <h1 className="hero-title">收书卖书</h1>
          <p className="hero-subtitle">让知识流转，让阅读更有价值</p>
          <Space size="large">
            <Button
              variant="gradient"
              gradient="primary"
              size="large"
              onClick={() => navigate('/books')}
              rounded
              elevated
            >
              开始浏览
            </Button>
            <Button
              variant="ghost"
              size="large"
              onClick={() => navigate('/sell')}
              rounded
              style={{
                borderColor: 'white',
                color: 'white'
              }}
            >
              出售图书
            </Button>
          </Space>
          
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">10,000+</span>
              <span className="stat-label">在售图书</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">5,000+</span>
              <span className="stat-label">注册用户</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">50,000+</span>
              <span className="stat-label">成功交易</span>
            </div>
          </div>
        </div>
      </section>

      {/* 轮播图 */}
      <section className="carousel-section">
        <div className="carousel-container">
          <Carousel autoplay effect="fade">
            {carouselItems.map((item, index) => (
              <div key={index}>
                <div className={`carousel-item ${item.className}`}>
                  <div className="carousel-content">
                    <h2 className="carousel-title">{item.title}</h2>
                    <p className="carousel-desc">{item.description}</p>
                    <Button
                      variant="gradient"
                      gradient="primary"
                      size="large"
                      onClick={item.action}
                      rounded
                      elevated
                    >
                      {item.buttonText}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </Carousel>
        </div>
      </section>

      {/* 分类导航 */}
      <section className="categories-section">
        <div className="section-header">
          <h2 className="section-title">图书分类</h2>
          <p className="section-subtitle">按分类浏览，快速找到您感兴趣的图书</p>
        </div>
        
        <div className="categories-grid">
          {loading ? (
            Array.from({ length: 8 }).map((_, index) => (
              <Card key={index} variant="elevated" className="category-card">
                <Loading variant="skeleton" size="medium" />
              </Card>
            ))
          ) : (
            categories.map(category => (
              <div
                key={category.id}
                className="category-card"
                onClick={() => navigate(`/books?category=${category.id}`)}
              >
                <div className="category-icon">
                  <BookOutlined />
                </div>
                <div className="category-name">{category.name}</div>
                <div className="category-count">{category.book_count || 0} 本图书</div>
              </div>
            ))
          )}
        </div>
      </section>

      {/* 推荐图书 */}
      <section className="recommendations-section">
        <div className="recommendations-container">
          <div className="section-header">
            <div className="header-left">
              <h2 className="section-title">
                <FireOutlined style={{ marginRight: 8, color: '#ff4d4f' }} />
                为您推荐
              </h2>
              <p className="section-subtitle">基于您的浏览历史和偏好推荐</p>
            </div>
            <div className="header-right">
              <Button
                variant="ghost"
                onClick={() => navigate('/recommendations')}
                rounded
              >
                查看更多 <RightOutlined />
              </Button>
            </div>
          </div>
          
          <div className="books-grid">
            {loading ? (
              Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} variant="elevated">
                  <Loading variant="skeleton" size="large" />
                </Card>
              ))
            ) : (
              recommendedBooks.map(book => (
                <EnhancedBookCard key={book.id} book={book} />
              ))
            )}
          </div>
        </div>
      </section>

      {/* 热销图书 */}
      <section className="recommendations-section" style={{ background: 'white' }}>
        <div className="recommendations-container">
          <div className="section-header">
            <div className="header-left">
              <h2 className="section-title">
                <TrophyOutlined style={{ marginRight: 8, color: '#ffa940' }} />
                热销榜单
              </h2>
              <p className="section-subtitle">最受欢迎的图书推荐</p>
            </div>
            <div className="header-right">
              <Button 
                className="view-more-btn"
                onClick={() => navigate('/books?sort=sales_count_DESC')}
              >
                查看更多 <RightOutlined />
              </Button>
            </div>
          </div>
          
          <div className="books-grid">
            {loading ? (
              Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} variant="elevated">
                  <Loading variant="skeleton" size="large" />
                </Card>
              ))
            ) : (
              popularBooks.map(book => (
                <EnhancedBookCard key={book.id} book={book} />
              ))
            )}
          </div>
        </div>
      </section>

      {/* 新书上架 */}
      <section className="recommendations-section">
        <div className="recommendations-container">
          <div className="section-header">
            <div className="header-left">
              <h2 className="section-title">
                <StarOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                新书上架
              </h2>
              <p className="section-subtitle">最新上架的优质图书</p>
            </div>
            <div className="header-right">
              <Button 
                className="view-more-btn"
                onClick={() => navigate('/books?sort=created_at_DESC')}
              >
                查看更多 <RightOutlined />
              </Button>
            </div>
          </div>
          
          <div className="books-grid">
            {loading ? (
              Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} variant="elevated">
                  <Loading variant="skeleton" size="large" />
                </Card>
              ))
            ) : (
              newBooks.map(book => (
                <EnhancedBookCard key={book.id} book={book} />
              ))
            )}
          </div>
        </div>
      </section>

      {/* 特色功能 */}
      <section className="features-section">
        <div className="features-container">
          <h2 className="section-title">为什么选择我们</h2>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-item">
                <div className="feature-icon">{feature.icon}</div>
                <h3 className="feature-title">{feature.title}</h3>
                <p className="feature-desc">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </HomeContainer>
  );
};

export default HomePage;
