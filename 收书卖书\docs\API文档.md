# 收书卖书平台 API 文档

## 概述

收书卖书平台提供完整的RESTful API，支持用户认证、图书管理、订单处理、评论系统等功能。

### 基础信息

- **Base URL**: `http://localhost:3001/api`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "pagination": {
    "current_page": 1,
    "total_pages": 10,
    "total_items": 100,
    "items_per_page": 10
  }
}
```

### 错误响应格式

```json
{
  "success": false,
  "message": "错误信息",
  "error_code": "ERROR_CODE"
}
```

## 认证相关 API

### 用户注册

**POST** `/auth/register`

**请求参数:**
```json
{
  "phone": "13800000000",
  "password": "password123",
  "username": "用户名",
  "email": "<EMAIL>"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "username": "用户名",
      "phone": "13800000000",
      "email": "<EMAIL>"
    },
    "token": "jwt_token"
  }
}
```

### 用户登录

**POST** `/auth/login`

**请求参数:**
```json
{
  "phone": "13800000000",
  "password": "password123"
}
```

### 刷新Token

**POST** `/auth/refresh`

**Headers:**
```
Authorization: Bearer <token>
```

### 退出登录

**POST** `/auth/logout`

**Headers:**
```
Authorization: Bearer <token>
```

## 图书相关 API

### 获取图书列表

**GET** `/books`

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 分类ID
- `keyword`: 搜索关键词
- `author`: 作者
- `min_price`: 最低价格
- `max_price`: 最高价格
- `condition`: 图书状况
- `sort`: 排序方式 (price_ASC, price_DESC, created_at_DESC, views_DESC, sales_count_DESC)

**响应:**
```json
{
  "success": true,
  "data": {
    "books": [
      {
        "id": "uuid",
        "title": "图书标题",
        "author": "作者",
        "publisher": "出版社",
        "isbn": "9787111111111",
        "category_id": "uuid",
        "description": "图书描述",
        "price": 29.90,
        "original_price": 39.90,
        "stock": 10,
        "condition": "九成新",
        "status": "上架",
        "cover_image": "图片URL",
        "images": ["图片URL1", "图片URL2"],
        "views": 100,
        "sales_count": 5,
        "created_at": "2024-01-01T00:00:00Z",
        "category": {
          "id": "uuid",
          "name": "分类名称"
        },
        "creator": {
          "id": "uuid",
          "username": "用户名"
        }
      }
    ]
  },
  "pagination": {
    "current_page": 1,
    "total_pages": 10,
    "total_items": 200,
    "items_per_page": 20
  }
}
```

### 获取图书详情

**GET** `/books/:id`

### 创建图书

**POST** `/books`

**Headers:**
```
Authorization: Bearer <token>
```

**请求参数:**
```json
{
  "title": "图书标题",
  "author": "作者",
  "publisher": "出版社",
  "isbn": "9787111111111",
  "category_id": "uuid",
  "description": "图书描述",
  "price": 29.90,
  "original_price": 39.90,
  "stock": 10,
  "condition": "九成新",
  "cover_image": "图片URL",
  "images": ["图片URL1", "图片URL2"]
}
```

### 更新图书

**PUT** `/books/:id`

### 删除图书

**DELETE** `/books/:id`

### 搜索图书

**GET** `/books/search`

**查询参数:**
- `keyword`: 搜索关键词
- `page`: 页码
- `limit`: 每页数量

## 订单相关 API

### 获取订单列表

**GET** `/orders`

**Headers:**
```
Authorization: Bearer <token>
```

### 创建订单

**POST** `/orders`

**请求参数:**
```json
{
  "items": [
    {
      "book_id": "uuid",
      "quantity": 1,
      "price": 29.90
    }
  ],
  "delivery_address": "收货地址",
  "delivery_phone": "收货电话",
  "delivery_method": "platform",
  "notes": "备注"
}
```

### 获取订单详情

**GET** `/orders/:id`

### 更新订单状态

**PUT** `/orders/:id/status`

**请求参数:**
```json
{
  "status": "paid"
}
```

### 取消订单

**DELETE** `/orders/:id`

## 购物车相关 API

### 获取购物车

**GET** `/cart`

### 添加到购物车

**POST** `/cart/items`

**请求参数:**
```json
{
  "book_id": "uuid",
  "quantity": 1
}
```

### 更新购物车商品

**PUT** `/cart/items/:id`

### 删除购物车商品

**DELETE** `/cart/items/:id`

### 清空购物车

**DELETE** `/cart`

## 收藏相关 API

### 获取收藏列表

**GET** `/favorites`

### 添加收藏

**POST** `/favorites`

**请求参数:**
```json
{
  "book_id": "uuid"
}
```

### 取消收藏

**DELETE** `/favorites/:book_id`

### 检查收藏状态

**GET** `/favorites/check/:book_id`

## 评论相关 API

### 获取图书评论

**GET** `/reviews/book/:bookId`

**查询参数:**
- `page`: 页码
- `limit`: 每页数量
- `sort`: 排序方式
- `rating`: 评分筛选
- `has_images`: 是否有图片

### 创建评论

**POST** `/reviews`

**请求参数:**
```json
{
  "book_id": "uuid",
  "rating": 5,
  "title": "评论标题",
  "content": "评论内容",
  "images": ["图片URL"],
  "is_anonymous": false
}
```

### 点赞评论

**POST** `/reviews/:reviewId/helpful`

### 获取评论统计

**GET** `/reviews/book/:bookId/stats`

## 推荐相关 API

### 获取个性化推荐

**GET** `/recommendations/personalized`

**Headers:**
```
Authorization: Bearer <token>
```

### 获取热门推荐

**GET** `/recommendations/popular`

### 获取新书推荐

**GET** `/recommendations/new`

### 获取相似图书

**GET** `/recommendations/similar/:bookId`

### 获取综合推荐

**GET** `/recommendations/mixed`

## 分类相关 API

### 获取分类列表

**GET** `/categories`

### 创建分类

**POST** `/categories`

### 更新分类

**PUT** `/categories/:id`

### 删除分类

**DELETE** `/categories/:id`

## 用户相关 API

### 获取用户信息

**GET** `/users/profile`

### 更新用户信息

**PUT** `/users/profile`

### 修改密码

**PUT** `/users/password`

### 上传头像

**POST** `/users/avatar`

## 文件上传 API

### 上传图片

**POST** `/upload/image`

**请求格式:** `multipart/form-data`

**响应:**
```json
{
  "success": true,
  "data": {
    "url": "图片URL",
    "filename": "文件名",
    "size": 1024
  }
}
```

## 健康检查 API

### 基础健康检查

**GET** `/health`

### 详细健康检查

**GET** `/health/detailed`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未认证或Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 状态码说明

### 图书状态
- `上架`: 正常销售
- `下架`: 暂停销售
- `缺货`: 库存为0
- `预售`: 预售状态

### 订单状态
- `pending`: 待支付
- `paid`: 已支付
- `delivering`: 配送中
- `delivered`: 已送达
- `cancelled`: 已取消
- `returned`: 已退货

### 图书状况
- `全新`: 全新未使用
- `九成新`: 轻微使用痕迹
- `八成新`: 明显使用痕迹
- `七成新`: 较多使用痕迹
- `六成新`: 大量使用痕迹

## 限流说明

- 普通用户: 100 请求/分钟
- 认证用户: 200 请求/分钟
- VIP用户: 500 请求/分钟

## 版本说明

当前API版本: v1.0

版本更新时会在响应头中包含版本信息：
```
API-Version: 1.0
```
