import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { PlusOutlined, BookOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const AdminBooks: React.FC = () => {
  return (
    <div>
      <Title level={2}>图书管理</Title>
      
      <Card>
        <div style={{ textAlign: 'center', padding: '64px 0' }}>
          <BookOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={3}>图书管理功能</Title>
          <Paragraph type="secondary">
            这里可以管理平台上的所有图书，包括添加新书、编辑图书信息、管理库存等功能。
          </Paragraph>
          <Space>
            <Button type="primary" icon={<PlusOutlined />}>
              添加图书
            </Button>
            <Button>
              批量导入
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default AdminBooks;
