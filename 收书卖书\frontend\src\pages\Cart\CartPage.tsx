import React from 'react';
import { Breadcrumb, Typography } from 'antd';
import { HomeOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import EnhancedCart from '../../components/business/EnhancedCart';
import { theme } from '../../styles/theme';

const { Title } = Typography;

const CartPageContainer = styled.div`
  min-height: 100vh;
  background: ${theme.colors.gradients.cool};

  .page-header {
    background: white;
    padding: ${theme.spacing[6]} 0;
    border-bottom: 1px solid ${theme.colors.gray[200]};
    box-shadow: ${theme.boxShadow.sm};

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 ${theme.spacing[6]};

      .breadcrumb {
        margin-bottom: ${theme.spacing[2]};

        .ant-breadcrumb-link {
          color: ${theme.colors.gray[600]};
          transition: color ${theme.animation.duration.fast};

          &:hover {
            color: ${theme.colors.primary[500]};
          }
        }
      }

      .page-title {
        margin: 0;
        font-size: ${theme.typography.fontSize['3xl']};
        font-weight: ${theme.typography.fontWeight.black};
        display: flex;
        align-items: center;
        gap: ${theme.spacing[3]};
        background: ${theme.colors.gradients.primary};
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        .cart-icon {
          color: ${theme.colors.primary[500]};
          font-size: ${theme.typography.fontSize['2xl']};
          filter: drop-shadow(0 2px 4px rgba(22, 119, 255, 0.3));
        }
      }
    }
  }
`;

interface CartPageProps {}

const CartPage: React.FC<CartPageProps> = () => {
  const navigate = useNavigate();

  return (
    <CartPageContainer>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <HomeOutlined />
              <span onClick={() => navigate('/')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                首页
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <ShoppingCartOutlined />
              购物车
            </Breadcrumb.Item>
          </Breadcrumb>
          
          <Title level={2} className="page-title">
            <ShoppingCartOutlined className="cart-icon" />
            我的购物车
          </Title>
        </div>
      </div>

      {/* 购物车内容 */}
      <EnhancedCart />
    </CartPageContainer>
  );
};

export default CartPage;
