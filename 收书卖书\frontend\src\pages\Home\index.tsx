import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Button,
  Typography,
  Space,
  Carousel,
  Statistic,
  List,
  Avatar,
  Tag
} from 'antd';
import {
  BookOutlined,
  UserOutlined,
  ShoppingOutlined,
  TrophyOutlined,
  RightOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { Book, Category } from '../../types';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';
import BookCard from '../../components/business/BookCard';

const { Title, Paragraph } = Typography;

const HeroSection = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
  margin-bottom: 48px;
  border-radius: 12px;
`;

const StatsSection = styled.div`
  background: white;
  padding: 48px 24px;
  border-radius: 12px;
  margin-bottom: 48px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled(Title)`
  text-align: center;
  margin-bottom: 32px !important;
  
  &::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: #1890ff;
    margin: 16px auto 0;
  }
`;

const CategoryCard = styled(Card)`
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  .ant-card-body {
    padding: 24px;
  }
`;

const Home: React.FC = () => {
  const navigate = useNavigate();
  const [popularBooks, setPopularBooks] = useState<Book[]>([]);
  const [latestBooks, setLatestBooks] = useState<Book[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [popularRes, latestRes, categoriesRes] = await Promise.all([
        booksService.getPopularBooks(8),
        booksService.getLatestBooks(8),
        categoriesService.getCategories()
      ]);

      if (popularRes.success) {
        setPopularBooks(popularRes.data || []);
      }
      if (latestRes.success) {
        setLatestBooks(latestRes.data || []);
      }
      if (categoriesRes.success) {
        setCategories(categoriesRes.data || []);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryClick = (categoryId: string) => {
    navigate(`/books?category_id=${categoryId}`);
  };

  return (
    <div>
      {/* Hero Section */}
      <HeroSection>
        <Title level={1} style={{ color: 'white', marginBottom: 16 }}>
          收书卖书平台
        </Title>
        <Paragraph style={{ fontSize: '18px', color: 'rgba(255,255,255,0.9)', marginBottom: 32 }}>
          专注大学生二手书交易，让知识传递更有价值
        </Paragraph>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            icon={<BookOutlined />}
            onClick={() => navigate('/books')}
          >
            浏览图书
          </Button>
          <Button
            size="large"
            style={{ background: 'rgba(255,255,255,0.2)', borderColor: 'white', color: 'white' }}
            onClick={() => navigate('/register')}
          >
            立即注册
          </Button>
        </Space>
      </HeroSection>

      {/* 统计数据 */}
      <StatsSection>
        <Row gutter={[32, 32]}>
          <Col xs={12} sm={6}>
            <Statistic
              title="在售图书"
              value={1234}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="注册用户"
              value={5678}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="成功交易"
              value={9012}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col xs={12} sm={6}>
            <Statistic
              title="用户满意度"
              value={98.5}
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Col>
        </Row>
      </StatsSection>

      {/* 图书分类 */}
      <Card style={{ marginBottom: 48 }}>
        <SectionTitle level={2}>图书分类</SectionTitle>
        <Row gutter={[16, 16]}>
          {categories.slice(0, 6).map((category) => (
            <Col xs={12} sm={8} md={4} key={category.id}>
              <CategoryCard
                hoverable
                onClick={() => handleCategoryClick(category.id)}
              >
                <BookOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: 16 }} />
                <Title level={4} style={{ marginBottom: 8 }}>
                  {category.name}
                </Title>
                <Paragraph type="secondary" style={{ marginBottom: 0 }}>
                  {category.description}
                </Paragraph>
              </CategoryCard>
            </Col>
          ))}
        </Row>
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button
            type="link"
            icon={<RightOutlined />}
            onClick={() => navigate('/books')}
          >
            查看更多分类
          </Button>
        </div>
      </Card>

      {/* 热门图书 */}
      <Card style={{ marginBottom: 48 }}>
        <SectionTitle level={2}>热门图书</SectionTitle>
        <Row gutter={[16, 16]}>
          {popularBooks.map((book) => (
            <Col xs={12} sm={8} md={6} key={book.id}>
              <BookCard book={book} />
            </Col>
          ))}
        </Row>
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button
            type="link"
            icon={<RightOutlined />}
            onClick={() => navigate('/books?sort_by=sales_count&sort_order=DESC')}
          >
            查看更多热门图书
          </Button>
        </div>
      </Card>

      {/* 最新图书 */}
      <Card>
        <SectionTitle level={2}>最新上架</SectionTitle>
        <Row gutter={[16, 16]}>
          {latestBooks.map((book) => (
            <Col xs={12} sm={8} md={6} key={book.id}>
              <BookCard book={book} />
            </Col>
          ))}
        </Row>
        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button
            type="link"
            icon={<RightOutlined />}
            onClick={() => navigate('/books?sort_by=created_at&sort_order=DESC')}
          >
            查看更多新书
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default Home;
