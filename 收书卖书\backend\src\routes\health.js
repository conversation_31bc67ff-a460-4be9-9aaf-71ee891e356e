const express = require('express');
const { sequelize } = require('../models');
const redis = require('../config/redis');

const router = express.Router();

// 健康检查端点
router.get('/', async (req, res) => {
  try {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {}
    };

    // 检查数据库连接
    try {
      await sequelize.authenticate();
      health.services.database = {
        status: 'ok',
        message: 'Database connection successful'
      };
    } catch (error) {
      health.services.database = {
        status: 'error',
        message: error.message
      };
      health.status = 'error';
    }

    // 检查Redis连接
    try {
      if (redis) {
        await redis.ping();
        health.services.redis = {
          status: 'ok',
          message: 'Redis connection successful'
        };
      } else {
        health.services.redis = {
          status: 'disabled',
          message: 'Redis not configured'
        };
      }
    } catch (error) {
      health.services.redis = {
        status: 'error',
        message: error.message
      };
    }

    // 检查内存使用情况
    const memUsage = process.memoryUsage();
    health.memory = {
      rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
    };

    // 根据服务状态设置HTTP状态码
    const statusCode = health.status === 'ok' ? 200 : 503;
    
    res.status(statusCode).json(health);
  } catch (error) {
    console.error('Health check error:', error);
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      message: error.message
    });
  }
});

// 详细健康检查（包含更多信息）
router.get('/detailed', async (req, res) => {
  try {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      node_version: process.version,
      platform: process.platform,
      arch: process.arch,
      services: {},
      metrics: {}
    };

    // 数据库详细检查
    try {
      const startTime = Date.now();
      await sequelize.authenticate();
      const responseTime = Date.now() - startTime;
      
      // 获取数据库统计信息
      const [results] = await sequelize.query(`
        SELECT 
          (SELECT COUNT(*) FROM users) as user_count,
          (SELECT COUNT(*) FROM books) as book_count,
          (SELECT COUNT(*) FROM orders) as order_count,
          (SELECT COUNT(*) FROM categories) as category_count
      `);
      
      health.services.database = {
        status: 'ok',
        response_time: `${responseTime}ms`,
        statistics: results[0]
      };
    } catch (error) {
      health.services.database = {
        status: 'error',
        message: error.message
      };
      health.status = 'error';
    }

    // Redis详细检查
    try {
      if (redis) {
        const startTime = Date.now();
        await redis.ping();
        const responseTime = Date.now() - startTime;
        
        const info = await redis.info();
        health.services.redis = {
          status: 'ok',
          response_time: `${responseTime}ms`,
          info: info.split('\r\n').slice(0, 10).join('\n') // 只显示前10行信息
        };
      } else {
        health.services.redis = {
          status: 'disabled',
          message: 'Redis not configured'
        };
      }
    } catch (error) {
      health.services.redis = {
        status: 'error',
        message: error.message
      };
    }

    // 系统指标
    const memUsage = process.memoryUsage();
    health.metrics = {
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        usage_percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
      },
      cpu: {
        usage: process.cpuUsage()
      },
      load_average: process.platform !== 'win32' ? require('os').loadavg() : 'N/A (Windows)'
    };

    const statusCode = health.status === 'ok' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    console.error('Detailed health check error:', error);
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      message: error.message
    });
  }
});

module.exports = router;
