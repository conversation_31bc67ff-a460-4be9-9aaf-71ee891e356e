import api from './api';
import { ApiResponse, PaginationResponse, Book } from '../types';

export interface Favorite {
  id: string;
  user_id: string;
  book_id: string;
  book?: Book;
  created_at: string;
}

export interface FavoriteStats {
  total_count: number;
  category_stats: Record<string, number>;
}

export const favoritesService = {
  // 获取收藏列表
  async getFavorites(params: {
    page?: number;
    limit?: number;
    sort?: string;
  } = {}): Promise<PaginationResponse<Favorite>> {
    const response = await api.get('/favorites', { params });
    return response.data;
  },

  // 添加收藏
  async addFavorite(bookId: string): Promise<ApiResponse<Favorite>> {
    const response = await api.post('/favorites', { book_id: bookId });
    return response.data;
  },

  // 取消收藏
  async removeFavorite(bookId: string): Promise<ApiResponse> {
    const response = await api.delete(`/favorites/${bookId}`);
    return response.data;
  },

  // 检查是否已收藏
  async checkFavorite(bookId: string): Promise<ApiResponse<{
    is_favorited: boolean;
    favorite_id: string | null;
  }>> {
    const response = await api.get(`/favorites/check/${bookId}`);
    return response.data;
  },

  // 批量操作收藏
  async batchFavorites(action: 'add' | 'remove', bookIds: string[]): Promise<ApiResponse<{
    added_count?: number;
    removed_count?: number;
    skipped_count?: number;
  }>> {
    const response = await api.post('/favorites/batch', {
      action,
      book_ids: bookIds
    });
    return response.data;
  },

  // 获取收藏统计
  async getFavoriteStats(): Promise<ApiResponse<FavoriteStats>> {
    const response = await api.get('/favorites/stats');
    return response.data;
  }
};
