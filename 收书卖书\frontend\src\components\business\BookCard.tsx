import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Tag, Button, Space, Typography, Image } from 'antd';
import { ShoppingCartOutlined, EyeOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { Book } from '../../types';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';

const { Text, Title } = Typography;
const { Meta } = Card;

const StyledCard = styled(Card)`
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  .ant-card-body {
    padding: 16px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .ant-card-meta {
    flex: 1;
  }
  
  .ant-card-meta-title {
    font-size: 14px;
    line-height: 1.4;
    height: 2.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .ant-card-meta-description {
    color: #666;
    font-size: 12px;
    height: 1.5em;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`;

const BookImage = styled(Image)`
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px;
`;

const PriceSection = styled.div`
  margin: 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Price = styled.span`
  color: #f5222d;
  font-size: 18px;
  font-weight: bold;
`;

const OriginalPrice = styled.span`
  color: #999;
  font-size: 12px;
  text-decoration: line-through;
  margin-left: 8px;
`;

const BookInfo = styled.div`
  margin: 8px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
`;

const ActionSection = styled.div`
  margin-top: 12px;
  display: flex;
  gap: 8px;
`;

interface BookCardProps {
  book: Book;
  showAddToCart?: boolean;
}

const BookCard: React.FC<BookCardProps> = ({ book, showAddToCart = true }) => {
  const navigate = useNavigate();
  const { addItem } = useCartStore();
  const { isAuthenticated } = useAuthStore();

  const handleCardClick = () => {
    navigate(`/books/${book.id}`);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    addItem(book, 1);
  };

  const getConditionColor = (condition: string) => {
    const colorMap: Record<string, string> = {
      '全新': 'green',
      '九成新': 'blue',
      '八成新': 'orange',
      '七成新': 'gold',
      '六成新': 'red'
    };
    return colorMap[condition] || 'default';
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      '上架': 'success',
      '下架': 'default',
      '缺货': 'error',
      '预售': 'warning'
    };
    return colorMap[status] || 'default';
  };

  return (
    <StyledCard
      hoverable
      onClick={handleCardClick}
      cover={
        <BookImage
          src={book.cover_image || '/images/book-placeholder.png'}
          alt={book.title}
          fallback="/images/book-placeholder.png"
          preview={false}
        />
      }
    >
      <Meta
        title={book.title}
        description={book.author}
      />
      
      <PriceSection>
        <div>
          <Price>¥{book.price}</Price>
          {book.original_price && book.original_price > book.price && (
            <OriginalPrice>¥{book.original_price}</OriginalPrice>
          )}
        </div>
        <Space size="small">
          <Tag color={getConditionColor(book.condition)} size="small">
            {book.condition}
          </Tag>
          <Tag color={getStatusColor(book.status)} size="small">
            {book.status}
          </Tag>
        </Space>
      </PriceSection>

      <BookInfo>
        <span>库存: {book.stock}</span>
        <Space size="small">
          <EyeOutlined />
          <span>{book.views}</span>
        </Space>
      </BookInfo>

      {showAddToCart && book.status === '上架' && book.stock > 0 && (
        <ActionSection>
          <Button
            type="primary"
            size="small"
            icon={<ShoppingCartOutlined />}
            onClick={handleAddToCart}
            block
          >
            加入购物车
          </Button>
        </ActionSection>
      )}
    </StyledCard>
  );
};

export default BookCard;
