# Claude Commands 详细解读

## 概述

`.claude/commands` 目录包含了一个完整的任务管理系统（Task Master）的命令结构，这是一个与 Claude Code 集成的智能项目管理工具。该系统通过自然语言处理和 AI 辅助，提供了从项目初始化到任务完成的全生命周期管理。

## 目录结构

```
.claude/commands/tm/
├── help.md                    # 帮助系统
├── tm-main.md                 # 主要命令参考
├── learn.md                   # 交互式学习系统
├── init/                      # 项目初始化
├── models/                    # AI 模型配置
├── parse-prd/                 # PRD 文档解析
├── list/                      # 任务列表管理
├── show/                      # 任务详情显示
├── add-task/                  # 添加任务
├── update/                    # 任务更新
├── set-status/                # 状态管理
├── expand/                    # 任务分解
├── analyze-complexity/        # 复杂度分析
├── complexity-report/         # 复杂度报告
├── next/                      # 下一步推荐
├── status/                    # 项目状态
├── add-dependency/            # 依赖管理
├── remove-dependency/         # 移除依赖
├── validate-dependencies/     # 依赖验证
├── fix-dependencies/          # 依赖修复
├── add-subtask/               # 子任务管理
├── remove-subtask/            # 移除子任务
├── clear-subtasks/            # 清理子任务
├── remove-task/               # 删除任务
├── generate/                  # 生成任务文件
├── sync-readme/               # 同步 README
├── workflows/                 # 工作流自动化
├── utils/                     # 实用工具
└── setup/                     # 安装配置
```

## 核心功能模块

### 1. 项目初始化 (init/)

**init-project.md**
- 功能：智能项目初始化
- 特点：
  - 自动检测现有项目文件
  - 支持 PRD 文件自动解析
  - 智能建议项目名称和配置
  - Git 仓库检测和 AI 提供商验证

**init-project-quick.md**
- 功能：快速初始化，跳过所有确认提示
- 使用场景：快速项目设置，接受所有默认值

### 2. AI 模型配置 (models/)

**view-models.md**
- 功能：显示当前 AI 模型配置
- 信息包括：主要提供商、研究提供商、备用提供商的状态

**setup-models.md**
- 功能：交互式 AI 模型配置
- 支持的提供商：Claude、GPT、Perplexity
- 智能推荐最佳组合配置

### 3. PRD 解析 (parse-prd/)

**parse-prd.md**
- 功能：将产品需求文档转换为结构化任务
- 特点：
  - 智能提取关键需求
  - 自动生成 10-15 个任务
  - 设置逻辑依赖关系
  - 估算复杂度和优先级

**parse-prd-with-research.md**
- 功能：增强版 PRD 解析，使用研究 AI
- 优势：
  - 获取最新最佳实践
  - 深入技术分析
  - 全面覆盖边缘情况
  - 行业标准建议

### 4. 任务管理 (list/, show/, add-task/, update/)

**list-tasks.md**
- 功能：智能任务列表，支持自然语言过滤
- 支持的过滤器：状态、优先级、ID 范围、特殊标记
- 示例：`"pending high"` → 待处理的高优先级任务

**show-task.md**
- 功能：详细任务信息显示
- 包含：核心详情、关系图、时间智能、可视化增强、智能洞察

**add-task.md**
- 功能：智能任务添加，自然语言解析
- 特点：
  - 自动检测优先级和类型
  - 推断依赖关系
  - 建议复杂度
  - 上下文感知增强

**update-task.md**
- 功能：智能任务更新，支持批量操作
- 支持：自然语言处理、字段检测、批量操作、上下文验证

### 5. 状态管理 (set-status/)

提供完整的任务状态生命周期管理：
- **to-pending.md**：重置为待处理
- **to-in-progress.md**：开始工作，包含环境设置
- **to-done.md**：完成任务，包含验证和后续行动
- **to-review.md**：提交审查，生成审查清单
- **to-deferred.md**：延期处理，文档化原因
- **to-cancelled.md**：取消任务，影响分析

### 6. 任务分解 (expand/, analyze-complexity/)

**expand-task.md**
- 功能：将复杂任务分解为子任务
- 过程：任务分析 → 子任务生成 → 智能分解 → 后续处理

**analyze-complexity.md**
- 功能：AI 驱动的复杂度分析
- 评估维度：技术复杂度、时间需求、依赖复杂度、风险因素
- 输出：复杂度评分、扩展建议、风险评估

**complexity-report.md**
- 功能：显示详细的复杂度分析报告
- 包含：执行摘要、详细分析、风险矩阵、行动建议

### 7. 依赖管理

**add-dependency.md** / **remove-dependency.md**
- 功能：管理任务间的依赖关系
- 特点：自然语言解析、循环依赖检测、影响分析

**validate-dependencies.md** / **fix-dependencies.md**
- 功能：依赖关系验证和自动修复
- 检查：循环依赖、缺失依赖、逻辑问题、复杂度警告

### 8. 子任务管理

**add-subtask.md** / **remove-subtask.md** / **clear-subtasks.md**
- 功能：完整的子任务生命周期管理
- 支持：创建、转换、删除、批量清理

### 9. 工作流自动化 (workflows/)

**smart-workflow.md**
- 功能：基于上下文的智能工作流执行
- 特点：学习用户模式、适应工作习惯、智能命令链

**auto-implement-tasks.md**
- 功能：高级自动实现，包含代码生成和测试
- 过程：预实现分析 → 智能实现策略 → 渐进式实现 → 质量保证

**command-pipeline.md**
- 功能：命令管道执行
- 支持：条件执行、迭代处理、并行执行、错误处理

### 10. 实用工具

**analyze-project.md**
- 功能：多维度项目分析
- 分析模式：速度分析、质量指标、风险评估、依赖图分析

**generate-tasks.md**
- 功能：从 tasks.json 生成独立的任务文件
- 用途：AI 代理消费、文档化、归档、共享

**sync-readme.md**
- 功能：将任务导出到格式化的 README.md
- 特点：专业格式化、进度跟踪、可视化元素

### 11. 安装配置 (setup/)

**install-taskmaster.md** / **quick-install-taskmaster.md**
- 功能：Task Master 的安装和配置
- 包含：检测、安装、验证、故障排除

## 系统特色

### 1. 自然语言处理
- 所有命令都支持自然语言输入
- 智能解析用户意图
- 上下文感知的命令执行

### 2. AI 驱动的智能化
- 多 AI 提供商支持
- 智能任务分析和建议
- 自动化工作流程

### 3. 完整的项目生命周期
- 从 PRD 解析到任务完成
- 依赖关系管理
- 进度跟踪和报告

### 4. 高度可定制
- 灵活的配置选项
- 可扩展的命令结构
- 适应不同工作模式

### 5. 集成友好
- Git 工作流集成
- CI/CD 管道支持
- 版本控制友好

## 使用模式

### 典型工作流程
1. **项目初始化**：`/project:tm/init` → `/project:tm/parse-prd`
2. **任务分析**：`/project:tm/analyze-complexity` → `/project:tm/expand/all`
3. **日常工作**：`/project:tm/next` → `/project:tm/set-status/to-in-progress`
4. **完成任务**：`/project:tm/set-status/to-done` → `/project:tm/next`
5. **项目监控**：`/project:tm/status` → `/project:tm/complexity-report`

### 高级功能
- **智能工作流**：`/project:tm/workflows/smart-flow`
- **自动实现**：`/project:tm/workflows/auto-implement`
- **命令管道**：`/project:tm/workflows/pipeline`

这个系统代表了现代项目管理工具的发展方向，将 AI 智能、自然语言处理和传统项目管理最佳实践相结合，为开发者提供了一个强大而直观的项目管理解决方案。

## 详细命令解析

### 帮助和学习系统

#### help.md
这是系统的主要帮助文档，提供了完整的命令导航和使用指南：

**主要功能：**
- 按类别组织的命令列表（设置安装、项目设置、任务生成、任务管理等）
- 自然语言使用示例
- 快速入门指南
- Tab 补全支持

**命令类别：**
- 🚀 设置安装：`/project:tm/setup/install`、`/project:tm/setup/quick-install`
- 📋 项目设置：`/project:tm/init`、`/project:tm/models`
- 🎯 任务生成：`/project:tm/parse-prd`、`/project:tm/generate`
- 📝 任务管理：`/project:tm/list`、`/project:tm/show`、`/project:tm/add-task`
- 🔄 状态管理：`/project:tm/set-status/*`
- 🔍 分析分解：`/project:tm/analyze-complexity`、`/project:tm/expand`
- 🔗 依赖管理：`/project:tm/add-dependency`、`/project:tm/validate-dependencies`
- 🤖 工作流：`/project:tm/workflows/*`

#### learn.md
交互式学习系统，根据用户输入和项目状态提供个性化指导：

**智能建议机制：**
- 基于关键词触发不同学习路径
- 根据项目状态提供上下文相关建议
- 提供从初学者到高级用户的学习路径

**学习路径：**
- **初学者**：init → status → next → complete
- **中级**：expand → sprint-plan → complexity-report → validate-deps
- **高级**：pipeline → smart-flow → 自定义命令

#### tm-main.md
完整的命令参考文档，详细描述了所有可用命令的层次结构和功能。

### 项目初始化详解

#### init-project.md
智能项目初始化的核心功能：

**初始化流程：**
1. **参数解析**：PRD 文件路径、项目名称、自动确认标志
2. **项目设置**：执行 `task-master init`
3. **智能初始化**：
   - 检测现有项目文件
   - 从目录名建议项目名
   - 检查 Git 仓库
   - 验证 AI 提供商配置

**配置选项：**
- `quick`/`-y`：跳过确认
- `<file.md>`：初始化后使用作为 PRD
- `--name=<name>`：设置项目名
- `--description=<desc>`：设置描述

**后续初始化：**
- 显示创建的项目结构
- 验证 AI 模型配置
- 建议下一步操作

#### init-project-quick.md
快速初始化版本，适用于快速原型和演示：

**执行：** `task-master init -y`

**智能默认值：**
- 项目名：当前目录名
- 描述："Task Master Project"
- 模型配置：现有环境变量
- 任务结构：标准格式

### AI 模型配置详解

#### view-models.md
显示当前 AI 模型配置状态：

**信息显示：**
1. **主要提供商**：模型 ID、API 密钥状态、用途
2. **研究提供商**：增强研究模式使用
3. **备用提供商**：主要提供商失败时的备份

**可视化状态示例：**
```
Task Master AI Model Configuration
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Main:     ✅ claude-3-5-sonnet (configured)
Research: ✅ perplexity-sonar (configured)
Fallback: ⚠️  Not configured (optional)
```

#### setup-models.md
交互式模型配置向导：

**设置过程：**
1. **环境检查**：检测现有 API 密钥、显示当前配置
2. **提供商选择**：选择主要、研究、备用提供商
3. **API 密钥配置**：提示缺失密钥、验证格式、测试连接

**智能推荐：**
- 最佳结果：Claude + Perplexity
- 预算考虑：GPT-3.5 + Perplexity
- 最大能力：GPT-4 + Perplexity + Claude 备用

**配置存储选项：**
1. 环境变量（推荐）
2. 项目 `.env` 文件
3. 全局 `.taskmaster/config`

### PRD 解析详解

#### parse-prd.md
核心 PRD 解析功能：

**解析过程：**
1. **文档分析**：提取关键需求、识别技术组件、检测依赖、估算复杂度
2. **任务生成**：创建 10-15 个任务、包含实现/测试/文档任务、设置逻辑依赖
3. **智能增强**：分组相关功能、设置适当优先级、添加验收标准

**选项解析：**
- 文件名后的数字 → `--num-tasks`
- `research` → 使用研究模式
- `comprehensive` → 生成更多任务

#### parse-prd-with-research.md
增强版 PRD 解析，使用研究 AI 提供商：

**研究优势：**
1. **当前最佳实践**：最新框架模式、安全考虑、性能优化
2. **技术深度挖掘**：实现方法、库推荐、架构模式
3. **全面覆盖**：边缘情况、错误处理、监控设置

**使用场景：**
- 新技术领域
- 复杂需求
- 需要合规性
- 最佳实践至关重要

### 任务列表和显示详解

#### list-tasks.md
智能任务列表系统，支持复杂的自然语言查询：

**过滤器支持：**
- **状态关键词**：pending, in-progress, done, review, deferred, cancelled
- **优先级**：high, medium, low 或 priority:high
- **特殊标记**：subtasks, tree, dependencies, blocked
- **ID 范围**：直接数字（如 "1,3,5" 或 "1-5"）
- **复合查询**："pending high" = 待处理且高优先级

**智能组合示例：**
- "pending high" → 待处理的高优先级任务
- "done today" → 今天完成的任务
- "blocked" → 有未满足依赖的任务
- "1-5" → 任务 1 到 5
- "subtasks tree" → 带子任务的层次视图

**增强显示：**
- 按相关标准分组
- 优先显示最重要信息
- 使用视觉指示器快速扫描
- 包含相关指标

#### list-tasks-with-subtasks.md
层次化任务视图：

**显示特点：**
- 父任务清晰指示器
- 子任务适当缩进
- 快速扫描的状态徽章
- 突出显示依赖和阻塞
- 带子任务的任务进度指示器

#### list-tasks-by-status.md
按状态过滤的任务列表：

**状态选项：**
- `pending`：尚未开始
- `in-progress`：正在进行
- `done`：已完成
- `review`：等待审查
- `deferred`：已推迟
- `cancelled`：已取消

**智能洞察：**
- **Pending**：显示推荐开始顺序
- **In-Progress**：显示空闲时间警告
- **Done**：显示新解锁的任务
- **Review**：指示审查持续时间
- **Deferred**：显示重新激活标准
- **Cancelled**：显示影响分析

#### show-task.md
详细任务信息显示系统：

**智能任务选择：**
- 数字 → 显示特定任务及完整上下文
- "current" → 显示活动的进行中任务
- "next" → 显示推荐的下一个任务
- "blocked" → 显示所有阻塞任务及原因
- "critical" → 显示关键路径任务
- 多个 ID → 比较视图

**上下文信息：**
1. **核心详情**：完整任务信息、当前状态历史、测试策略、优先级和复杂度
2. **关系**：依赖关系、被依赖关系、父/子任务层次、相关任务
3. **时间智能**：创建/更新时间戳、当前状态时间、估计 vs 实际时间

**可视化增强示例：**
```
📋 Task #45: Implement User Authentication
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Status: 🟡 in-progress (2 hours)
Priority: 🔴 High | Complexity: 73/100

Dependencies: ✅ #41, ✅ #42, ⏳ #43 (blocked)
Blocks: #46, #47, #52

Progress: ████████░░ 80% complete
```

**智能洞察：**
- **风险评估**：复杂度 vs 剩余时间
- **瓶颈分析**：是否阻塞关键工作
- **推荐**：建议方法或关注点
- **相似任务**：其他人如何完成类似工作

### 任务添加和更新详解

#### add-task.md
智能任务添加系统：

**输入理解：**
- 自然语言 → 结构化任务
- 从关键词检测优先级（urgent, ASAP, important）
- 从上下文推断依赖关系
- 基于描述建议复杂度
- 确定任务类型（feature, bug, refactor, test, docs）

**智能解析示例：**
```
"Add urgent task to fix login bug"
→ Title: Fix login bug
→ Priority: high
→ Type: bug
→ Suggested complexity: medium

"Create task for API documentation after task 23 is done"
→ Title: API documentation
→ Dependencies: [23]
→ Type: documentation
→ Priority: medium
```

**上下文增强：**
- 建议相关现有任务
- 警告潜在冲突
- 推荐依赖关系
- 如果复杂则建议子任务

**验证和创建：**
- 验证依赖关系存在
- 检查重复项
- 确保逻辑顺序
- 验证任务完整性

#### update-task.md
智能任务更新系统：

**自然语言处理示例：**
- "mark 23 as done" → 更新状态为完成
- "increase priority of 45" → 设置优先级为高
- "add dependency on 12 to task 34" → 添加依赖
- "tasks 20-25 need review" → 批量状态更新
- "all API tasks high priority" → 基于模式的更新

**智能字段检测：**
- **状态关键词**：done, complete, start, pause, review
- **优先级变更**：urgent, high, low, deprioritize
- **依赖更新**：depends on, blocks, after
- **分配**：assign to, owner, responsible
- **时间**：estimate, spent, deadline

**批量操作支持：**
```
示例：
- "complete tasks 12, 15, 18"
- "all pending auth tasks to in-progress"
- "increase priority for tasks blocking 45"
- "defer all documentation tasks"
```

**上下文验证：**
- 状态转换有效性
- 依赖关系不创建循环
- 优先级变更合理性
- 批量更新不破坏项目流程

#### update-single-task.md
单个任务精确更新：

**更新类型：**
1. **内容更新**：增强描述、添加需求、澄清细节、更新验收标准
2. **元数据更新**：更改优先级、调整时间估算、更新复杂度、修改依赖
3. **战略更新**：修订方法、更改测试策略、更新实现注释、调整子任务需求

**AI 驱动更新：**
1. **理解上下文**：读取当前任务状态、识别更新意图、保持一致性
2. **应用更改**：更新相关字段、保持风格一致、添加而不删除、增强清晰度
3. **验证结果**：检查连贯性、验证完整性、维护关系、建议相关更新

#### update-tasks-from-id.md
从特定 ID 开始的批量任务更新：

**任务选择：**
- 包含任务本身
- 包含所有依赖任务
- 包含相关子任务
- 智能边界检测

**智能功能：**
1. **范围检测**：找到自然任务分组、识别相关功能、在逻辑边界停止
2. **一致性维护**：保持命名约定、保留关系、更新交叉引用
3. **更改预览**：显示影响范围、预览更改内容、需要确认

### 状态管理详解

#### to-pending.md
将任务重置为待处理状态：

**使用场景：**
- 重置错误开始的任务
- 推迟过早开始的工作
- 重新组织冲刺优先级

**验证检查：**
- 如果任务当前正在进行则警告
- 检查是否会阻塞其他任务
- 建议记录重置原因
- 保留已完成的工作

#### to-in-progress.md
开始任务工作：

**预启动检查：**
1. 验证依赖关系已满足
2. 检查是否有其他任务正在进行
3. 确保任务详情完整
4. 验证测试策略存在

**环境设置：**
- 创建/检出适当的 git 分支
- 打开相关文档
- 设置测试监视器（如适用）
- 显示任务详情和验收标准
- 显示类似已完成任务作为参考

**智能建议：**
- 基于复杂度的估计完成时间
- 来自类似任务的相关文件
- 需要注意的潜在阻塞因素
- 推荐的第一步

#### to-done.md
标记任务完成：

**完成前检查：**
1. 验证测试策略已遵循
2. 检查所有子任务是否完成
3. 验证验收标准已满足
4. 确保代码已提交

**完成后行动：**
1. **更新依赖关系**：识别新解锁的任务、更新冲刺进度、重新计算项目时间线
2. **文档化**：生成完成摘要、用学习更新 CLAUDE.md、记录实现方法
3. **下一步**：显示新可用任务、建议逻辑下一任务、更新速度指标

#### to-review.md
提交任务审查：

**审查准备：**
1. **生成审查清单**：链接到 PR/MR、突出关键更改、注意需要关注的区域
2. **文档化**：用审查注释更新任务、链接相关工件、指定审查者
3. **智能行动**：创建审查提醒、跟踪审查持续时间、基于专业知识建议审查者

#### to-deferred.md
推迟任务：

**有效推迟原因：**
- 等待外部依赖
- 为未来冲刺重新优先级
- 被技术限制阻塞
- 资源约束
- 战略时机考虑

**推迟管理：**
1. **记录原因**：捕获推迟原因、设置重新激活标准、注意任何部分完成的工作
2. **影响分析**：检查依赖任务、更新项目时间线、通知受影响的利益相关者
3. **未来规划**：设置审查提醒、为特定里程碑标记、保留重新激活上下文

#### to-cancelled.md
取消任务：

**有效取消原因：**
- 需求变更
- 功能弃用
- 与其他任务重复
- 战略转向
- 技术方法无效

**取消前检查：**
1. 确认没有关键依赖
2. 检查部分实现
3. 验证取消理由
4. 记录学到的经验

**取消影响：**
1. **依赖更新**：通知依赖任务、更新项目范围、重新计算时间线
2. **清理行动**：删除相关分支、归档任何完成的工作、更新文档
3. **学习捕获**：记录取消原因、注意学到的内容、更新估算模型

### 任务分解和复杂度分析详解

#### expand-task.md
智能任务分解：

**分解过程：**
1. **任务分析**：审查任务复杂度、识别组件、检测技术挑战、估算时间需求
2. **子任务生成**：通常创建 3-7 个子任务、每个子任务 1-4 小时、逻辑实现顺序、清晰验收标准
3. **智能分解**：设置/配置任务、核心实现、测试组件、集成步骤、文档更新

**基于任务类型的增强功能：**
- **功能**：设置 → 实现 → 测试 → 集成
- **错误修复**：重现 → 诊断 → 修复 → 验证
- **重构**：分析 → 计划 → 重构 → 验证

#### expand-all-tasks.md
批量任务分解：

**智能选择标准：**
- 标记为待处理
- 高复杂度（>5）
- 缺乏现有子任务
- 将从分解中受益

**分解过程：**
1. **分析阶段**：识别分解候选、分组相关任务、计划分解策略
2. **批处理**：按逻辑顺序分解任务、保持一致性、保留关系、优化并行性
3. **质量控制**：确保子任务质量、避免过度分解、保持任务连贯性、更新依赖

#### analyze-complexity.md
AI 驱动的复杂度分析：

**分析参数：**
- `--research` → 使用研究 AI 进行更深入分析
- `--threshold=5` → 仅标记复杂度超过 5 的任务
- 默认：分析所有待处理任务

**分析过程：**
1. **任务评估**：技术复杂度、时间需求、依赖复杂度、风险因素、知识需求
2. **复杂度评分**：基于实现难度、集成挑战、测试需求、未知因素、技术债务风险分配 1-10 分
3. **推荐**：建议分解方法、推荐子任务分解、识别风险区域、提出缓解策略

**智能分析功能：**
1. **模式识别**：类似任务比较、历史复杂度准确性、团队速度考虑、技术栈因素
2. **上下文因素**：团队专业知识、可用资源、时间线约束、业务关键性
3. **风险评估**：技术风险、时间线风险、依赖风险、知识差距

**输出格式示例：**
```
Task Complexity Analysis Report
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

High Complexity Tasks (>7):
📍 #5 "Implement real-time sync" - Score: 9/10
   Factors: WebSocket complexity, state management, conflict resolution
   Recommendation: Expand into 5-7 subtasks
   Risks: Performance, data consistency

Medium Complexity Tasks (5-7):
📍 #23 "Add export functionality" - Score: 6/10
   Consider expansion if timeline tight

Low Complexity Tasks (<5):
✅ 15 tasks - No expansion needed
```

#### complexity-report.md
复杂度分析报告查看器：

**报告内容：**
1. **执行摘要**：分析日期、分析任务数、复杂度分布、关键发现
2. **详细任务分析**：复杂度评分分解、贡献因素、识别的特定风险、分解建议
3. **风险矩阵**：风险 vs 复杂度的可视化表示
4. **推荐**：立即行动、冲刺规划建议

**交互功能：**
1. **快速行动**：按 'e' 分解任务、按 'd' 查看任务详情、按 'r' 刷新分析
2. **过滤**：按复杂度级别查看、按风险因素过滤、仅显示可行项目
3. **导出选项**：Markdown 格式、CSV 用于电子表格、JSON 用于工具

### 依赖管理详解

#### add-dependency.md
添加任务间依赖关系：

**参数解析：**
- "make 5 depend on 3" → 任务 5 依赖任务 3
- "5 needs 3" → 任务 5 依赖任务 3
- "5 3" → 任务 5 依赖任务 3
- "5 after 3" → 任务 5 依赖任务 3

**验证检查：**
1. **验证两个任务都存在**
2. **检查循环依赖**
3. **确保依赖在逻辑上合理**
4. **如果创建复杂链则警告**

**智能功能：**
- 检测依赖是否已存在
- 建议相关依赖
- 显示对任务流程的影响
- 如需要则更新任务优先级

#### remove-dependency.md
移除任务间依赖关系：

**参数解析：**
- "remove dependency between 5 and 3"
- "5 no longer needs 3"
- "unblock 5 from 3"
- "5 3" → 移除 5 对 3 的依赖

**移除前检查：**
1. **验证依赖存在**
2. **检查对任务流程的影响**
3. **如果破坏逻辑序列则警告**
4. **显示将被解锁的内容**

**智能分析：**
- 显示依赖可能存在的原因
- 检查移除是否使任务可执行
- 验证没有关键路径中断
- 建议替代依赖

#### validate-dependencies.md
验证所有任务依赖的问题：

**验证检查：**
1. **循环依赖**：A 依赖 B，B 依赖 A、复杂循环链、自依赖
2. **缺失依赖**：对不存在任务的引用、已删除任务引用、无效任务 ID
3. **逻辑问题**：已完成任务依赖待处理任务、依赖链中的已取消任务、不可能的序列
4. **复杂度警告**：过于复杂的依赖链、每个任务的依赖过多、瓶颈任务

**智能分析：**
- 可视化依赖图
- 关键路径分析
- 瓶颈识别
- 建议优化

**报告格式示例：**
```
Dependency Validation Report
━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ No circular dependencies found
⚠️  2 warnings found:
   - Task #23 has 7 dependencies (consider breaking down)
   - Task #45 blocks 5 other tasks (potential bottleneck)
❌ 1 error found:
   - Task #67 depends on deleted task #66

Critical Path: #1 → #5 → #23 → #45 → #50 (15 days)
```

#### fix-dependencies.md
自动修复依赖问题：

**自动修复的问题：**
- 移除对已删除任务的引用
- 打破简单循环依赖
- 移除自依赖
- 清理重复依赖

**智能解决方案：**
- 重新排序依赖以保持逻辑
- 建议任务合并以处理过度依赖
- 扁平化不必要的依赖链
- 移除冗余的传递依赖

**修复过程：**
1. **分析阶段**：运行验证检查、按类型分类问题、确定修复策略
2. **执行阶段**：应用自动修复、记录所有更改、保留任务关系
3. **验证阶段**：修复后重新验证、显示前后比较、突出需要手动修复的内容

### 子任务管理详解

#### add-subtask.md
添加子任务到父任务：

**参数解析：**
- "add subtask to 5: implement login form"
- "break down 5 with: setup, implement, test"
- "subtask for 5: handle edge cases"
- "5: validate user input" → 向任务 5 添加子任务

**执行模式：**
1. **创建新子任务**：`task-master add-subtask --parent=<id> --title="<title>"`
2. **转换现有任务**：`task-master add-subtask --parent=<id> --task-id=<existing-id>`

**智能功能：**
1. **自动子任务生成**：如果标题包含 "and" 或逗号则创建多个、建议常见子任务模式
2. **智能默认值**：基于父任务的优先级、适当的时间估算、子任务间的逻辑依赖
3. **验证**：检查父任务复杂度、如果子任务过多则警告、确保子任务合理

#### convert-task-to-subtask.md
将现有任务转换为子任务：

**参数解析：**
- "move task 8 under 5"
- "make 8 a subtask of 5"
- "nest 8 in 5"
- "5 8" → 使任务 8 成为任务 5 的子任务

**转换前检查：**
1. **验证**：两个任务都存在且有效、没有循环父关系、任务不是已有子任务
2. **影响分析**：将受影响的依赖、依赖转换任务的任务、需要的优先级对齐

**转换过程：**
1. 将任务 ID 从 "8" 更改为 "5.1"（下一个可用）
2. 更新所有依赖引用
3. 在适当时继承父任务的上下文
4. 如需要则调整优先级
5. 更新时间估算

#### remove-subtask.md
从父任务移除子任务：

**参数解析：**
- "remove subtask 5.1"
- "delete 5.1"
- "convert 5.1 to task" → 移除并转换
- "5.1 standalone" → 转换为独立任务

**执行选项：**
1. **删除子任务**：`task-master remove-subtask --id=<parentId.subtaskId>`
2. **转换为独立**：`task-master remove-subtask --id=<parentId.subtaskId> --convert`

**移除过程：**
- **删除**：确认子任务是否有已完成工作、更新父任务估算、移除子任务及其数据
- **转换**：分配新的独立任务 ID、保留所有任务数据、更新依赖引用、维护任务历史

#### clear-subtasks.md / clear-all-subtasks.md
批量清理子任务：

**clear-subtasks.md** - 清理特定任务的所有子任务：
- 批量移除父任务的所有子任务
- 显示影响摘要和确认
- 选项转换为独立任务或归档

**clear-all-subtasks.md** - 全局清理所有子任务：
- 极其谨慎的操作，需要双重确认
- 创建自动备份
- 要求输入确认短语："CLEAR ALL SUBTASKS"
- 适用于项目重构或重大方法转变

### 工作流自动化详解

#### smart-workflow.md
基于上下文的智能工作流：

**上下文分析：**
- 之前执行的命令
- 当前项目状态
- 上次会话的未完成工作
- 用户的典型模式

**智能执行：**
- 如果上个命令是 `status` → 可能开始工作 → 运行每日站会
- 如果上个命令是 `complete` → 任务完成 → 找下一个任务
- 如果上个命令是 `list pending` → 规划 → 建议冲刺规划
- 如果上个命令是 `expand` → 分解工作 → 显示复杂度分析

**学习模式：**
- 跟踪命令序列
- 注意时间偏好
- 记住常见工作流
- 适应用户风格

#### auto-implement-tasks.md
高级自动实现系统：

**实现前分析：**
- 分析任务复杂度和需求
- 检查代码库模式和约定
- 识别类似已完成任务
- 评估测试覆盖需求
- 检测潜在风险

**智能实现策略：**
- **功能任务**：研究现有模式 → 设计组件架构 → 实现测试 → 系统集成 → 更新文档
- **错误修复任务**：重现问题 → 识别根本原因 → 实现最小修复 → 添加回归测试 → 验证副作用
- **重构任务**：分析当前结构 → 计划增量更改 → 维护测试覆盖 → 逐步重构 → 验证行为不变

**代码智能：**
- **模式识别**：从现有代码学习、遵循团队约定、使用首选库、匹配风格指南
- **测试驱动方法**：尽可能先写测试、确保全面覆盖、包含边缘情况、性能考虑

**渐进式实现：**
```
Step 1/5: Setting up component structure ✓
Step 2/5: Implementing core logic ✓
Step 3/5: Adding error handling ⚡ (in progress)
Step 4/5: Writing tests ⏳
Step 5/5: Integration testing ⏳

Current: Adding try-catch blocks and validation...
```

#### command-pipeline.md
命令管道执行系统：

**管道格式：**
- **简单管道**：`init → expand-all → sprint-plan`
- **条件管道**：`status → if:pending>10 → sprint-plan → else → next`
- **迭代管道**：`for:pending-tasks → expand → complexity-check`

**智能管道模式：**
1. **项目设置管道**：`init [prd] → expand-all → complexity-report → sprint-plan → show first-sprint`
2. **日常工作管道**：`standup → if:in-progress → continue → else → next → start`
3. **任务完成管道**：`complete [id] → git-commit → if:blocked-tasks-freed → show-freed → next`

**管道功能：**
- **变量**：存储结果、在条件中使用、在命令间传递
- **错误处理**：失败时处理、错误时跳过、重试逻辑
- **并行执行**：并行分支、结果合并

### 实用工具详解

#### next-task.md
智能下一步行动推荐：

**上下文收集：**
- 活动任务（进行中）
- 最近完成的任务
- 阻塞的任务
- 上次活动后的时间
- 提供的参数

**智能决策树：**
- **如果有进行中任务**：空闲 > 2 小时？→ 建议恢复或切换、接近完成？→ 显示剩余步骤、被阻塞？→ 找替代任务
- **如果没有进行中任务**：未阻塞的高优先级任务？→ 开始最高优先级、复杂任务需要分解？→ 建议扩展、所有任务都被阻塞？→ 显示依赖解决方案

**特殊参数处理：**
- "quick" → 找 < 2 小时的任务
- "easy" → 找低复杂度任务
- "important" → 找高优先级（不考虑复杂度）
- "continue" → 恢复上次工作的任务

**准备工作流：**
1. 显示完整上下文和历史
2. 设置开发环境
3. 运行相关测试
4. 打开相关文件
5. 显示类似已完成任务
6. 估算完成时间

#### status/project-status.md
增强的项目状态概览：

**执行摘要：**
- 🏃 活动工作（进行中任务）
- 📊 进度指标（完成百分比、速度）
- 🚧 阻塞因素和风险
- ⏱️ 时间分析（估计 vs 实际）
- 🎯 冲刺/里程碑进度

**上下文分析：**
- "sprint" → 当前冲刺进度和燃尽图
- "blocked" → 依赖链和解决路径
- "team" → 任务分配和工作负载
- "timeline" → 进度遵守和预测
- "risk" → 高复杂度或逾期项目

**智能洞察：**
- **工作流健康**：空闲任务、瓶颈、快速胜利
- **预测分析**：基于速度的完成预测、错过截止日期的风险、最优任务顺序建议

**可视化智能示例：**
```
Sprint Progress: ████████░░ 80% (16/20 tasks)
Velocity Trend: ↗️ +15% this week
Blocked Tasks:  🔴 3 critical path items

Priority Distribution:
High:   ████████ 8 tasks (2 blocked)
Medium: ████░░░░ 4 tasks
Low:    ██░░░░░░ 2 tasks
```

#### utils/analyze-project.md
高级项目分析：

**分析模式：**
- "velocity" → 冲刺速度和趋势
- "quality" → 代码质量指标
- "risk" → 风险评估和缓解
- "dependencies" → 依赖图分析
- "team" → 工作负载和技能分布
- "architecture" → 系统设计连贯性

**速度分析示例：**
```
📊 Velocity Analysis
━━━━━━━━━━━━━━━━━━━
Current Sprint: 24 points/week ↗️ +20%
Rolling Average: 20 points/week
Efficiency: 85% (17/20 tasks on time)

Bottlenecks Detected:
- Code review delays (avg 4h wait)
- Test environment availability
- Dependency on external team

Recommendations:
1. Implement parallel review process
2. Add staging environment
3. Mock external dependencies
```

**风险评估：**
- **技术风险**：没有备用负责人的高复杂度任务、架构中的单点故障、关键路径中的测试覆盖不足
- **项目风险**：关键路径依赖、资源可用性差距、截止日期可行性分析、范围蔓延指标

**预测洞察：**
- 基于模式的截止日期完成概率
- 资源需求预测
- 风险实现可能性
- 建议的干预措施

#### generate/generate-tasks.md
任务文件生成系统：

**生成内容：**
为每个任务创建如 `task_001.txt` 的文件：
```
Task ID: 1
Title: Implement user authentication
Status: pending
Priority: high
Dependencies: []
Created: 2024-01-15
Complexity: 7

## Description
Create a secure user authentication system with login, logout, and session management.

## Details
- Use JWT tokens for session management
- Implement secure password hashing
- Add remember me functionality
- Include password reset flow

## Test Strategy
- Unit tests for auth functions
- Integration tests for login flow
- Security testing for vulnerabilities
- Performance tests for concurrent logins
```

**智能功能：**
1. **一致格式化**：标准化结构、清晰章节、AI 可读格式、Markdown 兼容
2. **上下文信息**：完整任务详情、相关任务引用、进度指示器、实现注释
3. **增量更新**：仅重新生成更改的任务、保留自定义添加、跟踪生成时间戳

**使用案例：**
- **AI 上下文**：为 AI 助手提供任务上下文
- **文档化**：独立任务文档
- **归档**：任务历史保存
- **共享**：向团队成员发送特定任务

#### sync-readme/sync-readme.md
README 同步系统：

**README 生成：**
1. **项目标题**：项目名称、任务进度摘要、最后更新时间
2. **任务章节**：按状态或优先级组织、进度指示器、任务描述、依赖注释
3. **可视化元素**：进度条、状态徽章、优先级指示器、完成复选标记

**智能功能：**
1. **智能分组**：按功能区域、按冲刺/里程碑、按分配开发者、按优先级
2. **进度跟踪**：整体完成情况、冲刺速度、燃尽指示、时间跟踪
3. **格式选项**：GitHub 风格 markdown、任务复选框、可折叠章节、表格格式

**示例输出：**
```markdown
## 🚀 Current Sprint

### In Progress
- [ ] 🔄 #5 **Implement user authentication** (60% complete)
  - Dependencies: API design (#3 ✅)
  - Subtasks: 4 (2 completed)
  - Est: 8h / Spent: 5h

### Pending (High Priority)
- [ ] ⚡ #8 **Create dashboard UI**
  - Blocked by: #5
  - Complexity: High
  - Est: 12h
```

#### setup/install-taskmaster.md
Task Master 安装系统：

**检测和安装过程：**
1. **检查当前安装**：验证 task-master 命令存在、检查 npm 全局包
2. **系统需求检查**：验证 Node.js 安装、验证 npm 安装、检查 Node 版本（需要 16+）
3. **全局安装 Task Master**：`npm install -g task-master-ai`
4. **验证安装**：检查版本、验证命令可用
5. **初始设置**：在当前目录初始化、配置 AI 提供商

**故障排除：**
- **权限错误**：使用 sudo 或修复 npm 权限
- **网络问题**：使用不同注册表
- **Node 版本问题**：通过 nvm 安装 Node 18+

## 系统架构和设计理念

### 设计原则

1. **自然语言优先**
   - 所有命令都支持自然语言输入
   - 智能意图识别和上下文理解
   - 减少学习曲线，提高可用性

2. **AI 驱动的智能化**
   - 多 AI 提供商支持（Claude、GPT、Perplexity）
   - 智能任务分析和复杂度评估
   - 自动化工作流程和代码生成

3. **上下文感知**
   - 基于项目状态的智能建议
   - 学习用户工作模式
   - 适应性工作流程

4. **完整生命周期管理**
   - 从 PRD 解析到任务完成的全流程
   - 依赖关系管理和验证
   - 进度跟踪和报告

5. **高度可扩展**
   - 模块化命令结构
   - 可定制的工作流程
   - 插件式架构设计

### 技术特色

1. **智能解析引擎**
   - 自然语言到结构化命令的转换
   - 上下文相关的参数推断
   - 多种输入格式支持

2. **AI 集成架构**
   - 主要、研究、备用提供商配置
   - 智能模型选择和切换
   - API 密钥管理和验证

3. **依赖图管理**
   - 循环依赖检测和修复
   - 关键路径分析
   - 智能依赖建议

4. **工作流自动化**
   - 基于上下文的智能工作流
   - 命令管道和条件执行
   - 学习型工作模式适应

## 使用最佳实践

### 项目启动最佳实践

1. **初始化流程**
   ```
   /project:tm/setup/quick-install    # 安装 Task Master
   /project:tm/init/quick             # 快速项目初始化
   /project:tm/models/setup           # 配置 AI 提供商
   /project:tm/parse-prd <prd-file>   # 解析需求文档
   ```

2. **任务分析和规划**
   ```
   /project:tm/analyze-complexity     # 分析任务复杂度
   /project:tm/expand/all            # 分解复杂任务
   /project:tm/validate-dependencies # 验证依赖关系
   /project:tm/status                # 查看项目状态
   ```

### 日常工作流程

1. **开始工作日**
   ```
   /project:tm/status                # 查看项目状态
   /project:tm/next                  # 获取下一个任务建议
   /project:tm/set-status/to-in-progress <id>  # 开始任务
   ```

2. **任务完成**
   ```
   /project:tm/set-status/to-done <id>  # 标记完成
   /project:tm/next                     # 获取下一个任务
   ```

3. **项目监控**
   ```
   /project:tm/complexity-report    # 查看复杂度报告
   /project:tm/utils/analyze        # 项目分析
   /project:tm/sync-readme          # 更新文档
   ```

### 高级功能使用

1. **智能工作流**
   ```
   /project:tm/workflows/smart-flow      # 上下文感知工作流
   /project:tm/workflows/auto-implement  # 自动实现
   /project:tm/workflows/pipeline        # 命令管道
   ```

2. **批量操作**
   ```
   /project:tm/update tasks 1-5: add security requirements
   /project:tm/list pending high priority
   /project:tm/expand/all
   ```

## 总结

这个 Task Master 命令系统代表了项目管理工具的重大创新，它将传统的任务管理与现代 AI 技术完美结合。系统的核心优势包括：

1. **智能化程度高**：从自然语言解析到智能工作流，AI 贯穿整个系统
2. **用户体验优秀**：自然语言交互降低了学习成本，提高了效率
3. **功能完整全面**：覆盖项目管理的全生命周期，从需求到交付
4. **高度可定制**：灵活的配置和扩展能力，适应不同团队需求
5. **集成友好**：与现有开发工具和流程无缝集成

这个系统不仅仅是一个任务管理工具，更是一个智能的项目管理助手，能够学习用户习惯、提供智能建议、自动化重复工作，真正实现了"AI 驱动的项目管理"的愿景。

对于开发团队而言，这个系统可以显著提高项目管理效率，减少管理开销，让团队更专注于核心开发工作。同时，其强大的分析和报告功能也为项目决策提供了有力支持。

## 技术实现详解

### 系统架构深度分析

#### 数据流架构

```
用户输入 (自然语言)
    ↓
参数解析引擎 (NLP处理)
    ↓
命令路由器 (意图识别)
    ↓
AI 提供商选择器 (上下文感知)
    ↓
Task Master CLI 执行器
    ↓
数据存储层 (.taskmaster/)
    ↓
输出格式化器 (可视化)
    ↓
用户界面 (Claude Code)
```

#### 文件系统结构

```
项目根目录/
├── .taskmaster/
│   ├── config.json              # 全局配置
│   ├── tasks.json               # 任务数据存储
│   ├── models.json              # AI 模型配置
│   ├── history/                 # 命令历史
│   │   ├── commands.log         # 命令执行日志
│   │   └── changes.log          # 数据变更日志
│   ├── reports/                 # 分析报告
│   │   ├── complexity-analysis.md
│   │   ├── dependency-graph.json
│   │   └── velocity-metrics.json
│   ├── tasks/                   # 生成的任务文件
│   │   ├── task_001.txt
│   │   ├── task_002.txt
│   │   └── ...
│   ├── backup/                  # 自动备份
│   │   ├── tasks-20240115.json
│   │   └── subtasks-20240115.json
│   └── cache/                   # 缓存数据
│       ├── ai-responses.json
│       └── dependency-cache.json
├── .claude/
│   └── commands/tm/             # 命令定义
└── README.md                    # 同步的项目文档
```

#### API 集成架构

```
Task Master Core
    ↓
AI Provider Abstraction Layer
    ├── Claude API (Anthropic)
    │   ├── Authentication: API Key
    │   ├── Model: claude-3-5-sonnet
    │   └── Usage: 主要任务生成
    ├── OpenAI API
    │   ├── Authentication: API Key
    │   ├── Models: gpt-4, gpt-3.5-turbo
    │   └── Usage: 备用提供商
    └── Perplexity API
        ├── Authentication: API Key
        ├── Model: perplexity-sonar
        └── Usage: 研究增强模式
```

### 详细命令实现分析

#### init-project.md 技术实现

**完整命令语法：**
```bash
task-master init [options] [prd-file]

Options:
  -y, --yes                    # 跳过所有确认提示
  --name=<project-name>        # 指定项目名称
  --description=<desc>         # 项目描述
  --template=<template-name>   # 使用项目模板
  --ai-provider=<provider>     # 指定默认 AI 提供商
  --skip-git                   # 跳过 Git 仓库检测
  --config-file=<path>         # 自定义配置文件路径
```

**内部工作流程：**
```javascript
async function initProject(args) {
  // 1. 参数解析和验证
  const options = parseArguments(args);
  validateOptions(options);

  // 2. 环境检测
  const environment = await detectEnvironment();
  const gitRepo = await detectGitRepository();
  const existingConfig = await checkExistingConfig();

  // 3. 项目配置生成
  const projectConfig = {
    name: options.name || path.basename(process.cwd()),
    description: options.description || "Task Master Project",
    created: new Date().toISOString(),
    version: "1.0.0",
    aiProviders: await detectAIProviders(),
    gitIntegration: gitRepo ? true : false
  };

  // 4. 目录结构创建
  await createDirectoryStructure();
  await writeConfigFiles(projectConfig);
  await initializeTasksFile();

  // 5. AI 提供商验证
  if (projectConfig.aiProviders.length === 0) {
    console.warn("No AI providers configured. Run 'task-master models --setup'");
  }

  // 6. 后续处理
  if (options.prdFile) {
    await parsePRD(options.prdFile);
  }

  return {
    success: true,
    projectPath: process.cwd(),
    config: projectConfig,
    nextSteps: generateNextSteps(projectConfig)
  };
}
```

**错误处理和边缘情况：**
```javascript
// 权限错误处理
try {
  await fs.mkdir('.taskmaster', { recursive: true });
} catch (error) {
  if (error.code === 'EACCES') {
    throw new Error('Permission denied. Run with sudo or check directory permissions.');
  }
  throw error;
}

// 现有项目检测
if (await fs.pathExists('.taskmaster/config.json')) {
  if (!options.force) {
    const answer = await inquirer.prompt([{
      type: 'confirm',
      name: 'overwrite',
      message: 'Task Master project already exists. Overwrite?',
      default: false
    }]);
    if (!answer.overwrite) {
      process.exit(0);
    }
  }
}

// Git 仓库冲突处理
if (gitRepo && gitRepo.hasUncommittedChanges) {
  console.warn('Uncommitted changes detected. Consider committing before initialization.');
}
```

**实际使用示例：**
```bash
# 基本初始化
$ task-master init
✓ Created .taskmaster directory structure
✓ Initialized empty tasks.json
✓ Detected git repository
⚠ No AI providers configured
→ Next: Run 'task-master models --setup'

# 带 PRD 文件的初始化
$ task-master init --name="E-commerce Platform" requirements.md
✓ Project "E-commerce Platform" initialized
✓ Parsing PRD file: requirements.md
✓ Generated 12 tasks from requirements
✓ AI provider: claude-3-5-sonnet (configured)
→ Next: Run 'task-master list' to see generated tasks

# 快速初始化（生产环境）
$ task-master init -y --template=web-app
✓ Quick initialization complete
✓ Applied web-app template
✓ 15 boilerplate tasks created
→ Ready to start development
```

#### parse-prd.md 技术实现

**完整命令语法：**
```bash
task-master parse-prd [options] <prd-file>

Options:
  --input=<file>               # PRD 文件路径
  --output=<file>              # 输出文件路径 (默认: tasks.json)
  --num-tasks=<number>         # 生成任务数量 (默认: 10-15)
  --research                   # 使用研究模式
  --comprehensive              # 生成更详细的任务
  --template=<template>        # 使用任务模板
  --priority-weights=<json>    # 优先级权重配置
  --complexity-threshold=<n>   # 复杂度阈值
  --include-tests              # 包含测试任务
  --include-docs               # 包含文档任务
  --dry-run                    # 预览模式，不写入文件
```

**AI 提示词模板：**
```javascript
const PRD_ANALYSIS_PROMPT = `
Analyze the following Product Requirements Document and generate a structured task breakdown.

PRD Content:
${prdContent}

Requirements:
1. Generate ${numTasks} tasks that cover all major requirements
2. Each task should have:
   - Clear, actionable title
   - Detailed description
   - Acceptance criteria
   - Time estimate (in hours)
   - Complexity score (1-10)
   - Priority level (high/medium/low)
   - Dependencies (if any)
   - Test strategy

3. Task categories to include:
   - Setup/Infrastructure (10-15%)
   - Core Features (60-70%)
   - Testing (15-20%)
   - Documentation (5-10%)

4. Consider:
   - Technical complexity
   - Business value
   - Risk factors
   - Team capabilities

Output format: JSON array of task objects
`;
```

**内部处理流程：**
```javascript
async function parsePRD(prdFile, options = {}) {
  // 1. 文件读取和预处理
  const prdContent = await fs.readFile(prdFile, 'utf8');
  const preprocessed = preprocessPRDContent(prdContent);

  // 2. AI 提供商选择
  const aiProvider = options.research ? 'perplexity' : 'claude';
  const model = await getConfiguredModel(aiProvider);

  // 3. 任务生成
  const prompt = buildPRDAnalysisPrompt(preprocessed, options);
  const aiResponse = await callAI(model, prompt);

  // 4. 响应解析和验证
  let tasks;
  try {
    tasks = JSON.parse(aiResponse);
    validateTaskStructure(tasks);
  } catch (error) {
    throw new Error(`Failed to parse AI response: ${error.message}`);
  }

  // 5. 任务后处理
  tasks = await postProcessTasks(tasks, options);

  // 6. 依赖关系分析
  tasks = await analyzeDependencies(tasks);

  // 7. 复杂度验证
  tasks = await validateComplexity(tasks, options.complexityThreshold);

  // 8. 输出生成
  if (!options.dryRun) {
    await saveTasks(tasks, options.output);
    await generateTaskFiles(tasks);
  }

  return {
    tasks,
    summary: generateSummary(tasks),
    recommendations: generateRecommendations(tasks)
  };
}
```

**研究模式增强处理：**
```javascript
async function enhanceWithResearch(tasks, prdContent) {
  const researchPrompt = `
  Based on the following tasks and requirements, provide current industry best practices and recommendations:

  Tasks: ${JSON.stringify(tasks, null, 2)}
  Requirements: ${prdContent}

  For each task, suggest:
  1. Modern implementation approaches
  2. Recommended libraries/frameworks
  3. Security considerations
  4. Performance optimizations
  5. Testing strategies
  6. Potential pitfalls to avoid
  `;

  const researchResponse = await callAI('perplexity', researchPrompt);
  const enhancements = parseResearchResponse(researchResponse);

  return tasks.map(task => ({
    ...task,
    researchNotes: enhancements[task.id] || {},
    modernApproaches: enhancements[task.id]?.approaches || [],
    securityConsiderations: enhancements[task.id]?.security || []
  }));
}
```

**输出示例：**
```json
{
  "tasks": [
    {
      "id": 1,
      "title": "Setup Authentication Framework",
      "description": "Implement secure user authentication system with JWT tokens",
      "details": [
        "Install and configure authentication middleware",
        "Set up JWT token generation and validation",
        "Implement password hashing with bcrypt",
        "Create user registration and login endpoints"
      ],
      "acceptanceCriteria": [
        "Users can register with email and password",
        "Users can login and receive JWT token",
        "Passwords are securely hashed",
        "Token validation works on protected routes"
      ],
      "priority": "high",
      "complexity": 7,
      "timeEstimate": 12,
      "dependencies": [],
      "testStrategy": {
        "unit": "Test authentication functions",
        "integration": "Test login/register flow",
        "security": "Test password security and token validation"
      },
      "researchNotes": {
        "modernApproaches": ["OAuth 2.0", "Passport.js", "Auth0"],
        "securityConsiderations": ["Rate limiting", "CSRF protection", "Secure headers"]
      }
    }
  ],
  "summary": {
    "totalTasks": 12,
    "estimatedHours": 156,
    "complexityDistribution": {
      "high": 3,
      "medium": 6,
      "low": 3
    },
    "priorityDistribution": {
      "high": 4,
      "medium": 5,
      "low": 3
    }
  },
  "recommendations": [
    "Consider breaking down task #5 (complexity 9) into subtasks",
    "Tasks #3 and #7 can be worked on in parallel",
    "High-priority tasks should be completed in first sprint"
  ]
}
```

#### list-tasks.md 技术实现

**完整命令语法：**
```bash
task-master list [filters] [options]

Filters:
  status:<status>              # 按状态过滤 (pending|in-progress|done|review|deferred|cancelled)
  priority:<priority>          # 按优先级过滤 (high|medium|low)
  complexity:<range>           # 按复杂度过滤 (1-10, >5, <3)
  assignee:<name>              # 按负责人过滤
  tag:<tag>                    # 按标签过滤
  created:<date-range>         # 按创建日期过滤
  updated:<date-range>         # 按更新日期过滤
  blocked                      # 仅显示被阻塞的任务
  unblocked                    # 仅显示未被阻塞的任务
  critical-path                # 仅显示关键路径任务

Options:
  --with-subtasks              # 包含子任务
  --tree                       # 树形显示
  --format=<format>            # 输出格式 (table|json|markdown|csv)
  --sort=<field>               # 排序字段
  --limit=<number>             # 限制显示数量
  --columns=<list>             # 自定义显示列
  --no-color                   # 禁用颜色输出
  --export=<file>              # 导出到文件
```

**智能过滤器解析：**
```javascript
function parseFilters(args) {
  const filters = {
    status: [],
    priority: [],
    complexity: null,
    assignee: null,
    tags: [],
    dateRange: null,
    blocked: null,
    criticalPath: false,
    customQuery: null
  };

  // 自然语言解析
  const naturalLanguagePatterns = [
    {
      pattern: /pending\s+(high|medium|low)/i,
      handler: (match) => {
        filters.status.push('pending');
        filters.priority.push(match[1].toLowerCase());
      }
    },
    {
      pattern: /blocked\s+tasks?/i,
      handler: () => { filters.blocked = true; }
    },
    {
      pattern: /complexity\s*[><=]\s*(\d+)/i,
      handler: (match) => {
        filters.complexity = {
          operator: match[0].match(/[><=]/)[0],
          value: parseInt(match[1])
        };
      }
    },
    {
      pattern: /(\d+)-(\d+)/,
      handler: (match) => {
        filters.idRange = {
          start: parseInt(match[1]),
          end: parseInt(match[2])
        };
      }
    }
  ];

  // 应用模式匹配
  const queryString = args.join(' ');
  naturalLanguagePatterns.forEach(({ pattern, handler }) => {
    const match = queryString.match(pattern);
    if (match) {
      handler(match);
    }
  });

  return filters;
}
```

**任务显示格式化：**
```javascript
function formatTaskList(tasks, options = {}) {
  switch (options.format) {
    case 'table':
      return formatAsTable(tasks, options);
    case 'tree':
      return formatAsTree(tasks, options);
    case 'json':
      return JSON.stringify(tasks, null, 2);
    case 'markdown':
      return formatAsMarkdown(tasks, options);
    default:
      return formatAsDefault(tasks, options);
  }
}

function formatAsTable(tasks, options) {
  const table = new Table({
    head: options.columns || ['ID', 'Title', 'Status', 'Priority', 'Complexity', 'Assignee'],
    colWidths: [5, 40, 12, 10, 10, 15]
  });

  tasks.forEach(task => {
    const row = [
      task.id,
      truncate(task.title, 35),
      colorizeStatus(task.status),
      colorizePriority(task.priority),
      task.complexity || 'N/A',
      task.assignee || 'Unassigned'
    ];
    table.push(row);
  });

  return table.toString();
}

function formatAsTree(tasks, options) {
  const tree = buildTaskTree(tasks);
  return renderTree(tree, {
    showSubtasks: options.withSubtasks,
    colorize: !options.noColor,
    maxDepth: options.maxDepth || 3
  });
}
```

**实际输出示例：**
```bash
# 基本列表
$ task-master list
┌─────┬──────────────────────────────────────┬────────────┬──────────┬──────────┬─────────────┐
│ ID  │ Title                                │ Status     │ Priority │ Complexity│ Assignee    │
├─────┼──────────────────────────────────────┼────────────┼──────────┼──────────┼─────────────┤
│ 1   │ Setup Authentication Framework      │ 🟡 progress│ 🔴 high  │ 7        │ john.doe    │
│ 2   │ Create User Registration API        │ ⏳ pending │ 🔴 high  │ 5        │ Unassigned  │
│ 3   │ Implement Password Reset Flow       │ ⏳ pending │ 🟡 medium│ 4        │ jane.smith  │
│ 4   │ Add Email Verification              │ ⏳ pending │ 🟢 low   │ 3        │ Unassigned  │
│ 5   │ Setup Database Schema               │ ✅ done    │ 🔴 high  │ 6        │ john.doe    │
└─────┴──────────────────────────────────────┴────────────┴──────────┴──────────┴─────────────┘

Summary: 5 tasks (1 in-progress, 3 pending, 1 done)
Blocked: 0 tasks
Critical Path: Tasks 1 → 2 → 3

# 树形显示带子任务
$ task-master list --tree --with-subtasks
📋 Project Tasks
├── 🟡 #1 Setup Authentication Framework (in-progress)
│   ├── ✅ #1.1 Install authentication middleware (done)
│   ├── 🟡 #1.2 Configure JWT settings (in-progress)
│   └── ⏳ #1.3 Create auth routes (pending)
├── ⏳ #2 Create User Registration API (pending)
│   └── Dependencies: #1
├── ⏳ #3 Implement Password Reset Flow (pending)
│   └── Dependencies: #2
└── ✅ #5 Setup Database Schema (done)

# 复杂过滤查询
$ task-master list pending high complexity>5 --format=markdown
## High Priority Pending Tasks (Complexity > 5)

| ID | Title | Complexity | Estimate | Dependencies |
|----|-------|------------|----------|--------------|
| 2  | Create User Registration API | 5 | 8h | #1 |

**Recommendations:**
- Task #2 is ready to start once #1 is completed
- Consider breaking down if complexity increases

# JSON 输出用于脚本处理
$ task-master list blocked --format=json
{
  "tasks": [],
  "summary": {
    "total": 0,
    "blocked": 0,
    "message": "No blocked tasks found"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### set-status 系列命令技术实现

**状态转换状态机：**
```javascript
const STATUS_TRANSITIONS = {
  'pending': ['in-progress', 'deferred', 'cancelled'],
  'in-progress': ['done', 'review', 'pending', 'deferred', 'cancelled'],
  'done': ['review', 'pending'], // 允许重新打开
  'review': ['done', 'in-progress', 'pending'],
  'deferred': ['pending', 'cancelled'],
  'cancelled': ['pending'] // 允许恢复
};

function validateStatusTransition(currentStatus, newStatus) {
  const allowedTransitions = STATUS_TRANSITIONS[currentStatus] || [];
  if (!allowedTransitions.includes(newStatus)) {
    throw new Error(
      `Invalid status transition: ${currentStatus} → ${newStatus}. ` +
      `Allowed transitions: ${allowedTransitions.join(', ')}`
    );
  }
}
```

**to-in-progress.md 详细实现：**
```bash
task-master set-status --id=<task-id> --status=in-progress [options]

Options:
  --force                      # 强制状态更改，跳过验证
  --assign=<assignee>          # 同时分配负责人
  --branch=<branch-name>       # 创建 Git 分支
  --estimate=<hours>           # 更新时间估算
  --notes=<text>               # 添加状态更改注释
  --notify=<emails>            # 通知相关人员
  --start-timer                # 开始时间跟踪
```

```javascript
async function setStatusToInProgress(taskId, options = {}) {
  // 1. 任务验证和依赖检查
  const task = await getTask(taskId);
  if (!task) {
    throw new Error(`Task ${taskId} not found`);
  }

  // 检查依赖关系
  const unmetDependencies = await checkUnmetDependencies(task);
  if (unmetDependencies.length > 0 && !options.force) {
    const depList = unmetDependencies.map(dep => `#${dep.id}: ${dep.title}`).join('\n');
    throw new Error(
      `Cannot start task ${taskId}. Unmet dependencies:\n${depList}\n\n` +
      `Use --force to override this check.`
    );
  }

  // 检查是否有其他进行中的任务
  const inProgressTasks = await getTasksByStatus('in-progress');
  if (inProgressTasks.length > 0 && !options.allowMultiple) {
    const currentTask = inProgressTasks[0];
    const answer = await inquirer.prompt([{
      type: 'list',
      name: 'action',
      message: `Task #${currentTask.id} is already in progress. What would you like to do?`,
      choices: [
        { name: `Pause #${currentTask.id} and start #${taskId}`, value: 'switch' },
        { name: `Work on both tasks simultaneously`, value: 'parallel' },
        { name: `Cancel starting #${taskId}`, value: 'cancel' }
      ]
    }]);

    if (answer.action === 'cancel') {
      return { cancelled: true };
    } else if (answer.action === 'switch') {
      await setTaskStatus(currentTask.id, 'pending', {
        notes: `Paused to work on #${taskId}`
      });
    }
  }

  // 2. 环境设置
  const environmentSetup = await setupWorkEnvironment(task, options);

  // 3. Git 分支管理
  if (options.branch !== false) {
    const branchName = options.branch || generateBranchName(task);
    await createGitBranch(branchName, task);
  }

  // 4. 状态更新
  const updatedTask = await updateTaskStatus(task, 'in-progress', {
    startedAt: new Date().toISOString(),
    assignee: options.assign || task.assignee,
    notes: options.notes,
    estimatedHours: options.estimate || task.estimatedHours,
    environment: environmentSetup
  });

  // 5. 时间跟踪
  if (options.startTimer) {
    await startTimeTracking(taskId);
  }

  // 6. 通知系统
  if (options.notify) {
    await sendNotifications(options.notify, {
      type: 'task_started',
      task: updatedTask,
      assignee: updatedTask.assignee
    });
  }

  // 7. 智能建议生成
  const suggestions = await generateWorkSuggestions(updatedTask);

  return {
    task: updatedTask,
    environment: environmentSetup,
    suggestions,
    nextSteps: generateNextSteps(updatedTask)
  };
}

async function setupWorkEnvironment(task, options) {
  const setup = {
    filesOpened: [],
    testsSetup: false,
    documentationLinks: [],
    similarTasks: []
  };

  // 打开相关文件
  const relatedFiles = await findRelatedFiles(task);
  setup.filesOpened = relatedFiles;

  // 设置测试监视器
  if (task.testStrategy && !options.skipTests) {
    await setupTestWatchers(task.testStrategy);
    setup.testsSetup = true;
  }

  // 收集相关文档
  setup.documentationLinks = await findRelevantDocumentation(task);

  // 查找类似已完成任务
  setup.similarTasks = await findSimilarCompletedTasks(task);

  return setup;
}
```

**实际使用示例：**
```bash
# 基本状态更改
$ task-master set-status --id=5 --status=in-progress
⚠️  Checking dependencies for task #5...
✅ All dependencies met
⚠️  Task #3 is currently in-progress. Switch to #5? (y/n) y
✅ Paused task #3
✅ Started task #5: "Implement user authentication"
🌿 Created git branch: feature/user-authentication
📁 Opened related files:
   - src/auth/index.js
   - tests/auth.test.js
   - docs/authentication.md
🔍 Similar completed tasks:
   - #12: OAuth integration (completed 2 weeks ago)
   - #8: Session management (completed 1 month ago)

⏱️  Estimated completion: 8 hours
📋 Next steps:
   1. Review authentication requirements
   2. Set up JWT configuration
   3. Implement login endpoint
   4. Write unit tests

# 强制启动（跳过依赖检查）
$ task-master set-status --id=7 --status=in-progress --force --notes="Working around blocked dependency"
⚠️  Forcing status change despite unmet dependencies:
   - #6: Database schema setup (pending)
✅ Task #7 started with force flag
📝 Note added: "Working around blocked dependency"

# 带分配和分支创建
$ task-master set-status --id=9 --status=in-progress --assign=john.doe --branch=feature/payment-integration
✅ Task #9 assigned to john.doe
🌿 Created git branch: feature/payment-integration
✅ Status changed to in-progress
📧 Notification <NAME_EMAIL>
```

#### update-task.md 高级实现

**自然语言更新解析器：**
```javascript
class TaskUpdateParser {
  constructor() {
    this.patterns = [
      // 优先级更新
      {
        regex: /(?:set|change|update)\s+priority\s+(?:of\s+)?(?:task\s+)?(\d+)\s+to\s+(high|medium|low)/i,
        handler: (match) => ({
          taskId: parseInt(match[1]),
          updates: { priority: match[2].toLowerCase() }
        })
      },

      // 时间估算更新
      {
        regex: /(?:add|increase)\s+(\d+)\s*(?:hours?|h)\s+to\s+(?:task\s+)?(\d+)/i,
        handler: (match) => ({
          taskId: parseInt(match[2]),
          updates: {
            estimatedHours: { operation: 'add', value: parseInt(match[1]) }
          }
        })
      },

      // 依赖关系更新
      {
        regex: /(?:add|create)\s+dependency\s+(?:from\s+)?(?:task\s+)?(\d+)\s+(?:to|on)\s+(?:task\s+)?(\d+)/i,
        handler: (match) => ({
          taskId: parseInt(match[1]),
          updates: {
            dependencies: { operation: 'add', value: parseInt(match[2]) }
          }
        })
      },

      // 内容更新
      {
        regex: /(?:update|modify)\s+(?:task\s+)?(\d+):\s*(.+)/i,
        handler: (match) => ({
          taskId: parseInt(match[1]),
          updates: {
            description: { operation: 'enhance', value: match[2] }
          }
        })
      },

      // 批量状态更新
      {
        regex: /(?:mark|set)\s+tasks?\s+([\d,\s-]+)\s+(?:as\s+)?(pending|in-progress|done|review|deferred|cancelled)/i,
        handler: (match) => {
          const taskIds = this.parseTaskIdList(match[1]);
          return {
            taskIds,
            updates: { status: match[2] }
          };
        }
      }
    ];
  }

  parse(input) {
    for (const pattern of this.patterns) {
      const match = input.match(pattern.regex);
      if (match) {
        return pattern.handler(match);
      }
    }

    // 如果没有匹配的模式，使用 AI 解析
    return this.parseWithAI(input);
  }

  async parseWithAI(input) {
    const prompt = `
    Parse the following task update request and return a structured update object:

    Input: "${input}"

    Return JSON with:
    {
      "taskId": number or array of numbers,
      "updates": {
        "field": "new_value" or {"operation": "add|remove|replace", "value": "..."}
      },
      "confidence": 0-1
    }
    `;

    const response = await callAI('claude', prompt);
    return JSON.parse(response);
  }

  parseTaskIdList(idString) {
    const ids = [];
    const parts = idString.split(',');

    for (const part of parts) {
      const trimmed = part.trim();
      if (trimmed.includes('-')) {
        // 范围：1-5
        const [start, end] = trimmed.split('-').map(n => parseInt(n.trim()));
        for (let i = start; i <= end; i++) {
          ids.push(i);
        }
      } else {
        // 单个 ID
        ids.push(parseInt(trimmed));
      }
    }

    return ids;
  }
}
```

**智能更新应用器：**
```javascript
class TaskUpdater {
  async applyUpdates(taskId, updates, options = {}) {
    const task = await getTask(taskId);
    const originalTask = { ...task };

    // 验证更新
    await this.validateUpdates(task, updates);

    // 应用更新
    const updatedTask = await this.processUpdates(task, updates, options);

    // 一致性检查
    await this.validateConsistency(updatedTask);

    // 保存更改
    await saveTask(updatedTask);

    // 记录更改历史
    await this.logChanges(originalTask, updatedTask, options);

    // 触发相关更新
    await this.triggerRelatedUpdates(updatedTask, originalTask);

    return {
      task: updatedTask,
      changes: this.generateChangesSummary(originalTask, updatedTask),
      suggestions: await this.generateSuggestions(updatedTask)
    };
  }

  async processUpdates(task, updates, options) {
    const updatedTask = { ...task };

    for (const [field, value] of Object.entries(updates)) {
      switch (field) {
        case 'description':
          updatedTask.description = await this.enhanceDescription(
            task.description,
            value,
            options.aiEnhance
          );
          break;

        case 'estimatedHours':
          if (typeof value === 'object' && value.operation) {
            updatedTask.estimatedHours = this.applyNumericOperation(
              task.estimatedHours,
              value.operation,
              value.value
            );
          } else {
            updatedTask.estimatedHours = value;
          }
          break;

        case 'dependencies':
          updatedTask.dependencies = await this.updateDependencies(
            task.dependencies,
            value
          );
          break;

        case 'priority':
          updatedTask.priority = value;
          // 自动调整相关任务优先级
          if (options.cascadePriority) {
            await this.cascadePriorityChanges(task.id, value);
          }
          break;

        default:
          updatedTask[field] = value;
      }
    }

    // 更新时间戳
    updatedTask.updatedAt = new Date().toISOString();

    return updatedTask;
  }

  async enhanceDescription(currentDescription, enhancement, useAI = true) {
    if (!useAI) {
      return `${currentDescription}\n\n${enhancement}`;
    }

    const prompt = `
    Enhance the following task description by incorporating the new information:

    Current description:
    ${currentDescription}

    New information to incorporate:
    ${enhancement}

    Requirements:
    1. Maintain the original intent and structure
    2. Seamlessly integrate new information
    3. Improve clarity and completeness
    4. Keep the same tone and style

    Return only the enhanced description.
    `;

    return await callAI('claude', prompt);
  }
}
```

**批量更新处理：**
```javascript
async function bulkUpdateTasks(taskIds, updates, options = {}) {
  const results = {
    successful: [],
    failed: [],
    warnings: []
  };

  // 预验证所有任务
  const tasks = await Promise.all(
    taskIds.map(id => getTask(id).catch(() => null))
  );

  const validTasks = tasks.filter(task => task !== null);
  const invalidIds = taskIds.filter((id, index) => tasks[index] === null);

  if (invalidIds.length > 0) {
    results.warnings.push(`Invalid task IDs: ${invalidIds.join(', ')}`);
  }

  // 影响分析
  if (!options.skipAnalysis) {
    const impact = await analyzeBulkUpdateImpact(validTasks, updates);
    if (impact.hasHighImpact && !options.force) {
      const confirmation = await confirmBulkUpdate(impact);
      if (!confirmation) {
        return { cancelled: true, impact };
      }
    }
  }

  // 执行批量更新
  for (const task of validTasks) {
    try {
      const result = await new TaskUpdater().applyUpdates(task.id, updates, {
        ...options,
        bulkOperation: true
      });
      results.successful.push(result);
    } catch (error) {
      results.failed.push({
        taskId: task.id,
        error: error.message
      });
    }
  }

  // 后处理
  await postProcessBulkUpdate(results, options);

  return results;
}
```

**实际使用示例：**
```bash
# 自然语言单任务更新
$ task-master update "add security requirements to task 5"
🔍 Analyzing update request...
✅ Parsed: Enhance task #5 with security requirements
📝 AI-enhanced description:
   Original: "Implement user authentication system"
   Enhanced: "Implement user authentication system with comprehensive security measures including rate limiting, CSRF protection, and secure session management"
✅ Updated task #5
💡 Suggestions:
   - Consider updating task #7 (API endpoints) for consistency
   - Add security testing subtasks

# 批量优先级更新
$ task-master update "set tasks 10-15 to high priority"
⚠️  Bulk Update Impact Analysis:
   - 6 tasks will change priority
   - May affect sprint planning
   - Tasks #12, #14 will move to critical path
Continue? (y/n) y
✅ Updated 6 tasks successfully
📊 Summary:
   - High priority tasks: 4 → 10
   - Critical path extended by 2 days
   - Recommend sprint rebalancing

# 复杂字段更新
$ task-master update --id=8 --field=estimatedHours --operation=add --value=4 --notes="Added time for additional testing"
✅ Task #8 time estimate: 12h → 16h (+4h)
📝 Note: "Added time for additional testing"
⚠️  Impact: Sprint capacity exceeded by 2 hours
💡 Suggestion: Consider moving task #15 to next sprint

# AI 辅助内容增强
$ task-master update 3: "needs to handle file uploads"
🤖 AI Enhancement in progress...
✅ Enhanced task #3 description:
   Added: File upload handling with validation, size limits, and secure storage
   Updated: Test strategy to include file upload scenarios
   Added: Security considerations for file type validation
⏱️  Time estimate adjusted: 6h → 9h (+3h)
🔗 Related tasks that may need updates: #7, #11
```

#### 依赖管理系统深度技术实现

**依赖图数据结构与算法：**
```javascript
class DependencyGraph {
  constructor() {
    this.nodes = new Map(); // taskId -> task
    this.edges = new Map(); // taskId -> Set of dependent taskIds
    this.reverseEdges = new Map(); // taskId -> Set of dependency taskIds
    this.weights = new Map(); // edge weights for critical path calculation
  }

  addTask(task) {
    this.nodes.set(task.id, task);
    if (!this.edges.has(task.id)) {
      this.edges.set(task.id, new Set());
    }
    if (!this.reverseEdges.has(task.id)) {
      this.reverseEdges.set(task.id, new Set());
    }
  }

  addDependency(taskId, dependsOnId, weight = null) {
    // taskId depends on dependsOnId
    this.reverseEdges.get(taskId).add(dependsOnId);
    this.edges.get(dependsOnId).add(taskId);

    if (weight !== null) {
      this.weights.set(`${dependsOnId}->${taskId}`, weight);
    }
  }

  // 高级循环依赖检测 - 支持复杂循环和强连通分量
  detectStronglyConnectedComponents() {
    const index = new Map();
    const lowLink = new Map();
    const onStack = new Set();
    const stack = [];
    const sccs = [];
    let currentIndex = 0;

    const strongConnect = (taskId) => {
      index.set(taskId, currentIndex);
      lowLink.set(taskId, currentIndex);
      currentIndex++;
      stack.push(taskId);
      onStack.add(taskId);

      for (const dependentId of this.edges.get(taskId)) {
        if (!index.has(dependentId)) {
          strongConnect(dependentId);
          lowLink.set(taskId, Math.min(lowLink.get(taskId), lowLink.get(dependentId)));
        } else if (onStack.has(dependentId)) {
          lowLink.set(taskId, Math.min(lowLink.get(taskId), index.get(dependentId)));
        }
      }

      if (lowLink.get(taskId) === index.get(taskId)) {
        const scc = [];
        let w;
        do {
          w = stack.pop();
          onStack.delete(w);
          scc.push(w);
        } while (w !== taskId);

        if (scc.length > 1) {
          sccs.push(scc);
        }
      }
    };

    for (const taskId of this.nodes.keys()) {
      if (!index.has(taskId)) {
        strongConnect(taskId);
      }
    }

    return sccs;
  }

  // 关键路径计算 - CPM算法实现
  calculateCriticalPath() {
    const earlyStart = new Map();
    const earlyFinish = new Map();
    const lateStart = new Map();
    const lateFinish = new Map();
    const slack = new Map();

    // 拓扑排序
    const sorted = this.topologicalSort();
    if (sorted.length !== this.nodes.size) {
      throw new Error('Cannot calculate critical path: graph contains cycles');
    }

    // 前向计算 - 最早开始和完成时间
    for (const taskId of sorted) {
      const task = this.nodes.get(taskId);
      const duration = task.estimatedHours || 0;

      let maxEarlyFinish = 0;
      for (const depId of this.reverseEdges.get(taskId)) {
        maxEarlyFinish = Math.max(maxEarlyFinish, earlyFinish.get(depId) || 0);
      }

      earlyStart.set(taskId, maxEarlyFinish);
      earlyFinish.set(taskId, maxEarlyFinish + duration);
    }

    // 项目总时长
    const projectDuration = Math.max(...Array.from(earlyFinish.values()));

    // 反向计算 - 最晚开始和完成时间
    for (let i = sorted.length - 1; i >= 0; i--) {
      const taskId = sorted[i];
      const task = this.nodes.get(taskId);
      const duration = task.estimatedHours || 0;

      if (this.edges.get(taskId).size === 0) {
        // 终端任务
        lateFinish.set(taskId, projectDuration);
      } else {
        let minLateStart = Infinity;
        for (const dependentId of this.edges.get(taskId)) {
          minLateStart = Math.min(minLateStart, lateStart.get(dependentId));
        }
        lateFinish.set(taskId, minLateStart);
      }

      lateStart.set(taskId, lateFinish.get(taskId) - duration);
      slack.set(taskId, lateStart.get(taskId) - earlyStart.get(taskId));
    }

    // 识别关键路径
    const criticalTasks = [];
    for (const taskId of this.nodes.keys()) {
      if (slack.get(taskId) === 0) {
        criticalTasks.push(taskId);
      }
    }

    return {
      projectDuration,
      criticalPath: this.buildCriticalPathSequence(criticalTasks),
      taskSchedule: Array.from(this.nodes.keys()).map(taskId => ({
        taskId,
        task: this.nodes.get(taskId),
        earlyStart: earlyStart.get(taskId),
        earlyFinish: earlyFinish.get(taskId),
        lateStart: lateStart.get(taskId),
        lateFinish: lateFinish.get(taskId),
        slack: slack.get(taskId),
        isCritical: slack.get(taskId) === 0
      })),
      bottlenecks: this.identifyBottlenecks(criticalTasks)
    };
  }

  buildCriticalPathSequence(criticalTasks) {
    // 构建关键路径的实际序列
    const criticalSet = new Set(criticalTasks);
    const path = [];
    const visited = new Set();

    // 找到起始任务（没有依赖的关键任务）
    const startTasks = criticalTasks.filter(taskId =>
      this.reverseEdges.get(taskId).size === 0 ||
      !Array.from(this.reverseEdges.get(taskId)).some(depId => criticalSet.has(depId))
    );

    const buildPath = (taskId) => {
      if (visited.has(taskId)) return;
      visited.add(taskId);
      path.push(taskId);

      // 找到下一个关键任务
      for (const dependentId of this.edges.get(taskId)) {
        if (criticalSet.has(dependentId)) {
          buildPath(dependentId);
          break; // 关键路径是唯一的
        }
      }
    };

    if (startTasks.length > 0) {
      buildPath(startTasks[0]);
    }

    return path;
  }

  identifyBottlenecks(criticalTasks) {
    const bottlenecks = [];

    for (const taskId of criticalTasks) {
      const task = this.nodes.get(taskId);
      const dependentCount = this.edges.get(taskId).size;
      const dependencyCount = this.reverseEdges.get(taskId).size;

      // 识别瓶颈特征
      if (dependentCount > 3) {
        bottlenecks.push({
          taskId,
          type: 'fan_out_bottleneck',
          severity: dependentCount > 5 ? 'high' : 'medium',
          description: `Task blocks ${dependentCount} other tasks`,
          recommendation: 'Consider breaking down this task or parallelizing dependent work'
        });
      }

      if (dependencyCount > 3) {
        bottlenecks.push({
          taskId,
          type: 'fan_in_bottleneck',
          severity: dependencyCount > 5 ? 'high' : 'medium',
          description: `Task depends on ${dependencyCount} other tasks`,
          recommendation: 'Consider if all dependencies are truly necessary'
        });
      }

      if (task.complexity > 7) {
        bottlenecks.push({
          taskId,
          type: 'complexity_bottleneck',
          severity: 'high',
          description: `High complexity task (${task.complexity}/10) on critical path`,
          recommendation: 'Expand this task into smaller, manageable subtasks'
        });
      }
    }

    return bottlenecks;
  }
}
```

**validate-dependencies.md 高级验证实现：**
```javascript
class AdvancedDependencyValidator {
  constructor() {
    this.validationRules = [
      new CircularDependencyRule(),
      new MissingDependencyRule(),
      new LogicalConsistencyRule(),
      new ComplexityRule(),
      new PerformanceRule(),
      new BusinessLogicRule()
    ];
  }

  async validate(options = {}) {
    const tasks = await getAllTasks();
    const graph = new DependencyGraph();

    // 构建增强依赖图
    for (const task of tasks) {
      graph.addTask(task);
      for (const depId of task.dependencies || []) {
        const weight = await this.calculateDependencyWeight(task, depId);
        graph.addDependency(task.id, depId, weight);
      }
    }

    const results = {
      errors: [],
      warnings: [],
      suggestions: [],
      metrics: {},
      graph: graph
    };

    // 执行所有验证规则
    for (const rule of this.validationRules) {
      const ruleResults = await rule.validate(graph, tasks, options);
      results.errors.push(...ruleResults.errors);
      results.warnings.push(...ruleResults.warnings);
      results.suggestions.push(...ruleResults.suggestions);
      Object.assign(results.metrics, ruleResults.metrics);
    }

    // 生成依赖健康评分
    results.healthScore = this.calculateHealthScore(results);

    // 生成优化建议
    results.optimizations = await this.generateOptimizations(graph, results);

    return results;
  }

  async calculateDependencyWeight(task, depId) {
    const depTask = await getTask(depId);
    if (!depTask) return 1;

    // 权重基于多个因素
    let weight = 1;

    // 复杂度影响
    weight += (depTask.complexity || 0) * 0.1;

    // 优先级影响
    const priorityWeights = { low: 0.5, medium: 1, high: 1.5 };
    weight *= priorityWeights[depTask.priority] || 1;

    // 状态影响
    if (depTask.status === 'done') weight *= 0.1;
    else if (depTask.status === 'in-progress') weight *= 0.5;

    return weight;
  }

  calculateHealthScore(results) {
    let score = 100;

    // 错误严重影响分数
    score -= results.errors.length * 15;

    // 警告中等影响分数
    score -= results.warnings.length * 5;

    // 复杂度影响
    const avgComplexity = results.metrics.averageComplexity || 0;
    if (avgComplexity > 7) score -= 10;
    else if (avgComplexity > 5) score -= 5;

    // 依赖密度影响
    const dependencyDensity = results.metrics.dependencyDensity || 0;
    if (dependencyDensity > 0.3) score -= 10;
    else if (dependencyDensity > 0.2) score -= 5;

    return Math.max(0, Math.min(100, score));
  }
}

class CircularDependencyRule {
  async validate(graph, tasks, options) {
    const results = { errors: [], warnings: [], suggestions: [], metrics: {} };

    // 使用强连通分量检测所有循环
    const sccs = graph.detectStronglyConnectedComponents();

    for (const scc of sccs) {
      const severity = this.assessCycleSeverity(scc, graph);

      results.errors.push({
        type: 'circular_dependency',
        severity: severity.level,
        cycle: scc,
        impact: severity.impact,
        message: `Circular dependency detected: ${scc.map(id => `#${id}`).join(' ↔ ')}`,
        autoFixable: severity.level === 'low',
        suggestedFix: this.suggestCycleFix(scc, graph)
      });
    }

    results.metrics.circularDependencies = sccs.length;
    results.metrics.maxCycleLength = sccs.length > 0 ? Math.max(...sccs.map(scc => scc.length)) : 0;

    return results;
  }

  assessCycleSeverity(cycle, graph) {
    const cycleLength = cycle.length;
    const criticalTasks = cycle.filter(taskId => {
      const task = graph.nodes.get(taskId);
      return task.priority === 'high' || task.complexity > 7;
    });

    if (cycleLength === 2 && criticalTasks.length === 0) {
      return { level: 'low', impact: 'minimal' };
    } else if (cycleLength <= 3 && criticalTasks.length <= 1) {
      return { level: 'medium', impact: 'moderate' };
    } else {
      return { level: 'high', impact: 'severe' };
    }
  }

  suggestCycleFix(cycle, graph) {
    if (cycle.length === 2) {
      // 简单双向依赖
      const [taskA, taskB] = cycle;
      const taskAObj = graph.nodes.get(taskA);
      const taskBObj = graph.nodes.get(taskB);

      const priorityValues = { low: 1, medium: 2, high: 3 };
      const priorityA = priorityValues[taskAObj.priority] || 1;
      const priorityB = priorityValues[taskBObj.priority] || 1;

      if (priorityA > priorityB) {
        return `Remove dependency: #${taskB} should not depend on #${taskA}`;
      } else {
        return `Remove dependency: #${taskA} should not depend on #${taskB}`;
      }
    } else {
      // 复杂循环
      const weakestLink = this.findWeakestLink(cycle, graph);
      return `Consider removing dependency at weakest link: ${weakestLink}`;
    }
  }

  findWeakestLink(cycle, graph) {
    let weakestLink = null;
    let minWeight = Infinity;

    for (let i = 0; i < cycle.length; i++) {
      const from = cycle[i];
      const to = cycle[(i + 1) % cycle.length];
      const weight = graph.weights.get(`${from}->${to}`) || 1;

      if (weight < minWeight) {
        minWeight = weight;
        weakestLink = `#${from} → #${to}`;
      }
    }

    return weakestLink;
  }
}
```

**fix-dependencies.md 智能修复系统：**
```javascript
class IntelligentDependencyFixer {
  constructor(validationResults) {
    this.results = validationResults;
    this.fixes = [];
    this.manualReviewRequired = [];
    this.backupData = new Map();
  }

  async executeAutoFix(options = {}) {
    // 创建备份
    await this.createBackup();

    try {
      // 按优先级执行修复
      await this.fixCriticalErrors();
      await this.fixWarnings();
      await this.applyOptimizations();

      // 验证修复结果
      const postFixValidation = await this.validateFixes();

      return {
        success: true,
        fixes: this.fixes,
        manualReviewRequired: this.manualReviewRequired,
        postFixValidation,
        backupLocation: this.backupData.get('location')
      };

    } catch (error) {
      // 修复失败，恢复备份
      await this.restoreBackup();
      throw new Error(`Auto-fix failed: ${error.message}. Backup restored.`);
    }
  }

  async fixCriticalErrors() {
    const criticalErrors = this.results.errors.filter(e => e.severity === 'high');

    for (const error of criticalErrors) {
      switch (error.type) {
        case 'circular_dependency':
          await this.fixCircularDependency(error);
          break;
        case 'missing_dependency':
          await this.fixMissingDependency(error);
          break;
        case 'invalid_dependency':
          await this.fixInvalidDependency(error);
          break;
      }
    }
  }

  async fixCircularDependency(error) {
    const cycle = error.cycle;

    if (error.autoFixable) {
      // 自动修复简单循环
      const fixAction = this.parseFixSuggestion(error.suggestedFix);
      await this.applyDependencyRemoval(fixAction.from, fixAction.to);

      this.fixes.push({
        type: 'circular_dependency_fixed',
        action: 'removed_dependency',
        from: fixAction.from,
        to: fixAction.to,
        originalCycle: cycle,
        confidence: 'high'
      });
    } else {
      // 复杂循环需要人工审查
      this.manualReviewRequired.push({
        type: 'complex_circular_dependency',
        cycle: cycle,
        analysisResults: await this.analyzeComplexCycle(cycle),
        recommendations: await this.generateCycleRecommendations(cycle)
      });
    }
  }

  async analyzeComplexCycle(cycle) {
    const analysis = {
      cycleLength: cycle.length,
      involvedTasks: [],
      businessImpact: 'unknown',
      technicalComplexity: 0,
      suggestedBreakPoints: []
    };

    for (const taskId of cycle) {
      const task = await getTask(taskId);
      analysis.involvedTasks.push({
        id: taskId,
        title: task.title,
        priority: task.priority,
        complexity: task.complexity,
        status: task.status,
        assignee: task.assignee
      });

      analysis.technicalComplexity += task.complexity || 0;
    }

    // 识别潜在的断点
    analysis.suggestedBreakPoints = await this.identifyBreakPoints(cycle);

    // 评估业务影响
    analysis.businessImpact = this.assessBusinessImpact(analysis.involvedTasks);

    return analysis;
  }

  async identifyBreakPoints(cycle) {
    const breakPoints = [];

    for (let i = 0; i < cycle.length; i++) {
      const from = cycle[i];
      const to = cycle[(i + 1) % cycle.length];

      const fromTask = await getTask(from);
      const toTask = await getTask(to);

      // 计算断点分数
      let score = 0;

      // 优先级差异
      const priorityValues = { low: 1, medium: 2, high: 3 };
      const priorityDiff = Math.abs(
        (priorityValues[fromTask.priority] || 1) -
        (priorityValues[toTask.priority] || 1)
      );
      score += priorityDiff * 10;

      // 复杂度差异
      const complexityDiff = Math.abs(
        (fromTask.complexity || 0) - (toTask.complexity || 0)
      );
      score += complexityDiff * 5;

      // 状态兼容性
      if (fromTask.status === 'done' && toTask.status !== 'done') {
        score += 20; // 已完成任务依赖未完成任务是不合理的
      }

      // 负责人不同
      if (fromTask.assignee !== toTask.assignee) {
        score += 5;
      }

      breakPoints.push({
        from,
        to,
        score,
        reason: this.generateBreakPointReason(fromTask, toTask, score)
      });
    }

    // 按分数排序，分数高的是更好的断点
    return breakPoints.sort((a, b) => b.score - a.score);
  }

  generateBreakPointReason(fromTask, toTask, score) {
    const reasons = [];

    if (fromTask.status === 'done' && toTask.status !== 'done') {
      reasons.push('Completed task depending on incomplete task');
    }

    if (fromTask.priority === 'low' && toTask.priority === 'high') {
      reasons.push('Low priority task blocking high priority task');
    }

    if (Math.abs((fromTask.complexity || 0) - (toTask.complexity || 0)) > 3) {
      reasons.push('Significant complexity difference');
    }

    if (fromTask.assignee !== toTask.assignee) {
      reasons.push('Different assignees - potential coordination issue');
    }

    return reasons.length > 0 ? reasons.join('; ') : 'Potential weak dependency link';
  }
}
```

#### 工作流自动化系统深度实现

**smart-workflow.md 智能工作流引擎：**
```javascript
class SmartWorkflowEngine {
  constructor() {
    this.contextAnalyzer = new WorkflowContextAnalyzer();
    this.patternMatcher = new WorkflowPatternMatcher();
    this.executionEngine = new WorkflowExecutionEngine();
    this.learningSystem = new WorkflowLearningSystem();
  }

  async executeSmartWorkflow(userInput = '', options = {}) {
    // 1. 上下文分析
    const context = await this.contextAnalyzer.analyzeFullContext();

    // 2. 模式匹配和工作流选择
    const workflowCandidates = await this.patternMatcher.findMatchingWorkflows(context, userInput);

    // 3. 工作流优先级排序
    const selectedWorkflow = await this.selectOptimalWorkflow(workflowCandidates, context);

    // 4. 执行工作流
    const executionResult = await this.executionEngine.execute(selectedWorkflow, context, options);

    // 5. 学习和优化
    await this.learningSystem.recordExecution(selectedWorkflow, executionResult, context);

    return {
      workflow: selectedWorkflow,
      context: context,
      result: executionResult,
      learningInsights: await this.learningSystem.generateInsights(executionResult)
    };
  }
}

class WorkflowContextAnalyzer {
  async analyzeFullContext() {
    const context = {
      // 项目状态上下文
      projectState: await this.getProjectState(),

      // 用户行为上下文
      userBehavior: await this.getUserBehaviorContext(),

      // 时间上下文
      timeContext: await this.getTimeContext(),

      // 环境上下文
      environmentContext: await this.getEnvironmentContext(),

      // 团队上下文
      teamContext: await this.getTeamContext()
    };

    // 计算上下文权重和优先级
    context.priorities = await this.calculateContextPriorities(context);

    return context;
  }

  async getProjectState() {
    const tasks = await getAllTasks();
    const dependencies = await getDependencyGraph();
    const criticalPath = dependencies.calculateCriticalPath();

    return {
      totalTasks: tasks.length,
      tasksByStatus: this.groupTasksByStatus(tasks),
      complexityDistribution: this.analyzeComplexityDistribution(tasks),
      priorityDistribution: this.analyzePriorityDistribution(tasks),
      blockedTasks: tasks.filter(t => this.isTaskBlocked(t)),
      criticalPath: criticalPath,
      velocity: await this.calculateVelocity(),
      burndownRate: await this.calculateBurndownRate(),
      riskFactors: await this.identifyRiskFactors(tasks)
    };
  }

  async getUserBehaviorContext() {
    const commandHistory = await this.getRecentCommandHistory(50);
    const workingSessions = await this.getWorkingSessions(7); // 最近7天

    return {
      recentCommands: commandHistory,
      commandPatterns: this.analyzeCommandPatterns(commandHistory),
      workingHours: this.analyzeWorkingHours(workingSessions),
      preferredWorkflows: await this.getPreferredWorkflows(),
      productivityPatterns: this.analyzeProductivityPatterns(workingSessions),
      interruptionFrequency: this.calculateInterruptionFrequency(commandHistory),
      focusTimePreference: this.analyzeFocusTimePreference(workingSessions)
    };
  }

  async getTimeContext() {
    const now = new Date();
    const workingHours = await this.getUserWorkingHours();

    return {
      currentTime: now,
      timeOfDay: this.categorizeTimeOfDay(now.getHours()),
      dayOfWeek: now.getDay(),
      isWorkingDay: this.isWorkingDay(now),
      isWorkingHours: this.isWithinWorkingHours(now, workingHours),
      sessionDuration: await this.getCurrentSessionDuration(),
      timeUntilBreak: this.calculateTimeUntilBreak(now, workingHours),
      energyLevel: await this.predictEnergyLevel(now),
      meetingSchedule: await this.getUpcomingMeetings(),
      deadlineProximity: await this.analyzeDeadlineProximity()
    };
  }

  async getEnvironmentContext() {
    return {
      gitStatus: await this.getGitStatus(),
      branchInfo: await this.getCurrentBranchInfo(),
      uncommittedChanges: await this.getUncommittedChanges(),
      testStatus: await this.getTestStatus(),
      buildStatus: await this.getBuildStatus(),
      developmentEnvironment: await this.getDevelopmentEnvironmentStatus(),
      resourceUsage: await this.getSystemResourceUsage(),
      networkConnectivity: await this.checkNetworkConnectivity()
    };
  }

  analyzeCommandPatterns(commandHistory) {
    const patterns = {
      sequentialPatterns: [],
      frequentCombinations: [],
      timeBasedPatterns: [],
      contextSwitchingFrequency: 0
    };

    // 分析命令序列模式
    for (let i = 0; i < commandHistory.length - 2; i++) {
      const sequence = commandHistory.slice(i, i + 3).map(cmd => cmd.command);
      const pattern = sequence.join(' → ');

      const existing = patterns.sequentialPatterns.find(p => p.pattern === pattern);
      if (existing) {
        existing.frequency++;
      } else {
        patterns.sequentialPatterns.push({
          pattern,
          frequency: 1,
          lastSeen: commandHistory[i + 2].timestamp,
          avgTimeBetween: this.calculateAvgTimeBetween(sequence, commandHistory)
        });
      }
    }

    // 分析频繁组合
    const commandCounts = new Map();
    commandHistory.forEach(cmd => {
      commandCounts.set(cmd.command, (commandCounts.get(cmd.command) || 0) + 1);
    });

    patterns.frequentCombinations = Array.from(commandCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([command, frequency]) => ({ command, frequency }));

    // 分析基于时间的模式
    patterns.timeBasedPatterns = this.analyzeTimeBasedPatterns(commandHistory);

    // 计算上下文切换频率
    patterns.contextSwitchingFrequency = this.calculateContextSwitchingFrequency(commandHistory);

    return patterns;
  }

  calculateContextSwitchingFrequency(commandHistory) {
    let switches = 0;
    const contextCategories = {
      'list': 'viewing',
      'show': 'viewing',
      'status': 'viewing',
      'add-task': 'creating',
      'update': 'modifying',
      'set-status': 'modifying',
      'expand': 'planning',
      'analyze-complexity': 'planning'
    };

    for (let i = 1; i < commandHistory.length; i++) {
      const prevContext = contextCategories[commandHistory[i-1].command] || 'other';
      const currContext = contextCategories[commandHistory[i].command] || 'other';

      if (prevContext !== currContext) {
        switches++;
      }
    }

    return commandHistory.length > 1 ? switches / (commandHistory.length - 1) : 0;
  }
}

class WorkflowPatternMatcher {
  constructor() {
    this.workflowTemplates = new Map();
    this.initializeWorkflowTemplates();
  }

  initializeWorkflowTemplates() {
    // 晨间启动工作流
    this.workflowTemplates.set('morning_startup', {
      name: 'Morning Startup',
      triggers: {
        timeOfDay: ['morning'],
        lastCommand: ['status', null],
        sessionDuration: [0, 30], // 分钟
        dayOfWeek: [1, 2, 3, 4, 5] // 工作日
      },
      steps: [
        { command: 'status', description: 'Check project overview' },
        { command: 'list blocked', description: 'Identify any blockers' },
        { command: 'list in-progress', description: 'Review active work' },
        { command: 'next', description: 'Get next task recommendation' }
      ],
      priority: 8,
      estimatedDuration: 5
    });

    // 任务完成后工作流
    this.workflowTemplates.set('task_completion', {
      name: 'Task Completion Follow-up',
      triggers: {
        lastCommand: ['set-status.*done', 'complete'],
        timeSinceLastCommand: [0, 300] // 5分钟内
      },
      steps: [
        { command: 'validate-dependencies', description: 'Check if tasks were unblocked' },
        { command: 'list unblocked', description: 'Show newly available tasks' },
        { command: 'next', description: 'Recommend next task' },
        { command: 'sync-readme', description: 'Update project documentation' }
      ],
      priority: 9,
      estimatedDuration: 3
    });

    // 复杂度分析工作流
    this.workflowTemplates.set('complexity_analysis', {
      name: 'Complexity Analysis and Planning',
      triggers: {
        lastCommand: ['parse-prd', 'add-task'],
        projectState: {
          highComplexityTasks: [3, Infinity],
          unexpandedTasks: [2, Infinity]
        }
      },
      steps: [
        { command: 'analyze-complexity', description: 'Analyze task complexity' },
        { command: 'complexity-report', description: 'Review complexity report' },
        { command: 'expand-all', description: 'Expand complex tasks', conditional: true },
        { command: 'validate-dependencies', description: 'Validate updated dependencies' }
      ],
      priority: 7,
      estimatedDuration: 10
    });

    // 冲刺规划工作流
    this.workflowTemplates.set('sprint_planning', {
      name: 'Sprint Planning',
      triggers: {
        projectState: {
          pendingTasks: [10, Infinity],
          inProgressTasks: [0, 2]
        },
        timeOfDay: ['morning', 'afternoon'],
        userBehavior: {
          planningSession: true
        }
      },
      steps: [
        { command: 'list pending priority:high', description: 'Review high priority tasks' },
        { command: 'analyze-complexity', description: 'Analyze complexity' },
        { command: 'validate-dependencies', description: 'Check dependencies' },
        { command: 'utils/analyze velocity', description: 'Check team velocity' },
        { command: 'generate sprint-plan', description: 'Generate sprint plan' }
      ],
      priority: 6,
      estimatedDuration: 15
    });

    // 问题解决工作流
    this.workflowTemplates.set('problem_solving', {
      name: 'Problem Solving',
      triggers: {
        projectState: {
          blockedTasks: [1, Infinity],
          errorRate: [0.1, Infinity]
        },
        lastCommand: ['list blocked', 'validate-dependencies']
      },
      steps: [
        { command: 'list blocked', description: 'Identify all blocked tasks' },
        { command: 'validate-dependencies', description: 'Analyze dependency issues' },
        { command: 'fix-dependencies', description: 'Auto-fix dependency problems' },
        { command: 'utils/analyze bottlenecks', description: 'Identify bottlenecks' },
        { command: 'generate unblock-plan', description: 'Create unblocking plan' }
      ],
      priority: 10,
      estimatedDuration: 8
    });
  }

  async findMatchingWorkflows(context, userInput) {
    const matches = [];

    for (const [workflowId, template] of this.workflowTemplates) {
      const matchScore = await this.calculateMatchScore(template, context, userInput);

      if (matchScore > 0.3) { // 阈值
        matches.push({
          workflowId,
          template,
          matchScore,
          matchReasons: await this.getMatchReasons(template, context, userInput)
        });
      }
    }

    // 按匹配分数排序
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }

  async calculateMatchScore(template, context, userInput) {
    let score = 0;
    let maxScore = 0;

    // 检查时间触发器
    if (template.triggers.timeOfDay) {
      maxScore += 0.2;
      if (template.triggers.timeOfDay.includes(context.timeContext.timeOfDay)) {
        score += 0.2;
      }
    }

    // 检查最后命令触发器
    if (template.triggers.lastCommand) {
      maxScore += 0.3;
      const lastCmd = context.userBehavior.recentCommands[0]?.command;
      for (const pattern of template.triggers.lastCommand) {
        if (pattern === null && !lastCmd) {
          score += 0.3;
          break;
        } else if (pattern && lastCmd && new RegExp(pattern).test(lastCmd)) {
          score += 0.3;
          break;
        }
      }
    }

    // 检查项目状态触发器
    if (template.triggers.projectState) {
      maxScore += 0.3;
      let stateMatches = 0;
      let totalStateChecks = 0;

      for (const [key, range] of Object.entries(template.triggers.projectState)) {
        totalStateChecks++;
        const actualValue = this.getProjectStateValue(context.projectState, key);

        if (actualValue >= range[0] && actualValue <= range[1]) {
          stateMatches++;
        }
      }

      if (totalStateChecks > 0) {
        score += (stateMatches / totalStateChecks) * 0.3;
      }
    }

    // 检查用户输入匹配
    if (userInput) {
      maxScore += 0.2;
      const inputScore = this.calculateInputMatchScore(template, userInput);
      score += inputScore * 0.2;
    }

    return maxScore > 0 ? score / maxScore : 0;
  }

  getProjectStateValue(projectState, key) {
    switch (key) {
      case 'pendingTasks':
        return projectState.tasksByStatus.pending || 0;
      case 'inProgressTasks':
        return projectState.tasksByStatus['in-progress'] || 0;
      case 'blockedTasks':
        return projectState.blockedTasks.length;
      case 'highComplexityTasks':
        return projectState.complexityDistribution.high || 0;
      case 'unexpandedTasks':
        return projectState.totalTasks - (projectState.expandedTasks || 0);
      default:
        return 0;
    }
  }

  calculateInputMatchScore(template, userInput) {
    const keywords = {
      'morning_startup': ['start', 'begin', 'morning', 'daily', 'standup'],
      'task_completion': ['done', 'complete', 'finish', 'next'],
      'complexity_analysis': ['complex', 'analyze', 'breakdown', 'expand'],
      'sprint_planning': ['plan', 'sprint', 'organize', 'schedule'],
      'problem_solving': ['blocked', 'stuck', 'problem', 'issue', 'fix']
    };

    const templateKeywords = keywords[template.name.toLowerCase().replace(' ', '_')] || [];
    const inputLower = userInput.toLowerCase();

    let matches = 0;
    for (const keyword of templateKeywords) {
      if (inputLower.includes(keyword)) {
        matches++;
      }
    }

    return templateKeywords.length > 0 ? matches / templateKeywords.length : 0;
  }
}
```

**auto-implement-tasks.md 高级自动实现系统：**
```javascript
class AdvancedAutoImplementationEngine {
  constructor() {
    this.codeAnalyzer = new CodebaseAnalyzer();
    this.patternRecognizer = new ImplementationPatternRecognizer();
    this.codeGenerator = new IntelligentCodeGenerator();
    this.testGenerator = new AutoTestGenerator();
    this.qualityAssurance = new QualityAssuranceEngine();
  }

  async autoImplementTask(taskId, options = {}) {
    const task = await getTask(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    // 1. 预实现分析
    const analysisResult = await this.performPreImplementationAnalysis(task);

    // 2. 实现策略选择
    const strategy = await this.selectImplementationStrategy(task, analysisResult);

    // 3. 渐进式实现
    const implementationResult = await this.executeProgressiveImplementation(task, strategy, options);

    // 4. 质量保证
    const qaResult = await this.qualityAssurance.performQualityChecks(implementationResult);

    // 5. 后实现处理
    const postProcessResult = await this.performPostImplementationTasks(task, implementationResult, qaResult);

    return {
      task,
      strategy,
      implementation: implementationResult,
      qualityAssurance: qaResult,
      postProcessing: postProcessResult,
      summary: this.generateImplementationSummary(implementationResult, qaResult)
    };
  }

  async performPreImplementationAnalysis(task) {
    const analysis = {
      codebaseContext: await this.codeAnalyzer.analyzeRelevantCode(task),
      similarImplementations: await this.findSimilarImplementations(task),
      dependencyAnalysis: await this.analyzeDependencies(task),
      riskAssessment: await this.assessImplementationRisks(task),
      resourceRequirements: await this.estimateResourceRequirements(task),
      testingStrategy: await this.planTestingStrategy(task)
    };

    return analysis;
  }

  async selectImplementationStrategy(task, analysis) {
    const strategies = [
      new TDDStrategy(),
      new IncrementalStrategy(),
      new PrototypeFirstStrategy(),
      new RefactoringStrategy(),
      new TemplateBasedStrategy()
    ];

    let bestStrategy = null;
    let bestScore = 0;

    for (const strategy of strategies) {
      const score = await strategy.calculateSuitabilityScore(task, analysis);
      if (score > bestScore) {
        bestScore = score;
        bestStrategy = strategy;
      }
    }

    return {
      strategy: bestStrategy,
      score: bestScore,
      rationale: bestStrategy.getRationale(task, analysis),
      estimatedSteps: bestStrategy.generateSteps(task, analysis)
    };
  }

  async executeProgressiveImplementation(task, strategy, options) {
    const steps = strategy.estimatedSteps;
    const results = [];

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];

      console.log(`Step ${i + 1}/${steps.length}: ${step.description}`);

      try {
        const stepResult = await this.executeImplementationStep(step, task, results);
        results.push({
          step: i + 1,
          description: step.description,
          result: stepResult,
          status: 'completed',
          duration: stepResult.duration
        });

        // 中间验证
        if (step.requiresValidation) {
          const validation = await this.validateIntermediateResult(stepResult, step);
          if (!validation.passed) {
            throw new Error(`Step validation failed: ${validation.errors.join(', ')}`);
          }
        }

      } catch (error) {
        results.push({
          step: i + 1,
          description: step.description,
          error: error.message,
          status: 'failed'
        });

        // 错误恢复
        if (step.errorRecovery) {
          const recovery = await this.attemptErrorRecovery(step, error, results);
          if (recovery.success) {
            results[results.length - 1].status = 'recovered';
            results[results.length - 1].recovery = recovery;
          } else {
            throw new Error(`Implementation failed at step ${i + 1}: ${error.message}`);
          }
        } else {
          throw error;
        }
      }
    }

    return {
      steps: results,
      totalDuration: results.reduce((sum, r) => sum + (r.result?.duration || 0), 0),
      successRate: results.filter(r => r.status === 'completed').length / results.length,
      artifacts: this.collectArtifacts(results)
    };
  }
}

class TDDStrategy {
  calculateSuitabilityScore(task, analysis) {
    let score = 0;

    // TDD 适合有明确需求的任务
    if (task.acceptanceCriteria && task.acceptanceCriteria.length > 0) {
      score += 0.3;
    }

    // 适合复杂度中等的任务
    if (task.complexity >= 4 && task.complexity <= 7) {
      score += 0.2;
    }

    // 适合有现有测试框架的项目
    if (analysis.codebaseContext.hasTestFramework) {
      score += 0.2;
    }

    // 适合核心功能开发
    if (task.priority === 'high' && task.type === 'feature') {
      score += 0.2;
    }

    // 风险较高的任务适合 TDD
    if (analysis.riskAssessment.overallRisk > 0.6) {
      score += 0.1;
    }

    return score;
  }

  getRationale(task, analysis) {
    return `TDD approach selected because:
    - Task has clear acceptance criteria (${task.acceptanceCriteria?.length || 0} criteria)
    - Moderate complexity (${task.complexity}/10) suitable for test-driven development
    - Existing test framework available: ${analysis.codebaseContext.testFramework}
    - High priority task requiring robust implementation
    - Risk level (${Math.round(analysis.riskAssessment.overallRisk * 100)}%) benefits from test coverage`;
  }

  generateSteps(task, analysis) {
    return [
      {
        description: 'Write failing tests based on acceptance criteria',
        type: 'test_creation',
        requiresValidation: true,
        estimatedDuration: 15,
        artifacts: ['test_files']
      },
      {
        description: 'Implement minimal code to make tests pass',
        type: 'code_implementation',
        requiresValidation: true,
        estimatedDuration: 30,
        artifacts: ['source_files']
      },
      {
        description: 'Refactor code while maintaining test coverage',
        type: 'refactoring',
        requiresValidation: true,
        estimatedDuration: 20,
        artifacts: ['refactored_files']
      },
      {
        description: 'Add integration tests',
        type: 'integration_testing',
        requiresValidation: true,
        estimatedDuration: 15,
        artifacts: ['integration_tests']
      },
      {
        description: 'Performance and security validation',
        type: 'validation',
        requiresValidation: true,
        estimatedDuration: 10,
        artifacts: ['validation_reports']
      }
    ];
  }
}
```

#### 高级分析和报告系统

**analyze-project.md 深度项目分析实现：**
```javascript
class AdvancedProjectAnalyzer {
  constructor() {
    this.metricsCollector = new ProjectMetricsCollector();
    this.trendAnalyzer = new TrendAnalyzer();
    this.predictiveAnalyzer = new PredictiveAnalyzer();
    this.benchmarkComparator = new BenchmarkComparator();
  }

  async performComprehensiveAnalysis(analysisType = 'full', options = {}) {
    const analysis = {
      timestamp: new Date().toISOString(),
      analysisType,
      options,
      results: {}
    };

    // 基础指标收集
    analysis.results.basicMetrics = await this.metricsCollector.collectBasicMetrics();

    // 根据分析类型执行特定分析
    switch (analysisType) {
      case 'velocity':
        analysis.results.velocity = await this.analyzeVelocity(options);
        break;
      case 'quality':
        analysis.results.quality = await this.analyzeQuality(options);
        break;
      case 'risk':
        analysis.results.risk = await this.analyzeRisk(options);
        break;
      case 'dependencies':
        analysis.results.dependencies = await this.analyzeDependencies(options);
        break;
      case 'team':
        analysis.results.team = await this.analyzeTeam(options);
        break;
      case 'architecture':
        analysis.results.architecture = await this.analyzeArchitecture(options);
        break;
      case 'full':
      default:
        analysis.results = await this.performFullAnalysis(options);
        break;
    }

    // 生成洞察和建议
    analysis.insights = await this.generateInsights(analysis.results);
    analysis.recommendations = await this.generateRecommendations(analysis.results);
    analysis.actionItems = await this.generateActionItems(analysis.results);

    return analysis;
  }

  async analyzeVelocity(options) {
    const velocityData = await this.metricsCollector.collectVelocityData(options.timeRange || 30);

    const analysis = {
      currentVelocity: velocityData.current,
      historicalVelocity: velocityData.historical,
      trends: await this.trendAnalyzer.analyzeVelocityTrends(velocityData),
      predictions: await this.predictiveAnalyzer.predictFutureVelocity(velocityData),
      bottlenecks: await this.identifyVelocityBottlenecks(velocityData),
      recommendations: await this.generateVelocityRecommendations(velocityData)
    };

    // 详细的速度分析
    analysis.breakdown = {
      byTaskType: this.analyzeVelocityByTaskType(velocityData),
      byComplexity: this.analyzeVelocityByComplexity(velocityData),
      byPriority: this.analyzeVelocityByPriority(velocityData),
      byAssignee: this.analyzeVelocityByAssignee(velocityData),
      byTimeOfDay: this.analyzeVelocityByTimeOfDay(velocityData),
      byDayOfWeek: this.analyzeVelocityByDayOfWeek(velocityData)
    };

    // 效率指标
    analysis.efficiency = {
      taskCompletionRate: velocityData.completed / velocityData.started,
      averageTaskDuration: velocityData.totalDuration / velocityData.completed,
      reworkRate: velocityData.reworked / velocityData.completed,
      blockageRate: velocityData.blocked / velocityData.total,
      contextSwitchingOverhead: await this.calculateContextSwitchingOverhead(velocityData)
    };

    return analysis;
  }

  async analyzeQuality(options) {
    const qualityData = await this.metricsCollector.collectQualityData();

    const analysis = {
      overallQualityScore: 0,
      dimensions: {
        codeQuality: await this.analyzeCodeQuality(qualityData),
        processQuality: await this.analyzeProcessQuality(qualityData),
        deliverableQuality: await this.analyzeDeliverableQuality(qualityData),
        testQuality: await this.analyzeTestQuality(qualityData)
      },
      trends: await this.trendAnalyzer.analyzeQualityTrends(qualityData),
      riskAreas: await this.identifyQualityRiskAreas(qualityData)
    };

    // 计算总体质量分数
    analysis.overallQualityScore = this.calculateOverallQualityScore(analysis.dimensions);

    return analysis;
  }

  async analyzeRisk(options) {
    const riskData = await this.metricsCollector.collectRiskData();

    const analysis = {
      overallRiskLevel: 'unknown',
      riskCategories: {
        technical: await this.analyzeTechnicalRisks(riskData),
        schedule: await this.analyzeScheduleRisks(riskData),
        resource: await this.analyzeResourceRisks(riskData),
        quality: await this.analyzeQualityRisks(riskData),
        external: await this.analyzeExternalRisks(riskData)
      },
      riskMatrix: await this.generateRiskMatrix(riskData),
      mitigationStrategies: await this.generateMitigationStrategies(riskData),
      contingencyPlans: await this.generateContingencyPlans(riskData)
    };

    // 计算总体风险级别
    analysis.overallRiskLevel = this.calculateOverallRiskLevel(analysis.riskCategories);

    return analysis;
  }

  async identifyVelocityBottlenecks(velocityData) {
    const bottlenecks = [];

    // 分析任务停留时间
    const taskDwellTimes = velocityData.tasks.map(task => ({
      id: task.id,
      title: task.title,
      dwellTime: this.calculateDwellTime(task),
      status: task.status,
      complexity: task.complexity
    }));

    // 识别长时间停留的任务
    const longDwellTasks = taskDwellTimes.filter(task =>
      task.dwellTime > velocityData.averageDwellTime * 2
    );

    if (longDwellTasks.length > 0) {
      bottlenecks.push({
        type: 'long_dwell_time',
        severity: 'high',
        affectedTasks: longDwellTasks,
        impact: `${longDwellTasks.length} tasks have excessive dwell time`,
        recommendation: 'Review task complexity and resource allocation'
      });
    }

    // 分析依赖瓶颈
    const dependencyBottlenecks = await this.analyzeDependencyBottlenecks(velocityData);
    bottlenecks.push(...dependencyBottlenecks);

    // 分析资源瓶颈
    const resourceBottlenecks = await this.analyzeResourceBottlenecks(velocityData);
    bottlenecks.push(...resourceBottlenecks);

    // 分析流程瓶颈
    const processBottlenecks = await this.analyzeProcessBottlenecks(velocityData);
    bottlenecks.push(...processBottlenecks);

    return bottlenecks.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  async analyzeDependencyBottlenecks(velocityData) {
    const bottlenecks = [];
    const dependencyGraph = await getDependencyGraph();

    // 识别高扇出任务（阻塞多个其他任务）
    const highFanOutTasks = [];
    for (const [taskId, dependents] of dependencyGraph.edges) {
      if (dependents.size > 3) {
        const task = dependencyGraph.nodes.get(taskId);
        highFanOutTasks.push({
          id: taskId,
          title: task.title,
          dependentCount: dependents.size,
          status: task.status
        });
      }
    }

    if (highFanOutTasks.length > 0) {
      bottlenecks.push({
        type: 'high_fan_out',
        severity: 'high',
        affectedTasks: highFanOutTasks,
        impact: `${highFanOutTasks.length} tasks block multiple other tasks`,
        recommendation: 'Prioritize completion of high fan-out tasks'
      });
    }

    // 识别长依赖链
    const criticalPath = dependencyGraph.calculateCriticalPath();
    if (criticalPath.path.length > 8) {
      bottlenecks.push({
        type: 'long_dependency_chain',
        severity: 'medium',
        affectedTasks: criticalPath.path,
        impact: `Critical path has ${criticalPath.path.length} tasks`,
        recommendation: 'Look for opportunities to parallelize work'
      });
    }

    return bottlenecks;
  }
}

class ProjectMetricsCollector {
  async collectBasicMetrics() {
    const tasks = await getAllTasks();
    const now = new Date();

    return {
      totalTasks: tasks.length,
      tasksByStatus: this.groupBy(tasks, 'status'),
      tasksByPriority: this.groupBy(tasks, 'priority'),
      tasksByComplexity: this.groupComplexityDistribution(tasks),
      averageComplexity: this.calculateAverage(tasks, 'complexity'),
      totalEstimatedHours: tasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0),
      totalActualHours: tasks.reduce((sum, task) => sum + (task.actualHours || 0), 0),
      completionRate: tasks.filter(t => t.status === 'done').length / tasks.length,
      blockedTasksCount: tasks.filter(t => this.isTaskBlocked(t)).length,
      overdueTasksCount: tasks.filter(t => this.isTaskOverdue(t, now)).length,
      averageTaskAge: this.calculateAverageTaskAge(tasks, now),
      velocityMetrics: await this.calculateVelocityMetrics(tasks)
    };
  }

  async collectVelocityData(timeRangeDays = 30) {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - timeRangeDays * 24 * 60 * 60 * 1000);

    const tasks = await getTasksInDateRange(startDate, endDate);
    const completedTasks = tasks.filter(t => t.status === 'done');

    return {
      timeRange: { start: startDate, end: endDate },
      total: tasks.length,
      completed: completedTasks.length,
      started: tasks.filter(t => t.startedAt).length,
      blocked: tasks.filter(t => this.isTaskBlocked(t)).length,
      reworked: tasks.filter(t => t.reworkCount > 0).length,
      totalDuration: completedTasks.reduce((sum, task) =>
        sum + (task.actualHours || task.estimatedHours || 0), 0
      ),
      tasks: tasks,
      dailyCompletions: this.groupCompletionsByDay(completedTasks),
      weeklyCompletions: this.groupCompletionsByWeek(completedTasks),
      averageDwellTime: this.calculateAverageDwellTime(tasks),
      throughput: completedTasks.length / timeRangeDays,
      leadTime: this.calculateAverageLeadTime(completedTasks),
      cycleTime: this.calculateAverageCycleTime(completedTasks)
    };
  }

  calculateAverageDwellTime(tasks) {
    const dwellTimes = tasks.map(task => this.calculateDwellTime(task));
    return dwellTimes.reduce((sum, time) => sum + time, 0) / dwellTimes.length;
  }

  calculateDwellTime(task) {
    const now = new Date();
    const lastUpdate = new Date(task.updatedAt || task.createdAt);
    return (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60); // 小时
  }

  calculateAverageLeadTime(completedTasks) {
    const leadTimes = completedTasks.map(task => {
      const created = new Date(task.createdAt);
      const completed = new Date(task.completedAt);
      return (completed.getTime() - created.getTime()) / (1000 * 60 * 60 * 24); // 天
    });

    return leadTimes.reduce((sum, time) => sum + time, 0) / leadTimes.length;
  }

  calculateAverageCycleTime(completedTasks) {
    const cycleTimes = completedTasks.map(task => {
      const started = new Date(task.startedAt || task.createdAt);
      const completed = new Date(task.completedAt);
      return (completed.getTime() - started.getTime()) / (1000 * 60 * 60 * 24); // 天
    });

    return cycleTimes.reduce((sum, time) => sum + time, 0) / cycleTimes.length;
  }
}
```

**故障排除和调试系统：**
```javascript
class TaskMasterDiagnosticSystem {
  constructor() {
    this.diagnosticChecks = [
      new ConfigurationCheck(),
      new DependencyCheck(),
      new PerformanceCheck(),
      new DataIntegrityCheck(),
      new AIProviderCheck(),
      new FileSystemCheck()
    ];
  }

  async runDiagnostics(options = {}) {
    const diagnosticResults = {
      timestamp: new Date().toISOString(),
      overallHealth: 'unknown',
      checks: [],
      issues: [],
      recommendations: [],
      systemInfo: await this.collectSystemInfo()
    };

    console.log('🔍 Running Task Master diagnostics...\n');

    for (const check of this.diagnosticChecks) {
      try {
        console.log(`Running ${check.name}...`);
        const result = await check.run(options);

        diagnosticResults.checks.push({
          name: check.name,
          status: result.status,
          message: result.message,
          details: result.details,
          duration: result.duration
        });

        if (result.issues) {
          diagnosticResults.issues.push(...result.issues);
        }

        if (result.recommendations) {
          diagnosticResults.recommendations.push(...result.recommendations);
        }

        console.log(`  ${result.status === 'pass' ? '✅' : '❌'} ${result.message}`);

      } catch (error) {
        diagnosticResults.checks.push({
          name: check.name,
          status: 'error',
          message: `Check failed: ${error.message}`,
          error: error.stack
        });

        console.log(`  ❌ ${check.name} failed: ${error.message}`);
      }
    }

    // 计算总体健康状态
    diagnosticResults.overallHealth = this.calculateOverallHealth(diagnosticResults.checks);

    // 生成诊断报告
    const report = this.generateDiagnosticReport(diagnosticResults);

    return {
      results: diagnosticResults,
      report: report,
      quickFixes: await this.generateQuickFixes(diagnosticResults.issues)
    };
  }

  async collectSystemInfo() {
    return {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: {
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + ' MB',
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + ' MB'
      },
      taskMasterVersion: await this.getTaskMasterVersion(),
      workingDirectory: process.cwd(),
      configLocation: await this.getConfigLocation(),
      lastBackup: await this.getLastBackupInfo()
    };
  }
}

class ConfigurationCheck {
  constructor() {
    this.name = 'Configuration Check';
  }

  async run(options) {
    const startTime = Date.now();
    const issues = [];
    const recommendations = [];

    // 检查配置文件存在性
    const configPath = path.join(process.cwd(), '.taskmaster', 'config.json');
    if (!await fs.pathExists(configPath)) {
      issues.push({
        type: 'missing_config',
        severity: 'high',
        message: 'Task Master configuration file not found',
        solution: 'Run "task-master init" to initialize the project'
      });
    } else {
      // 检查配置文件完整性
      try {
        const config = await fs.readJson(configPath);

        // 检查必需字段
        const requiredFields = ['name', 'version', 'created'];
        for (const field of requiredFields) {
          if (!config[field]) {
            issues.push({
              type: 'invalid_config',
              severity: 'medium',
              message: `Missing required configuration field: ${field}`,
              solution: `Add "${field}" to your .taskmaster/config.json file`
            });
          }
        }

        // 检查 AI 提供商配置
        if (!config.aiProviders || config.aiProviders.length === 0) {
          issues.push({
            type: 'no_ai_providers',
            severity: 'high',
            message: 'No AI providers configured',
            solution: 'Run "task-master models --setup" to configure AI providers'
          });
        }

      } catch (error) {
        issues.push({
          type: 'corrupt_config',
          severity: 'high',
          message: 'Configuration file is corrupted or invalid JSON',
          solution: 'Restore from backup or reinitialize with "task-master init"'
        });
      }
    }

    // 检查任务文件
    const tasksPath = path.join(process.cwd(), '.taskmaster', 'tasks.json');
    if (!await fs.pathExists(tasksPath)) {
      issues.push({
        type: 'missing_tasks',
        severity: 'medium',
        message: 'Tasks file not found',
        solution: 'Initialize tasks with "task-master init" or "task-master parse-prd"'
      });
    }

    const duration = Date.now() - startTime;
    const status = issues.filter(i => i.severity === 'high').length > 0 ? 'fail' : 'pass';

    return {
      status,
      message: status === 'pass' ? 'Configuration is valid' : `Found ${issues.length} configuration issues`,
      details: { configPath, tasksPath },
      issues,
      recommendations,
      duration
    };
  }
}

class PerformanceCheck {
  constructor() {
    this.name = 'Performance Check';
  }

  async run(options) {
    const startTime = Date.now();
    const issues = [];
    const recommendations = [];
    const metrics = {};

    // 检查任务文件大小
    const tasksPath = path.join(process.cwd(), '.taskmaster', 'tasks.json');
    if (await fs.pathExists(tasksPath)) {
      const stats = await fs.stat(tasksPath);
      metrics.tasksFileSize = stats.size;

      if (stats.size > 1024 * 1024) { // 1MB
        issues.push({
          type: 'large_tasks_file',
          severity: 'medium',
          message: `Tasks file is large (${Math.round(stats.size / 1024)} KB)`,
          solution: 'Consider archiving completed tasks or splitting into multiple projects'
        });
      }
    }

    // 检查内存使用
    const memUsage = process.memoryUsage();
    metrics.memoryUsage = memUsage;

    if (memUsage.heapUsed > 100 * 1024 * 1024) { // 100MB
      issues.push({
        type: 'high_memory_usage',
        severity: 'medium',
        message: `High memory usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`,
        solution: 'Restart Task Master or reduce the number of concurrent operations'
      });
    }

    // 检查命令响应时间
    const responseTimeTest = await this.testCommandResponseTime();
    metrics.averageResponseTime = responseTimeTest.averageTime;

    if (responseTimeTest.averageTime > 2000) { // 2秒
      issues.push({
        type: 'slow_response_time',
        severity: 'medium',
        message: `Slow command response time: ${responseTimeTest.averageTime}ms`,
        solution: 'Check system resources and consider optimizing task data'
      });
    }

    // 检查磁盘空间
    const diskSpace = await this.checkDiskSpace();
    metrics.diskSpace = diskSpace;

    if (diskSpace.available < 100 * 1024 * 1024) { // 100MB
      issues.push({
        type: 'low_disk_space',
        severity: 'high',
        message: `Low disk space: ${Math.round(diskSpace.available / 1024 / 1024)} MB available`,
        solution: 'Free up disk space or move project to a location with more space'
      });
    }

    const duration = Date.now() - startTime;
    const status = issues.filter(i => i.severity === 'high').length > 0 ? 'fail' : 'pass';

    return {
      status,
      message: status === 'pass' ? 'Performance is acceptable' : `Found ${issues.length} performance issues`,
      details: metrics,
      issues,
      recommendations,
      duration
    };
  }

  async testCommandResponseTime() {
    const testCommands = ['status', 'list', 'show 1'];
    const times = [];

    for (const command of testCommands) {
      const start = Date.now();
      try {
        await taskMasterCLI.execute(command);
        times.push(Date.now() - start);
      } catch (error) {
        // 忽略错误，只测试响应时间
        times.push(Date.now() - start);
      }
    }

    return {
      times,
      averageTime: times.reduce((sum, time) => sum + time, 0) / times.length,
      maxTime: Math.max(...times),
      minTime: Math.min(...times)
    };
  }

  async checkDiskSpace() {
    const stats = await fs.stat(process.cwd());
    // 简化的磁盘空间检查，实际实现需要使用系统调用
    return {
      total: 1024 * 1024 * 1024, // 1GB (示例)
      available: 512 * 1024 * 1024 // 512MB (示例)
    };
  }
}
```

#### 性能优化和扩展性分析

**性能优化策略实现：**
```javascript
class PerformanceOptimizer {
  constructor() {
    this.optimizationStrategies = [
      new DataStructureOptimization(),
      new CachingOptimization(),
      new QueryOptimization(),
      new MemoryOptimization(),
      new IOOptimization()
    ];
  }

  async optimizePerformance(options = {}) {
    const currentMetrics = await this.collectPerformanceMetrics();
    const optimizationPlan = await this.generateOptimizationPlan(currentMetrics);

    const results = {
      beforeMetrics: currentMetrics,
      optimizations: [],
      afterMetrics: null,
      improvements: {}
    };

    for (const strategy of this.optimizationStrategies) {
      if (strategy.isApplicable(currentMetrics, options)) {
        console.log(`Applying ${strategy.name}...`);

        const optimizationResult = await strategy.apply(currentMetrics, options);
        results.optimizations.push(optimizationResult);

        if (optimizationResult.success) {
          console.log(`  ✅ ${optimizationResult.description}`);
        } else {
          console.log(`  ❌ ${optimizationResult.error}`);
        }
      }
    }

    // 重新测量性能
    results.afterMetrics = await this.collectPerformanceMetrics();
    results.improvements = this.calculateImprovements(results.beforeMetrics, results.afterMetrics);

    return results;
  }

  async collectPerformanceMetrics() {
    const metrics = {
      timestamp: new Date().toISOString(),
      memory: process.memoryUsage(),
      timing: {},
      fileSystem: {},
      database: {},
      ai: {}
    };

    // 命令执行时间测试
    metrics.timing = await this.measureCommandTiming();

    // 文件系统性能
    metrics.fileSystem = await this.measureFileSystemPerformance();

    // 数据库操作性能（任务文件读写）
    metrics.database = await this.measureDatabasePerformance();

    // AI 提供商响应时间
    metrics.ai = await this.measureAIPerformance();

    return metrics;
  }

  async measureCommandTiming() {
    const commands = [
      'status',
      'list',
      'list pending',
      'show 1',
      'analyze-complexity',
      'validate-dependencies'
    ];

    const timings = {};

    for (const command of commands) {
      const times = [];

      // 运行每个命令3次取平均值
      for (let i = 0; i < 3; i++) {
        const start = process.hrtime.bigint();
        try {
          await taskMasterCLI.execute(command);
        } catch (error) {
          // 忽略错误，只测量时间
        }
        const end = process.hrtime.bigint();
        times.push(Number(end - start) / 1000000); // 转换为毫秒
      }

      timings[command] = {
        average: times.reduce((sum, time) => sum + time, 0) / times.length,
        min: Math.min(...times),
        max: Math.max(...times),
        samples: times
      };
    }

    return timings;
  }

  async measureFileSystemPerformance() {
    const testFile = path.join(process.cwd(), '.taskmaster', 'perf_test.json');
    const testData = { test: 'performance', timestamp: Date.now() };

    // 写入性能测试
    const writeStart = process.hrtime.bigint();
    await fs.writeJson(testFile, testData);
    const writeEnd = process.hrtime.bigint();

    // 读取性能测试
    const readStart = process.hrtime.bigint();
    await fs.readJson(testFile);
    const readEnd = process.hrtime.bigint();

    // 清理测试文件
    await fs.remove(testFile);

    return {
      writeTime: Number(writeEnd - writeStart) / 1000000,
      readTime: Number(readEnd - readStart) / 1000000
    };
  }

  async measureDatabasePerformance() {
    const tasksFile = path.join(process.cwd(), '.taskmaster', 'tasks.json');

    if (!await fs.pathExists(tasksFile)) {
      return { error: 'Tasks file not found' };
    }

    // 测量任务文件加载时间
    const loadStart = process.hrtime.bigint();
    const tasks = await getAllTasks();
    const loadEnd = process.hrtime.bigint();

    // 测量任务查询性能
    const queryStart = process.hrtime.bigint();
    const pendingTasks = tasks.filter(t => t.status === 'pending');
    const queryEnd = process.hrtime.bigint();

    return {
      loadTime: Number(loadEnd - loadStart) / 1000000,
      queryTime: Number(queryEnd - queryStart) / 1000000,
      taskCount: tasks.length,
      fileSize: (await fs.stat(tasksFile)).size
    };
  }

  async measureAIPerformance() {
    const aiProviders = await getConfiguredAIProviders();
    const performance = {};

    for (const provider of aiProviders) {
      try {
        const start = process.hrtime.bigint();
        await callAI(provider.name, 'Test prompt for performance measurement');
        const end = process.hrtime.bigint();

        performance[provider.name] = {
          responseTime: Number(end - start) / 1000000,
          status: 'available'
        };
      } catch (error) {
        performance[provider.name] = {
          responseTime: null,
          status: 'error',
          error: error.message
        };
      }
    }

    return performance;
  }
}

class CachingOptimization {
  constructor() {
    this.name = 'Caching Optimization';
    this.cache = new Map();
  }

  isApplicable(metrics, options) {
    // 如果命令执行时间较慢，启用缓存优化
    const avgCommandTime = Object.values(metrics.timing).reduce(
      (sum, timing) => sum + timing.average, 0
    ) / Object.keys(metrics.timing).length;

    return avgCommandTime > 500; // 500ms
  }

  async apply(metrics, options) {
    try {
      // 实现智能缓存策略
      await this.implementTaskCache();
      await this.implementDependencyCache();
      await this.implementAnalysisCache();

      return {
        success: true,
        description: 'Implemented intelligent caching for tasks, dependencies, and analysis results',
        details: {
          taskCacheEnabled: true,
          dependencyCacheEnabled: true,
          analysisCacheEnabled: true,
          cacheSize: this.cache.size
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async implementTaskCache() {
    // 缓存任务数据，带有智能失效策略
    const originalGetAllTasks = getAllTasks;

    getAllTasks = async () => {
      const cacheKey = 'all_tasks';
      const cached = this.cache.get(cacheKey);

      if (cached && this.isCacheValid(cached)) {
        return cached.data;
      }

      const tasks = await originalGetAllTasks();
      this.cache.set(cacheKey, {
        data: tasks,
        timestamp: Date.now(),
        ttl: 30000 // 30秒
      });

      return tasks;
    };
  }

  async implementDependencyCache() {
    // 缓存依赖图计算结果
    const originalGetDependencyGraph = getDependencyGraph;

    getDependencyGraph = async () => {
      const cacheKey = 'dependency_graph';
      const cached = this.cache.get(cacheKey);

      if (cached && this.isCacheValid(cached)) {
        return cached.data;
      }

      const graph = await originalGetDependencyGraph();
      this.cache.set(cacheKey, {
        data: graph,
        timestamp: Date.now(),
        ttl: 60000 // 1分钟
      });

      return graph;
    };
  }

  isCacheValid(cached) {
    return Date.now() - cached.timestamp < cached.ttl;
  }
}
```

**团队协作模式分析：**
```javascript
class TeamCollaborationAnalyzer {
  constructor() {
    this.collaborationPatterns = new Map();
    this.communicationAnalyzer = new CommunicationAnalyzer();
    this.workloadAnalyzer = new WorkloadAnalyzer();
  }

  async analyzeTeamCollaboration(options = {}) {
    const analysis = {
      timestamp: new Date().toISOString(),
      teamMetrics: await this.collectTeamMetrics(),
      collaborationPatterns: await this.analyzeCollaborationPatterns(),
      communicationEffectiveness: await this.analyzeCommunicationEffectiveness(),
      workloadDistribution: await this.analyzeWorkloadDistribution(),
      knowledgeSharing: await this.analyzeKnowledgeSharing(),
      bottlenecks: await this.identifyTeamBottlenecks(),
      recommendations: []
    };

    analysis.recommendations = await this.generateTeamRecommendations(analysis);

    return analysis;
  }

  async collectTeamMetrics() {
    const tasks = await getAllTasks();
    const teamMembers = this.extractTeamMembers(tasks);

    const metrics = {
      teamSize: teamMembers.length,
      members: [],
      overallProductivity: 0,
      collaborationScore: 0,
      knowledgeDistribution: 0
    };

    for (const member of teamMembers) {
      const memberTasks = tasks.filter(t => t.assignee === member);
      const memberMetrics = {
        name: member,
        tasksAssigned: memberTasks.length,
        tasksCompleted: memberTasks.filter(t => t.status === 'done').length,
        averageComplexity: this.calculateAverageComplexity(memberTasks),
        specializations: this.identifySpecializations(memberTasks),
        collaborationFrequency: await this.calculateCollaborationFrequency(member, tasks),
        productivityScore: this.calculateProductivityScore(memberTasks),
        knowledgeAreas: this.identifyKnowledgeAreas(memberTasks)
      };

      metrics.members.push(memberMetrics);
    }

    metrics.overallProductivity = this.calculateOverallProductivity(metrics.members);
    metrics.collaborationScore = this.calculateCollaborationScore(metrics.members);
    metrics.knowledgeDistribution = this.calculateKnowledgeDistribution(metrics.members);

    return metrics;
  }

  async analyzeCollaborationPatterns() {
    const tasks = await getAllTasks();
    const patterns = {
      pairProgramming: await this.detectPairProgramming(tasks),
      knowledgeTransfer: await this.detectKnowledgeTransfer(tasks),
      crossFunctionalWork: await this.detectCrossFunctionalWork(tasks),
      mentoring: await this.detectMentoringPatterns(tasks),
      codeReview: await this.detectCodeReviewPatterns(tasks)
    };

    return patterns;
  }

  async detectPairProgramming(tasks) {
    const pairTasks = tasks.filter(task =>
      task.assignee && task.assignee.includes(',') ||
      task.collaborators && task.collaborators.length > 0
    );

    const patterns = {
      frequency: pairTasks.length / tasks.length,
      commonPairs: this.identifyCommonPairs(pairTasks),
      effectiveness: this.measurePairEffectiveness(pairTasks),
      preferredTaskTypes: this.analyzePairTaskTypes(pairTasks)
    };

    return patterns;
  }

  async detectKnowledgeTransfer(tasks) {
    const transferIndicators = {
      documentationTasks: tasks.filter(t => t.type === 'documentation'),
      trainingTasks: tasks.filter(t => t.title.toLowerCase().includes('training')),
      onboardingTasks: tasks.filter(t => t.title.toLowerCase().includes('onboard')),
      knowledgeBaseTasks: tasks.filter(t => t.title.toLowerCase().includes('knowledge'))
    };

    const transferScore = Object.values(transferIndicators).reduce(
      (sum, tasks) => sum + tasks.length, 0
    ) / tasks.length;

    return {
      transferScore,
      indicators: transferIndicators,
      knowledgeGaps: await this.identifyKnowledgeGaps(tasks),
      transferOpportunities: await this.identifyTransferOpportunities(tasks)
    };
  }

  async identifyTeamBottlenecks() {
    const teamMetrics = await this.collectTeamMetrics();
    const bottlenecks = [];

    // 工作负载不平衡
    const workloads = teamMetrics.members.map(m => m.tasksAssigned);
    const avgWorkload = workloads.reduce((sum, w) => sum + w, 0) / workloads.length;
    const workloadVariance = this.calculateVariance(workloads, avgWorkload);

    if (workloadVariance > avgWorkload * 0.5) {
      bottlenecks.push({
        type: 'workload_imbalance',
        severity: 'high',
        description: 'Significant workload imbalance detected',
        affectedMembers: teamMetrics.members.filter(m =>
          Math.abs(m.tasksAssigned - avgWorkload) > avgWorkload * 0.3
        ),
        recommendation: 'Redistribute tasks to balance workload'
      });
    }

    // 知识孤岛
    const knowledgeAreas = new Map();
    teamMetrics.members.forEach(member => {
      member.knowledgeAreas.forEach(area => {
        if (!knowledgeAreas.has(area)) {
          knowledgeAreas.set(area, []);
        }
        knowledgeAreas.get(area).push(member.name);
      });
    });

    const singlePersonAreas = Array.from(knowledgeAreas.entries())
      .filter(([area, experts]) => experts.length === 1);

    if (singlePersonAreas.length > 0) {
      bottlenecks.push({
        type: 'knowledge_silos',
        severity: 'medium',
        description: `${singlePersonAreas.length} knowledge areas have only one expert`,
        affectedAreas: singlePersonAreas.map(([area, experts]) => ({ area, expert: experts[0] })),
        recommendation: 'Implement knowledge sharing sessions and cross-training'
      });
    }

    // 协作频率低
    const lowCollaborationMembers = teamMetrics.members.filter(m =>
      m.collaborationFrequency < 0.2
    );

    if (lowCollaborationMembers.length > 0) {
      bottlenecks.push({
        type: 'low_collaboration',
        severity: 'medium',
        description: `${lowCollaborationMembers.length} team members have low collaboration frequency`,
        affectedMembers: lowCollaborationMembers,
        recommendation: 'Encourage pair programming and collaborative tasks'
      });
    }

    return bottlenecks;
  }

  async generateTeamRecommendations(analysis) {
    const recommendations = [];

    // 基于工作负载分布的建议
    if (analysis.workloadDistribution.imbalanceScore > 0.3) {
      recommendations.push({
        category: 'workload_management',
        priority: 'high',
        title: 'Rebalance Team Workload',
        description: 'Current workload distribution shows significant imbalance',
        actions: [
          'Redistribute high-complexity tasks from overloaded members',
          'Assign more tasks to underutilized team members',
          'Consider task complexity when making assignments'
        ],
        expectedImpact: 'Improved team productivity and reduced burnout risk'
      });
    }

    // 基于协作模式的建议
    if (analysis.collaborationPatterns.pairProgramming.frequency < 0.1) {
      recommendations.push({
        category: 'collaboration',
        priority: 'medium',
        title: 'Increase Pair Programming',
        description: 'Low pair programming frequency detected',
        actions: [
          'Schedule regular pair programming sessions',
          'Identify complex tasks suitable for pair work',
          'Rotate pairs to spread knowledge'
        ],
        expectedImpact: 'Better code quality and knowledge sharing'
      });
    }

    // 基于知识分享的建议
    if (analysis.knowledgeSharing.transferScore < 0.15) {
      recommendations.push({
        category: 'knowledge_management',
        priority: 'high',
        title: 'Improve Knowledge Sharing',
        description: 'Limited knowledge transfer activities detected',
        actions: [
          'Create documentation tasks for complex implementations',
          'Schedule regular knowledge sharing sessions',
          'Implement code review requirements',
          'Create onboarding tasks for new technologies'
        ],
        expectedImpact: 'Reduced knowledge silos and improved team resilience'
      });
    }

    return recommendations;
  }

  calculateVariance(values, mean) {
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
}
```

**扩展性和集成分析：**
```javascript
class ScalabilityAnalyzer {
  constructor() {
    this.scalabilityMetrics = new Map();
    this.integrationPoints = new Map();
  }

  async analyzeScalability(options = {}) {
    const analysis = {
      currentScale: await this.assessCurrentScale(),
      scalabilityLimits: await this.identifyScalabilityLimits(),
      growthProjections: await this.projectGrowth(options.timeHorizon || 12),
      integrationReadiness: await this.assessIntegrationReadiness(),
      recommendations: []
    };

    analysis.recommendations = await this.generateScalabilityRecommendations(analysis);

    return analysis;
  }

  async assessCurrentScale() {
    const tasks = await getAllTasks();
    const teamMetrics = await this.getTeamMetrics();
    const systemMetrics = await this.getSystemMetrics();

    return {
      taskVolume: {
        total: tasks.length,
        active: tasks.filter(t => ['pending', 'in-progress'].includes(t.status)).length,
        complexity: this.calculateComplexityDistribution(tasks),
        growthRate: await this.calculateTaskGrowthRate()
      },
      teamScale: {
        size: teamMetrics.size,
        productivity: teamMetrics.averageProductivity,
        specialization: teamMetrics.specializationIndex,
        collaborationComplexity: this.calculateCollaborationComplexity(teamMetrics)
      },
      systemScale: {
        dataSize: systemMetrics.totalDataSize,
        responseTime: systemMetrics.averageResponseTime,
        memoryUsage: systemMetrics.memoryUsage,
        concurrentUsers: systemMetrics.estimatedConcurrentUsers
      }
    };
  }

  async identifyScalabilityLimits() {
    const limits = {
      technical: await this.identifyTechnicalLimits(),
      organizational: await this.identifyOrganizationalLimits(),
      process: await this.identifyProcessLimits()
    };

    return limits;
  }

  async identifyTechnicalLimits() {
    const limits = [];

    // 文件系统限制
    const tasksFileSize = await this.getTasksFileSize();
    if (tasksFileSize > 10 * 1024 * 1024) { // 10MB
      limits.push({
        type: 'file_size_limit',
        current: tasksFileSize,
        limit: 50 * 1024 * 1024, // 50MB practical limit
        severity: 'medium',
        description: 'Tasks file approaching size limits for efficient processing',
        solution: 'Implement task archiving or database migration'
      });
    }

    // 内存限制
    const memoryUsage = process.memoryUsage();
    if (memoryUsage.heapUsed > 100 * 1024 * 1024) { // 100MB
      limits.push({
        type: 'memory_limit',
        current: memoryUsage.heapUsed,
        limit: 512 * 1024 * 1024, // 512MB practical limit
        severity: 'high',
        description: 'High memory usage may impact performance at scale',
        solution: 'Implement data pagination and lazy loading'
      });
    }

    // 依赖图复杂度限制
    const dependencyGraph = await getDependencyGraph();
    const graphComplexity = this.calculateGraphComplexity(dependencyGraph);
    if (graphComplexity > 1000) {
      limits.push({
        type: 'graph_complexity_limit',
        current: graphComplexity,
        limit: 5000, // Practical limit for graph algorithms
        severity: 'medium',
        description: 'Dependency graph complexity may slow down analysis',
        solution: 'Implement graph optimization and caching strategies'
      });
    }

    return limits;
  }

  async projectGrowth(monthsAhead) {
    const historicalData = await this.getHistoricalGrowthData();
    const currentMetrics = await this.assessCurrentScale();

    const projections = {
      taskVolume: this.projectTaskGrowth(historicalData, monthsAhead),
      teamSize: this.projectTeamGrowth(historicalData, monthsAhead),
      systemLoad: this.projectSystemLoad(currentMetrics, monthsAhead),
      scalingPoints: []
    };

    // 识别关键扩展点
    projections.scalingPoints = this.identifyScalingPoints(projections);

    return projections;
  }

  identifyScalingPoints(projections) {
    const scalingPoints = [];

    // 任务量扩展点
    if (projections.taskVolume.projected > 1000) {
      scalingPoints.push({
        type: 'task_volume',
        threshold: 1000,
        projectedMonth: this.findThresholdMonth(projections.taskVolume, 1000),
        impact: 'Performance degradation in task operations',
        preparation: [
          'Implement task archiving',
          'Optimize data structures',
          'Consider database migration'
        ]
      });
    }

    // 团队规模扩展点
    if (projections.teamSize.projected > 20) {
      scalingPoints.push({
        type: 'team_size',
        threshold: 20,
        projectedMonth: this.findThresholdMonth(projections.teamSize, 20),
        impact: 'Communication overhead and coordination complexity',
        preparation: [
          'Implement team hierarchies',
          'Create specialized roles',
          'Enhance collaboration tools'
        ]
      });
    }

    return scalingPoints;
  }

  async generateScalabilityRecommendations(analysis) {
    const recommendations = [];

    // 基于当前规模的建议
    if (analysis.currentScale.taskVolume.total > 500) {
      recommendations.push({
        category: 'data_management',
        priority: 'high',
        title: 'Implement Task Archiving',
        description: 'Large number of tasks may impact performance',
        implementation: {
          effort: 'medium',
          timeline: '2-3 weeks',
          steps: [
            'Design archiving strategy',
            'Implement archive functionality',
            'Create archive management tools',
            'Migrate old completed tasks'
          ]
        },
        benefits: [
          'Improved performance',
          'Reduced memory usage',
          'Better data organization'
        ]
      });
    }

    // 基于增长预测的建议
    for (const scalingPoint of analysis.growthProjections.scalingPoints) {
      if (scalingPoint.projectedMonth <= 6) { // 6个月内
        recommendations.push({
          category: 'proactive_scaling',
          priority: 'high',
          title: `Prepare for ${scalingPoint.type} scaling`,
          description: `${scalingPoint.type} threshold will be reached in ${scalingPoint.projectedMonth} months`,
          implementation: {
            effort: 'high',
            timeline: `${scalingPoint.projectedMonth - 1} months`,
            steps: scalingPoint.preparation
          },
          urgency: scalingPoint.projectedMonth <= 3 ? 'critical' : 'high'
        });
      }
    }

    return recommendations;
  }

  async getHistoricalGrowthData() {
    // 从历史记录中提取增长数据
    const historyPath = path.join(process.cwd(), '.taskmaster', 'history');
    const growthData = {
      taskCreationRate: [],
      teamGrowthRate: [],
      complexityTrends: [],
      performanceMetrics: []
    };

    if (await fs.pathExists(historyPath)) {
      const historyFiles = await fs.readdir(historyPath);

      for (const file of historyFiles.sort()) {
        if (file.endsWith('.json')) {
          const data = await fs.readJson(path.join(historyPath, file));
          growthData.taskCreationRate.push({
            date: data.date,
            count: data.tasksCreated || 0
          });
          growthData.teamGrowthRate.push({
            date: data.date,
            size: data.teamSize || 0
          });
        }
      }
    }

    return growthData;
  }

  projectTaskGrowth(historicalData, monthsAhead) {
    const taskData = historicalData.taskCreationRate;
    if (taskData.length < 2) {
      return { projected: 0, confidence: 'low' };
    }

    // 简单线性回归预测
    const growthRate = this.calculateGrowthRate(taskData);
    const currentCount = taskData[taskData.length - 1].count;

    return {
      current: currentCount,
      projected: Math.round(currentCount + (growthRate * monthsAhead)),
      growthRate: growthRate,
      confidence: taskData.length > 6 ? 'high' : 'medium'
    };
  }

  calculateGrowthRate(dataPoints) {
    if (dataPoints.length < 2) return 0;

    const n = dataPoints.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    dataPoints.forEach((point, index) => {
      sumX += index;
      sumY += point.count;
      sumXY += index * point.count;
      sumXX += index * index;
    });

    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }
}

#### 性能基准测试方法论

class PerformanceBenchmarkSuite {
  constructor() {
    this.benchmarks = new Map();
    this.baselineMetrics = null;
  }

  async runComprehensiveBenchmark(options = {}) {
    console.log('🚀 Starting comprehensive performance benchmark...\n');

    const benchmark = {
      timestamp: new Date().toISOString(),
      environment: await this.collectEnvironmentInfo(),
      baseline: await this.establishBaseline(),
      tests: [],
      summary: {}
    };

    // 核心性能测试套件
    const testSuites = [
      new CommandExecutionBenchmark(),
      new DataOperationsBenchmark(),
      new AIProviderBenchmark(),
      new ScalabilityBenchmark(),
      new MemoryUsageBenchmark()
    ];

    for (const suite of testSuites) {
      console.log(`Running ${suite.name}...`);
      const result = await suite.run(options);
      benchmark.tests.push(result);
      console.log(`  ✅ Completed in ${result.duration}ms\n`);
    }

    benchmark.summary = this.generateBenchmarkSummary(benchmark.tests);

    // 保存基准测试结果
    await this.saveBenchmarkResults(benchmark);

    return benchmark;
  }

  async establishBaseline() {
    if (this.baselineMetrics) {
      return this.baselineMetrics;
    }

    const baseline = {
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuCount: require('os').cpus().length,
        totalMemory: require('os').totalmem(),
        freeMemory: require('os').freemem()
      },
      taskMasterVersion: await this.getTaskMasterVersion(),
      projectSize: await this.getProjectSize(),
      initialMemory: process.memoryUsage()
    };

    this.baselineMetrics = baseline;
    return baseline;
  }

  async getProjectSize() {
    const tasksPath = path.join(process.cwd(), '.taskmaster', 'tasks.json');
    const configPath = path.join(process.cwd(), '.taskmaster', 'config.json');

    const size = {
      taskCount: 0,
      fileSize: 0,
      dependencyCount: 0
    };

    if (await fs.pathExists(tasksPath)) {
      const tasks = await fs.readJson(tasksPath);
      size.taskCount = Array.isArray(tasks) ? tasks.length : Object.keys(tasks).length;
      size.fileSize = (await fs.stat(tasksPath)).size;
      size.dependencyCount = tasks.reduce((sum, task) =>
        sum + (task.dependencies ? task.dependencies.length : 0), 0
      );
    }

    return size;
  }
}

class CommandExecutionBenchmark {
  constructor() {
    this.name = 'Command Execution Benchmark';
  }

  async run(options) {
    const startTime = Date.now();
    const results = {
      name: this.name,
      tests: [],
      summary: {}
    };

    // 测试常用命令的执行时间
    const commands = [
      'status',
      'list',
      'list pending',
      'list in-progress',
      'show 1',
      'analyze-complexity',
      'validate-dependencies',
      'next'
    ];

    for (const command of commands) {
      const commandResult = await this.benchmarkCommand(command, options.iterations || 10);
      results.tests.push(commandResult);
    }

    results.summary = this.calculateCommandSummary(results.tests);
    results.duration = Date.now() - startTime;

    return results;
  }

  async benchmarkCommand(command, iterations) {
    const times = [];
    const memoryUsages = [];

    for (let i = 0; i < iterations; i++) {
      // 垃圾回收以获得一致的内存测量
      if (global.gc) {
        global.gc();
      }

      const startMemory = process.memoryUsage();
      const startTime = process.hrtime.bigint();

      try {
        await taskMasterCLI.execute(command);
      } catch (error) {
        // 记录错误但继续测试
        console.warn(`Command "${command}" failed: ${error.message}`);
      }

      const endTime = process.hrtime.bigint();
      const endMemory = process.memoryUsage();

      times.push(Number(endTime - startTime) / 1000000); // 转换为毫秒
      memoryUsages.push(endMemory.heapUsed - startMemory.heapUsed);
    }

    return {
      command,
      iterations,
      times: {
        min: Math.min(...times),
        max: Math.max(...times),
        average: times.reduce((sum, time) => sum + time, 0) / times.length,
        median: this.calculateMedian(times),
        p95: this.calculatePercentile(times, 95),
        p99: this.calculatePercentile(times, 99)
      },
      memory: {
        averageDelta: memoryUsages.reduce((sum, usage) => sum + usage, 0) / memoryUsages.length,
        maxDelta: Math.max(...memoryUsages)
      }
    };
  }

  calculateMedian(values) {
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }

  calculatePercentile(values, percentile) {
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }
}

class ScalabilityBenchmark {
  constructor() {
    this.name = 'Scalability Benchmark';
  }

  async run(options) {
    const startTime = Date.now();
    const results = {
      name: this.name,
      tests: [],
      summary: {}
    };

    // 测试不同规模下的性能
    const scales = [10, 50, 100, 500, 1000];

    for (const scale of scales) {
      console.log(`  Testing with ${scale} tasks...`);
      const scaleResult = await this.benchmarkScale(scale);
      results.tests.push(scaleResult);
    }

    results.summary = this.analyzeScalabilityTrends(results.tests);
    results.duration = Date.now() - startTime;

    return results;
  }

  async benchmarkScale(taskCount) {
    // 创建测试数据
    const testTasks = this.generateTestTasks(taskCount);
    const tempTasksFile = path.join(process.cwd(), '.taskmaster', 'tasks_test.json');

    try {
      // 备份原始任务文件
      const originalTasksFile = path.join(process.cwd(), '.taskmaster', 'tasks.json');
      const hasOriginal = await fs.pathExists(originalTasksFile);
      let originalTasks = null;

      if (hasOriginal) {
        originalTasks = await fs.readJson(originalTasksFile);
      }

      // 写入测试数据
      await fs.writeJson(originalTasksFile, testTasks);

      // 执行性能测试
      const testResults = {
        taskCount,
        operations: {}
      };

      // 测试各种操作
      testResults.operations.list = await this.timeOperation(() =>
        taskMasterCLI.execute('list')
      );

      testResults.operations.status = await this.timeOperation(() =>
        taskMasterCLI.execute('status')
      );

      testResults.operations.complexityAnalysis = await this.timeOperation(() =>
        taskMasterCLI.execute('analyze-complexity')
      );

      testResults.operations.dependencyValidation = await this.timeOperation(() =>
        taskMasterCLI.execute('validate-dependencies')
      );

      // 恢复原始数据
      if (hasOriginal && originalTasks) {
        await fs.writeJson(originalTasksFile, originalTasks);
      } else if (!hasOriginal) {
        await fs.remove(originalTasksFile);
      }

      return testResults;

    } catch (error) {
      console.error(`Scalability test failed for ${taskCount} tasks:`, error.message);
      return {
        taskCount,
        error: error.message
      };
    }
  }

  generateTestTasks(count) {
    const tasks = [];
    const priorities = ['low', 'medium', 'high'];
    const statuses = ['pending', 'in-progress', 'done', 'review'];

    for (let i = 1; i <= count; i++) {
      tasks.push({
        id: i,
        title: `Test Task ${i}`,
        description: `Generated test task for scalability testing`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        complexity: Math.floor(Math.random() * 10) + 1,
        estimatedHours: Math.floor(Math.random() * 20) + 1,
        dependencies: this.generateRandomDependencies(i, count),
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        assignee: `user${Math.floor(Math.random() * 5) + 1}`
      });
    }

    return tasks;
  }

  generateRandomDependencies(taskId, totalTasks) {
    const dependencies = [];
    const maxDeps = Math.min(3, taskId - 1); // 最多3个依赖，且只能依赖之前的任务
    const depCount = Math.floor(Math.random() * (maxDeps + 1));

    for (let i = 0; i < depCount; i++) {
      const depId = Math.floor(Math.random() * taskId) + 1;
      if (depId !== taskId && !dependencies.includes(depId)) {
        dependencies.push(depId);
      }
    }

    return dependencies;
  }

  async timeOperation(operation) {
    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();

    try {
      await operation();

      const endTime = process.hrtime.bigint();
      const endMemory = process.memoryUsage();

      return {
        duration: Number(endTime - startTime) / 1000000, // 毫秒
        memoryDelta: endMemory.heapUsed - startMemory.heapUsed,
        success: true
      };
    } catch (error) {
      const endTime = process.hrtime.bigint();
      return {
        duration: Number(endTime - startTime) / 1000000,
        memoryDelta: 0,
        success: false,
        error: error.message
      };
    }
  }

  analyzeScalabilityTrends(tests) {
    const summary = {
      linearityScore: {},
      performanceDegradation: {},
      memoryGrowth: {},
      recommendations: []
    };

    // 分析每个操作的线性度
    const operations = ['list', 'status', 'complexityAnalysis', 'dependencyValidation'];

    for (const operation of operations) {
      const dataPoints = tests.map(test => ({
        taskCount: test.taskCount,
        duration: test.operations[operation]?.duration || 0
      })).filter(point => point.duration > 0);

      if (dataPoints.length >= 3) {
        summary.linearityScore[operation] = this.calculateLinearity(dataPoints);
        summary.performanceDegradation[operation] = this.calculateDegradation(dataPoints);
      }
    }

    // 生成建议
    summary.recommendations = this.generateScalabilityRecommendations(summary);

    return summary;
  }

  calculateLinearity(dataPoints) {
    // 计算 R² 值来评估线性度
    const n = dataPoints.length;
    const sumX = dataPoints.reduce((sum, point) => sum + point.taskCount, 0);
    const sumY = dataPoints.reduce((sum, point) => sum + point.duration, 0);
    const sumXY = dataPoints.reduce((sum, point) => sum + point.taskCount * point.duration, 0);
    const sumXX = dataPoints.reduce((sum, point) => sum + point.taskCount * point.taskCount, 0);
    const sumYY = dataPoints.reduce((sum, point) => sum + point.duration * point.duration, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    if (denominator === 0) return 0;

    const r = numerator / denominator;
    return r * r; // R²
  }

  calculateDegradation(dataPoints) {
    if (dataPoints.length < 2) return 0;

    const first = dataPoints[0];
    const last = dataPoints[dataPoints.length - 1];

    // 计算每个任务的平均处理时间变化
    const firstRate = first.duration / first.taskCount;
    const lastRate = last.duration / last.taskCount;

    return (lastRate - firstRate) / firstRate;
  }
}

## 综合故障排除指南

### 常见问题诊断程序

#### 问题分类和诊断流程

```javascript
class TroubleshootingGuide {
  constructor() {
    this.diagnosticTree = this.buildDiagnosticTree();
    this.errorCodes = this.initializeErrorCodes();
    this.solutionDatabase = this.buildSolutionDatabase();
  }

  async diagnoseIssue(symptoms, context = {}) {
    const diagnosis = {
      symptoms,
      context,
      possibleCauses: [],
      recommendedActions: [],
      diagnosticSteps: [],
      severity: 'unknown'
    };

    // 症状分析
    const symptomAnalysis = await this.analyzeSymptoms(symptoms);
    diagnosis.possibleCauses = symptomAnalysis.causes;
    diagnosis.severity = symptomAnalysis.severity;

    // 生成诊断步骤
    diagnosis.diagnosticSteps = await this.generateDiagnosticSteps(symptomAnalysis, context);

    // 推荐解决方案
    diagnosis.recommendedActions = await this.recommendSolutions(diagnosis);

    return diagnosis;
  }

  buildDiagnosticTree() {
    return {
      'command_fails': {
        symptoms: ['command not found', 'permission denied', 'syntax error'],
        causes: ['installation_issue', 'path_issue', 'permission_issue'],
        diagnostics: ['check_installation', 'check_path', 'check_permissions']
      },
      'slow_performance': {
        symptoms: ['commands take long time', 'high memory usage', 'system lag'],
        causes: ['large_dataset', 'memory_leak', 'disk_io_bottleneck'],
        diagnostics: ['check_data_size', 'monitor_memory', 'check_disk_io']
      },
      'ai_provider_issues': {
        symptoms: ['ai calls fail', 'timeout errors', 'invalid responses'],
        causes: ['api_key_invalid', 'network_issues', 'rate_limiting'],
        diagnostics: ['validate_api_keys', 'test_connectivity', 'check_rate_limits']
      },
      'data_corruption': {
        symptoms: ['invalid json', 'missing tasks', 'dependency errors'],
        causes: ['file_corruption', 'concurrent_access', 'incomplete_writes'],
        diagnostics: ['validate_json', 'check_file_locks', 'restore_backup']
      }
    };
  }

  initializeErrorCodes() {
    return {
      'TM001': {
        message: 'Task Master not initialized',
        description: 'The current directory does not contain a Task Master project',
        causes: [
          'Running commands outside a Task Master project directory',
          'Missing .taskmaster directory',
          'Corrupted project configuration'
        ],
        solutions: [
          {
            title: 'Initialize new project',
            command: 'task-master init',
            description: 'Create a new Task Master project in current directory'
          },
          {
            title: 'Navigate to existing project',
            command: 'cd /path/to/project',
            description: 'Change to directory containing .taskmaster folder'
          }
        ],
        severity: 'high'
      },

      'TM002': {
        message: 'Configuration file corrupted',
        description: 'The .taskmaster/config.json file is invalid or corrupted',
        causes: [
          'Invalid JSON syntax in config file',
          'Incomplete file write operation',
          'Manual editing errors'
        ],
        solutions: [
          {
            title: 'Restore from backup',
            command: 'cp .taskmaster/backup/config-*.json .taskmaster/config.json',
            description: 'Restore configuration from automatic backup'
          },
          {
            title: 'Reinitialize project',
            command: 'task-master init --force',
            description: 'Recreate configuration (will lose custom settings)'
          },
          {
            title: 'Manual repair',
            description: 'Edit .taskmaster/config.json to fix JSON syntax errors',
            example: `{
  "name": "My Project",
  "version": "1.0.0",
  "created": "2024-01-15T10:00:00Z",
  "aiProviders": []
}`
          }
        ],
        severity: 'high'
      },

      'TM003': {
        message: 'AI Provider authentication failed',
        description: 'Unable to authenticate with configured AI provider',
        causes: [
          'Invalid or expired API key',
          'Incorrect API endpoint configuration',
          'Network connectivity issues',
          'API service temporarily unavailable'
        ],
        solutions: [
          {
            title: 'Verify API key',
            command: 'task-master models --verify',
            description: 'Test API key validity'
          },
          {
            title: 'Reconfigure providers',
            command: 'task-master models --setup',
            description: 'Set up AI providers with new credentials'
          },
          {
            title: 'Check network connectivity',
            command: 'curl -I https://api.anthropic.com',
            description: 'Test network access to AI provider'
          }
        ],
        severity: 'medium'
      },

      'TM004': {
        message: 'Circular dependency detected',
        description: 'Task dependencies form a circular reference',
        causes: [
          'Incorrect dependency assignment',
          'Complex dependency chains',
          'Data import errors'
        ],
        solutions: [
          {
            title: 'Auto-fix dependencies',
            command: 'task-master fix-dependencies',
            description: 'Automatically resolve circular dependencies'
          },
          {
            title: 'Manual dependency review',
            command: 'task-master validate-dependencies --verbose',
            description: 'Get detailed dependency analysis'
          },
          {
            title: 'Visualize dependency graph',
            command: 'task-master utils/analyze dependencies',
            description: 'Generate dependency visualization'
          }
        ],
        severity: 'medium'
      },

      'TM005': {
        message: 'Memory usage exceeded limits',
        description: 'Task Master is consuming excessive memory',
        causes: [
          'Large number of tasks (>1000)',
          'Memory leak in processing',
          'Inefficient data structures',
          'Concurrent operations'
        ],
        solutions: [
          {
            title: 'Archive old tasks',
            command: 'task-master utils/archive --older-than=30d',
            description: 'Move completed tasks to archive'
          },
          {
            title: 'Restart Task Master',
            command: 'task-master restart',
            description: 'Clear memory and restart service'
          },
          {
            title: 'Enable memory optimization',
            command: 'task-master config --set memory.optimization=true',
            description: 'Enable memory-efficient processing mode'
          }
        ],
        severity: 'high'
      }
    };
  }
}
```

#### 步骤化诊断程序

**1. 安装和配置问题诊断：**
```bash
#!/bin/bash
# Task Master 诊断脚本

echo "🔍 Task Master 诊断工具"
echo "========================"

# 检查 Node.js 安装
echo "1. 检查 Node.js 安装..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "   ✅ Node.js 已安装: $NODE_VERSION"

    # 检查版本兼容性
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$MAJOR_VERSION" -lt 16 ]; then
        echo "   ⚠️  警告: Node.js 版本过低，建议升级到 16+ 版本"
    fi
else
    echo "   ❌ Node.js 未安装"
    echo "   💡 解决方案: 安装 Node.js 16+ 版本"
    echo "      - 访问 https://nodejs.org"
    echo "      - 或使用 nvm: curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
    exit 1
fi

# 检查 npm 安装
echo "2. 检查 npm 安装..."
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo "   ✅ npm 已安装: $NPM_VERSION"
else
    echo "   ❌ npm 未安装"
    echo "   💡 解决方案: npm 通常与 Node.js 一起安装，请重新安装 Node.js"
    exit 1
fi

# 检查 Task Master 安装
echo "3. 检查 Task Master 安装..."
if command -v task-master &> /dev/null; then
    TM_VERSION=$(task-master --version 2>/dev/null || echo "unknown")
    echo "   ✅ Task Master 已安装: $TM_VERSION"
else
    echo "   ❌ Task Master 未安装"
    echo "   💡 解决方案: npm install -g task-master-ai"
    exit 1
fi

# 检查项目初始化
echo "4. 检查项目初始化..."
if [ -d ".taskmaster" ]; then
    echo "   ✅ Task Master 项目已初始化"

    # 检查配置文件
    if [ -f ".taskmaster/config.json" ]; then
        echo "   ✅ 配置文件存在"

        # 验证 JSON 格式
        if jq empty .taskmaster/config.json 2>/dev/null; then
            echo "   ✅ 配置文件格式正确"
        else
            echo "   ❌ 配置文件格式错误"
            echo "   💡 解决方案: task-master init --force"
        fi
    else
        echo "   ❌ 配置文件缺失"
        echo "   💡 解决方案: task-master init"
    fi

    # 检查任务文件
    if [ -f ".taskmaster/tasks.json" ]; then
        echo "   ✅ 任务文件存在"
        TASK_COUNT=$(jq length .taskmaster/tasks.json 2>/dev/null || echo "0")
        echo "   📊 任务数量: $TASK_COUNT"
    else
        echo "   ⚠️  任务文件不存在（这是正常的，如果项目刚初始化）"
    fi
else
    echo "   ❌ 项目未初始化"
    echo "   💡 解决方案: task-master init"
fi

# 检查 AI 提供商配置
echo "5. 检查 AI 提供商配置..."
if [ -f ".taskmaster/config.json" ]; then
    AI_PROVIDERS=$(jq -r '.aiProviders // [] | length' .taskmaster/config.json 2>/dev/null || echo "0")
    if [ "$AI_PROVIDERS" -gt 0 ]; then
        echo "   ✅ AI 提供商已配置 ($AI_PROVIDERS 个)"
    else
        echo "   ⚠️  未配置 AI 提供商"
        echo "   💡 解决方案: task-master models --setup"
    fi
fi

# 检查权限
echo "6. 检查文件权限..."
if [ -w ".taskmaster" ]; then
    echo "   ✅ 目录权限正常"
else
    echo "   ❌ 目录权限不足"
    echo "   💡 解决方案: chmod -R 755 .taskmaster"
fi

# 性能检查
echo "7. 性能检查..."
if [ -f ".taskmaster/tasks.json" ]; then
    FILE_SIZE=$(stat -f%z .taskmaster/tasks.json 2>/dev/null || stat -c%s .taskmaster/tasks.json 2>/dev/null || echo "0")
    if [ "$FILE_SIZE" -gt 1048576 ]; then  # 1MB
        echo "   ⚠️  任务文件较大 ($(($FILE_SIZE / 1024))KB)"
        echo "   💡 建议: 考虑归档旧任务以提高性能"
    else
        echo "   ✅ 文件大小正常"
    fi
fi

echo ""
echo "🎉 诊断完成！"
```

**2. 性能问题诊断：**
```javascript
class PerformanceDiagnostic {
  async diagnosePerformanceIssues() {
    console.log('🔍 性能问题诊断开始...\n');

    const diagnosis = {
      issues: [],
      recommendations: [],
      metrics: {}
    };

    // 1. 内存使用分析
    const memoryAnalysis = await this.analyzeMemoryUsage();
    diagnosis.metrics.memory = memoryAnalysis;

    if (memoryAnalysis.heapUsed > 100 * 1024 * 1024) { // 100MB
      diagnosis.issues.push({
        type: 'high_memory_usage',
        severity: 'high',
        description: `内存使用过高: ${Math.round(memoryAnalysis.heapUsed / 1024 / 1024)}MB`,
        impact: '可能导致系统响应缓慢或崩溃'
      });

      diagnosis.recommendations.push({
        action: 'reduce_memory_usage',
        steps: [
          '运行 task-master utils/archive --older-than=30d 归档旧任务',
          '重启 Task Master 进程',
          '考虑启用内存优化模式'
        ]
      });
    }

    // 2. 文件大小分析
    const fileSizeAnalysis = await this.analyzeFileSize();
    diagnosis.metrics.fileSize = fileSizeAnalysis;

    if (fileSizeAnalysis.tasksFileSize > 5 * 1024 * 1024) { // 5MB
      diagnosis.issues.push({
        type: 'large_data_file',
        severity: 'medium',
        description: `任务文件过大: ${Math.round(fileSizeAnalysis.tasksFileSize / 1024 / 1024)}MB`,
        impact: '文件读写操作变慢'
      });

      diagnosis.recommendations.push({
        action: 'optimize_data_storage',
        steps: [
          '归档已完成的任务',
          '考虑数据库迁移',
          '启用数据压缩'
        ]
      });
    }

    // 3. 命令响应时间分析
    const responseTimeAnalysis = await this.analyzeResponseTimes();
    diagnosis.metrics.responseTimes = responseTimeAnalysis;

    const slowCommands = Object.entries(responseTimeAnalysis)
      .filter(([cmd, time]) => time > 2000) // 2秒
      .map(([cmd, time]) => ({ command: cmd, time }));

    if (slowCommands.length > 0) {
      diagnosis.issues.push({
        type: 'slow_commands',
        severity: 'medium',
        description: `${slowCommands.length} 个命令响应缓慢`,
        details: slowCommands,
        impact: '用户体验下降'
      });

      diagnosis.recommendations.push({
        action: 'optimize_commands',
        steps: [
          '检查数据索引',
          '启用命令缓存',
          '优化算法复杂度'
        ]
      });
    }

    // 4. 依赖图复杂度分析
    const dependencyAnalysis = await this.analyzeDependencyComplexity();
    diagnosis.metrics.dependencies = dependencyAnalysis;

    if (dependencyAnalysis.complexity > 1000) {
      diagnosis.issues.push({
        type: 'complex_dependencies',
        severity: 'medium',
        description: `依赖图过于复杂: ${dependencyAnalysis.complexity}`,
        impact: '依赖分析和验证变慢'
      });

      diagnosis.recommendations.push({
        action: 'simplify_dependencies',
        steps: [
          '移除不必要的依赖关系',
          '分解复杂任务',
          '优化依赖结构'
        ]
      });
    }

    return diagnosis;
  }

  async analyzeMemoryUsage() {
    const usage = process.memoryUsage();
    return {
      heapUsed: usage.heapUsed,
      heapTotal: usage.heapTotal,
      external: usage.external,
      rss: usage.rss,
      utilization: usage.heapUsed / usage.heapTotal
    };
  }

  async analyzeFileSize() {
    const tasksPath = path.join(process.cwd(), '.taskmaster', 'tasks.json');
    const configPath = path.join(process.cwd(), '.taskmaster', 'config.json');

    const analysis = {
      tasksFileSize: 0,
      configFileSize: 0,
      totalSize: 0
    };

    if (await fs.pathExists(tasksPath)) {
      analysis.tasksFileSize = (await fs.stat(tasksPath)).size;
    }

    if (await fs.pathExists(configPath)) {
      analysis.configFileSize = (await fs.stat(configPath)).size;
    }

    analysis.totalSize = analysis.tasksFileSize + analysis.configFileSize;

    return analysis;
  }

  async analyzeResponseTimes() {
    const commands = ['status', 'list', 'show 1', 'analyze-complexity'];
    const times = {};

    for (const command of commands) {
      const start = Date.now();
      try {
        await taskMasterCLI.execute(command);
        times[command] = Date.now() - start;
      } catch (error) {
        times[command] = Date.now() - start;
      }
    }

    return times;
  }

  async analyzeDependencyComplexity() {
    try {
      const graph = await getDependencyGraph();
      const nodeCount = graph.nodes.size;
      const edgeCount = Array.from(graph.edges.values())
        .reduce((sum, edges) => sum + edges.size, 0);

      return {
        nodeCount,
        edgeCount,
        complexity: nodeCount * Math.log(edgeCount + 1),
        density: edgeCount / (nodeCount * (nodeCount - 1))
      };
    } catch (error) {
      return {
        nodeCount: 0,
        edgeCount: 0,
        complexity: 0,
        density: 0,
        error: error.message
      };
    }
  }
}
```

**3. 网络和 API 连接问题诊断：**
```bash
#!/bin/bash
# API 连接诊断脚本

echo "🌐 API 连接诊断"
echo "==============="

# AI 提供商端点
declare -A ENDPOINTS=(
    ["claude"]="https://api.anthropic.com/v1/messages"
    ["openai"]="https://api.openai.com/v1/chat/completions"
    ["perplexity"]="https://api.perplexity.ai/chat/completions"
)

# 检查基本网络连接
echo "1. 检查网络连接..."
if ping -c 1 google.com &> /dev/null; then
    echo "   ✅ 网络连接正常"
else
    echo "   ❌ 网络连接失败"
    echo "   💡 检查网络设置和防火墙配置"
    exit 1
fi

# 检查 DNS 解析
echo "2. 检查 DNS 解析..."
for provider in "${!ENDPOINTS[@]}"; do
    endpoint=${ENDPOINTS[$provider]}
    domain=$(echo $endpoint | sed 's|https://||' | sed 's|/.*||')

    if nslookup $domain &> /dev/null; then
        echo "   ✅ $provider DNS 解析正常"
    else
        echo "   ❌ $provider DNS 解析失败"
        echo "   💡 检查 DNS 设置或使用其他 DNS 服务器"
    fi
done

# 检查 HTTPS 连接
echo "3. 检查 HTTPS 连接..."
for provider in "${!ENDPOINTS[@]}"; do
    endpoint=${ENDPOINTS[$provider]}
    domain=$(echo $endpoint | sed 's|https://||' | sed 's|/.*||')

    if curl -s --connect-timeout 10 -I https://$domain &> /dev/null; then
        echo "   ✅ $provider HTTPS 连接正常"
    else
        echo "   ❌ $provider HTTPS 连接失败"
        echo "   💡 检查防火墙设置和代理配置"
    fi
done

# 检查 API 密钥配置
echo "4. 检查 API 密钥配置..."
if [ -f ".taskmaster/config.json" ]; then
    # 检查环境变量
    if [ ! -z "$ANTHROPIC_API_KEY" ]; then
        echo "   ✅ Claude API 密钥已设置"
    else
        echo "   ⚠️  Claude API 密钥未设置"
    fi

    if [ ! -z "$OPENAI_API_KEY" ]; then
        echo "   ✅ OpenAI API 密钥已设置"
    else
        echo "   ⚠️  OpenAI API 密钥未设置"
    fi

    if [ ! -z "$PERPLEXITY_API_KEY" ]; then
        echo "   ✅ Perplexity API 密钥已设置"
    else
        echo "   ⚠️  Perplexity API 密钥未设置"
    fi
else
    echo "   ❌ 配置文件不存在"
fi

# 测试 API 调用
echo "5. 测试 API 调用..."
echo "   (这将消耗少量 API 配额)"

# Claude API 测试
if [ ! -z "$ANTHROPIC_API_KEY" ]; then
    echo "   测试 Claude API..."
    response=$(curl -s -w "%{http_code}" -o /tmp/claude_test.json \
        -H "Content-Type: application/json" \
        -H "x-api-key: $ANTHROPIC_API_KEY" \
        -H "anthropic-version: 2023-06-01" \
        -d '{
            "model": "claude-3-haiku-20240307",
            "max_tokens": 10,
            "messages": [{"role": "user", "content": "test"}]
        }' \
        https://api.anthropic.com/v1/messages)

    if [ "$response" = "200" ]; then
        echo "   ✅ Claude API 调用成功"
    else
        echo "   ❌ Claude API 调用失败 (HTTP $response)"
        if [ -f "/tmp/claude_test.json" ]; then
            echo "   错误详情: $(cat /tmp/claude_test.json)"
        fi
    fi
fi

echo ""
echo "🎉 API 连接诊断完成！"
```

## CI/CD 集成和扩展性

### GitHub Actions 集成

**完整的 GitHub Actions 工作流配置：**

```yaml
# .github/workflows/taskmaster-ci.yml
name: Task Master CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每日自动任务分析
    - cron: '0 9 * * 1-5'  # 工作日上午9点

env:
  NODE_VERSION: '18'
  ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  PERPLEXITY_API_KEY: ${{ secrets.PERPLEXITY_API_KEY }}

jobs:
  setup-and-validate:
    runs-on: ubuntu-latest
    outputs:
      has-tasks: ${{ steps.check-tasks.outputs.has-tasks }}
      task-count: ${{ steps.check-tasks.outputs.task-count }}

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install Task Master
      run: |
        npm install -g task-master-ai
        task-master --version

    - name: Initialize or validate project
      run: |
        if [ ! -d ".taskmaster" ]; then
          echo "Initializing Task Master project..."
          task-master init -y --name="${{ github.repository }}"
        else
          echo "Validating existing Task Master project..."
          task-master status
        fi

    - name: Check for tasks
      id: check-tasks
      run: |
        if [ -f ".taskmaster/tasks.json" ]; then
          TASK_COUNT=$(jq length .taskmaster/tasks.json)
          echo "has-tasks=true" >> $GITHUB_OUTPUT
          echo "task-count=$TASK_COUNT" >> $GITHUB_OUTPUT
          echo "Found $TASK_COUNT tasks"
        else
          echo "has-tasks=false" >> $GITHUB_OUTPUT
          echo "task-count=0" >> $GITHUB_OUTPUT
          echo "No tasks found"
        fi

    - name: Validate dependencies
      if: steps.check-tasks.outputs.has-tasks == 'true'
      run: |
        echo "Validating task dependencies..."
        task-master validate-dependencies --format=json > dependency-report.json

        # 检查是否有错误
        ERROR_COUNT=$(jq '.errors | length' dependency-report.json)
        if [ "$ERROR_COUNT" -gt 0 ]; then
          echo "❌ Found $ERROR_COUNT dependency errors:"
          jq -r '.errors[] | "- \(.type): \(.message)"' dependency-report.json
          exit 1
        else
          echo "✅ All dependencies are valid"
        fi

    - name: Upload dependency report
      if: steps.check-tasks.outputs.has-tasks == 'true'
      uses: actions/upload-artifact@v4
      with:
        name: dependency-report
        path: dependency-report.json

  complexity-analysis:
    needs: setup-and-validate
    if: needs.setup-and-validate.outputs.has-tasks == 'true'
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js and Task Master
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    - run: npm install -g task-master-ai

    - name: Run complexity analysis
      run: |
        echo "Running complexity analysis..."
        task-master analyze-complexity --format=json > complexity-report.json

        # 生成复杂度报告
        task-master complexity-report --format=markdown > complexity-report.md

    - name: Check for high complexity tasks
      run: |
        HIGH_COMPLEXITY_COUNT=$(jq '[.tasks[] | select(.complexity > 7)] | length' complexity-report.json)
        echo "High complexity tasks: $HIGH_COMPLEXITY_COUNT"

        if [ "$HIGH_COMPLEXITY_COUNT" -gt 0 ]; then
          echo "⚠️ Found $HIGH_COMPLEXITY_COUNT high complexity tasks"
          jq -r '.tasks[] | select(.complexity > 7) | "- Task #\(.id): \(.title) (complexity: \(.complexity))"' complexity-report.json

          # 创建 GitHub issue 如果复杂度过高
          if [ "$HIGH_COMPLEXITY_COUNT" -gt 5 ]; then
            echo "Creating GitHub issue for high complexity tasks..."
            gh issue create \
              --title "High Complexity Tasks Detected" \
              --body "$(cat complexity-report.md)" \
              --label "complexity,needs-review"
          fi
        fi
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload complexity report
      uses: actions/upload-artifact@v4
      with:
        name: complexity-report
        path: |
          complexity-report.json
          complexity-report.md

  auto-task-expansion:
    needs: [setup-and-validate, complexity-analysis]
    if: needs.setup-and-validate.outputs.has-tasks == 'true'
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js and Task Master
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    - run: npm install -g task-master-ai

    - name: Download complexity report
      uses: actions/download-artifact@v4
      with:
        name: complexity-report

    - name: Auto-expand complex tasks
      run: |
        echo "Auto-expanding complex tasks..."

        # 获取需要扩展的任务
        TASKS_TO_EXPAND=$(jq -r '.tasks[] | select(.complexity > 7) | .id' complexity-report.json)

        if [ ! -z "$TASKS_TO_EXPAND" ]; then
          for task_id in $TASKS_TO_EXPAND; do
            echo "Expanding task #$task_id..."
            task-master expand --id=$task_id --auto-confirm
          done

          # 验证扩展结果
          task-master validate-dependencies

          # 提交更改
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .taskmaster/

          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "Auto-expand complex tasks [skip ci]"
            git push
          fi
        else
          echo "No tasks need expansion"
        fi

  performance-monitoring:
    needs: setup-and-validate
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js and Task Master
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    - run: npm install -g task-master-ai

    - name: Run performance benchmark
      run: |
        echo "Running performance benchmark..."
        task-master utils/benchmark --format=json > benchmark-results.json

        # 检查性能回归
        if [ -f "previous-benchmark.json" ]; then
          echo "Comparing with previous benchmark..."

          # 简单的性能比较
          CURRENT_AVG=$(jq '.summary.averageResponseTime' benchmark-results.json)
          PREVIOUS_AVG=$(jq '.summary.averageResponseTime' previous-benchmark.json)

          # 计算性能变化百分比
          CHANGE=$(echo "scale=2; ($CURRENT_AVG - $PREVIOUS_AVG) / $PREVIOUS_AVG * 100" | bc)

          echo "Performance change: ${CHANGE}%"

          # 如果性能下降超过20%，创建警告
          if (( $(echo "$CHANGE > 20" | bc -l) )); then
            echo "⚠️ Performance regression detected: ${CHANGE}%"
            gh issue create \
              --title "Performance Regression Detected" \
              --body "Performance has degraded by ${CHANGE}%. Please investigate." \
              --label "performance,bug"
          fi
        fi

        # 保存当前基准作为下次比较的基础
        cp benchmark-results.json previous-benchmark.json
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload benchmark results
      uses: actions/upload-artifact@v4
      with:
        name: benchmark-results
        path: |
          benchmark-results.json
          previous-benchmark.json

  daily-maintenance:
    if: github.event_name == 'schedule'
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js and Task Master
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    - run: npm install -g task-master-ai

    - name: Daily maintenance tasks
      run: |
        echo "Running daily maintenance..."

        # 1. 清理过期的已完成任务
        task-master utils/cleanup --older-than=90d --status=done

        # 2. 验证数据完整性
        task-master validate-dependencies --fix-auto

        # 3. 生成项目状态报告
        task-master status --format=markdown > daily-status-report.md

        # 4. 更新 README
        task-master sync-readme

        # 5. 创建备份
        task-master utils/backup --compress

    - name: Commit maintenance changes
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action - Daily Maintenance"
        git add .

        if git diff --staged --quiet; then
          echo "No maintenance changes to commit"
        else
          git commit -m "Daily maintenance: cleanup and updates [skip ci]"
          git push
        fi

    - name: Upload status report
      uses: actions/upload-artifact@v4
      with:
        name: daily-status-report
        path: daily-status-report.md

  deployment:
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [setup-and-validate, complexity-analysis, performance-monitoring]
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying Task Master configuration to production..."

        # 这里可以添加部署到生产环境的逻辑
        # 例如：同步到团队共享存储、更新文档站点等

        echo "Deployment completed successfully"
```

### Jenkins 集成

**Jenkins Pipeline 配置：**

```groovy
// Jenkinsfile
pipeline {
    agent any

    environment {
        NODE_VERSION = '18'
        ANTHROPIC_API_KEY = credentials('anthropic-api-key')
        OPENAI_API_KEY = credentials('openai-api-key')
        PERPLEXITY_API_KEY = credentials('perplexity-api-key')
    }

    triggers {
        // 每日构建
        cron('H 9 * * 1-5')
        // Git 变更触发
        pollSCM('H/15 * * * *')
    }

    stages {
        stage('Setup') {
            steps {
                script {
                    // 安装 Node.js
                    def nodeHome = tool name: 'NodeJS-18', type: 'jenkins.plugins.nodejs.tools.NodeJSInstallation'
                    env.PATH = "${nodeHome}/bin:${env.PATH}"

                    // 安装 Task Master
                    sh 'npm install -g task-master-ai'
                    sh 'task-master --version'
                }
            }
        }

        stage('Project Validation') {
            steps {
                script {
                    // 初始化或验证项目
                    if (!fileExists('.taskmaster')) {
                        sh 'task-master init -y --name="${JOB_NAME}"'
                    } else {
                        sh 'task-master status'
                    }

                    // 验证依赖关系
                    def dependencyResult = sh(
                        script: 'task-master validate-dependencies --format=json',
                        returnStdout: true
                    ).trim()

                    writeFile file: 'dependency-report.json', text: dependencyResult

                    // 检查错误
                    def report = readJSON file: 'dependency-report.json'
                    if (report.errors.size() > 0) {
                        error("Found ${report.errors.size()} dependency errors")
                    }
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'dependency-report.json', fingerprint: true
                }
            }
        }

        stage('Complexity Analysis') {
            steps {
                script {
                    // 运行复杂度分析
                    sh 'task-master analyze-complexity --format=json > complexity-report.json'
                    sh 'task-master complexity-report --format=markdown > complexity-report.md'

                    // 检查高复杂度任务
                    def complexityReport = readJSON file: 'complexity-report.json'
                    def highComplexityTasks = complexityReport.tasks.findAll { it.complexity > 7 }

                    if (highComplexityTasks.size() > 5) {
                        currentBuild.result = 'UNSTABLE'
                        echo "Warning: Found ${highComplexityTasks.size()} high complexity tasks"
                    }
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'complexity-report.*', fingerprint: true
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: '.',
                        reportFiles: 'complexity-report.md',
                        reportName: 'Complexity Report'
                    ])
                }
            }
        }

        stage('Performance Testing') {
            steps {
                script {
                    // 运行性能基准测试
                    sh 'task-master utils/benchmark --format=json > benchmark-results.json'

                    // 性能回归检测
                    if (fileExists('previous-benchmark.json')) {
                        def currentBenchmark = readJSON file: 'benchmark-results.json'
                        def previousBenchmark = readJSON file: 'previous-benchmark.json'

                        def currentAvg = currentBenchmark.summary.averageResponseTime
                        def previousAvg = previousBenchmark.summary.averageResponseTime
                        def change = ((currentAvg - previousAvg) / previousAvg) * 100

                        echo "Performance change: ${change}%"

                        if (change > 20) {
                            currentBuild.result = 'UNSTABLE'
                            echo "Warning: Performance regression detected: ${change}%"
                        }
                    }

                    // 保存当前基准
                    sh 'cp benchmark-results.json previous-benchmark.json'
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'benchmark-results.json,previous-benchmark.json', fingerprint: true
                }
            }
        }

        stage('Auto Maintenance') {
            when {
                anyOf {
                    triggeredBy 'TimerTrigger'
                    branch 'main'
                }
            }
            steps {
                script {
                    // 自动维护任务
                    sh 'task-master utils/cleanup --older-than=90d --status=done'
                    sh 'task-master validate-dependencies --fix-auto'
                    sh 'task-master sync-readme'

                    // 提交更改
                    sh '''
                        git config user.email "<EMAIL>"
                        git config user.name "Jenkins CI"
                        git add .
                        if ! git diff --staged --quiet; then
                            git commit -m "Automated maintenance: cleanup and updates [skip ci]"
                            git push origin ${BRANCH_NAME}
                        fi
                    '''
                }
            }
        }
    }

    post {
        always {
            // 清理工作空间
            cleanWs()
        }

        success {
            // 成功通知
            emailext (
                subject: "Task Master CI: Build Successful - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: "The Task Master CI pipeline completed successfully.",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }

        failure {
            // 失败通知
            emailext (
                subject: "Task Master CI: Build Failed - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: "The Task Master CI pipeline failed. Please check the build logs.",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }

        unstable {
            // 不稳定构建通知
            emailext (
                subject: "Task Master CI: Build Unstable - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: "The Task Master CI pipeline completed with warnings. Please review the reports.",
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}
```

### 自定义命令创建和插件开发

#### 自定义命令开发框架

**命令插件架构：**

```javascript
// plugins/custom-commands/base-command.js
class BaseTaskMasterCommand {
  constructor(name, description) {
    this.name = name;
    this.description = description;
    this.options = new Map();
    this.validators = [];
    this.middleware = [];
  }

  // 添加命令选项
  addOption(name, config) {
    this.options.set(name, {
      type: config.type || 'string',
      required: config.required || false,
      default: config.default,
      description: config.description,
      validator: config.validator
    });
    return this;
  }

  // 添加验证器
  addValidator(validator) {
    this.validators.push(validator);
    return this;
  }

  // 添加中间件
  use(middleware) {
    this.middleware.push(middleware);
    return this;
  }

  // 执行命令
  async execute(args, context) {
    try {
      // 1. 参数解析和验证
      const parsedArgs = await this.parseArguments(args);
      await this.validateArguments(parsedArgs);

      // 2. 执行中间件
      for (const middleware of this.middleware) {
        await middleware(parsedArgs, context);
      }

      // 3. 执行主要逻辑
      const result = await this.run(parsedArgs, context);

      // 4. 后处理
      return await this.postProcess(result, parsedArgs, context);

    } catch (error) {
      return await this.handleError(error, args, context);
    }
  }

  // 子类需要实现的主要方法
  async run(args, context) {
    throw new Error('Command must implement run() method');
  }

  // 可选的钩子方法
  async parseArguments(args) {
    const parsed = {};

    // 基本参数解析逻辑
    for (const [name, config] of this.options) {
      const value = args[name] || config.default;

      if (config.required && value === undefined) {
        throw new Error(`Required option --${name} is missing`);
      }

      if (value !== undefined && config.validator) {
        if (!config.validator(value)) {
          throw new Error(`Invalid value for option --${name}: ${value}`);
        }
      }

      parsed[name] = this.convertType(value, config.type);
    }

    return parsed;
  }

  async validateArguments(args) {
    for (const validator of this.validators) {
      await validator(args);
    }
  }

  async postProcess(result, args, context) {
    return result;
  }

  async handleError(error, args, context) {
    console.error(`Command ${this.name} failed:`, error.message);
    throw error;
  }

  convertType(value, type) {
    switch (type) {
      case 'number':
        return Number(value);
      case 'boolean':
        return Boolean(value);
      case 'array':
        return Array.isArray(value) ? value : [value];
      default:
        return value;
    }
  }
}

// 命令注册器
class CommandRegistry {
  constructor() {
    this.commands = new Map();
    this.aliases = new Map();
  }

  register(command) {
    if (!(command instanceof BaseTaskMasterCommand)) {
      throw new Error('Command must extend BaseTaskMasterCommand');
    }

    this.commands.set(command.name, command);
    return this;
  }

  alias(commandName, aliasName) {
    if (!this.commands.has(commandName)) {
      throw new Error(`Command ${commandName} not found`);
    }

    this.aliases.set(aliasName, commandName);
    return this;
  }

  async execute(commandName, args, context) {
    // 解析别名
    const actualCommandName = this.aliases.get(commandName) || commandName;
    const command = this.commands.get(actualCommandName);

    if (!command) {
      throw new Error(`Command ${commandName} not found`);
    }

    return await command.execute(args, context);
  }

  list() {
    return Array.from(this.commands.keys());
  }

  getCommand(name) {
    const actualName = this.aliases.get(name) || name;
    return this.commands.get(actualName);
  }
}
```

**示例自定义命令：**

```javascript
// plugins/custom-commands/team-report-command.js
class TeamReportCommand extends BaseTaskMasterCommand {
  constructor() {
    super('team-report', 'Generate comprehensive team performance report');

    this.addOption('period', {
      type: 'string',
      default: '30d',
      description: 'Report period (e.g., 7d, 30d, 3m)',
      validator: (value) => /^\d+[dwmy]$/.test(value)
    })
    .addOption('format', {
      type: 'string',
      default: 'markdown',
      description: 'Output format (markdown, json, html)',
      validator: (value) => ['markdown', 'json', 'html'].includes(value)
    })
    .addOption('include-charts', {
      type: 'boolean',
      default: false,
      description: 'Include performance charts'
    })
    .addOption('output', {
      type: 'string',
      description: 'Output file path'
    });

    // 添加验证器
    this.addValidator(async (args) => {
      const tasks = await getAllTasks();
      if (tasks.length === 0) {
        throw new Error('No tasks found. Initialize project with tasks first.');
      }
    });

    // 添加中间件
    this.use(async (args, context) => {
      console.log(`Generating team report for period: ${args.period}`);
      context.startTime = Date.now();
    });
  }

  async run(args, context) {
    const report = {
      metadata: {
        generatedAt: new Date().toISOString(),
        period: args.period,
        format: args.format
      },
      summary: {},
      teamMetrics: {},
      individualMetrics: {},
      recommendations: []
    };

    // 1. 收集团队数据
    const teamData = await this.collectTeamData(args.period);
    report.teamMetrics = teamData;

    // 2. 生成个人指标
    const individualData = await this.generateIndividualMetrics(teamData);
    report.individualMetrics = individualData;

    // 3. 计算摘要
    report.summary = this.calculateSummary(teamData, individualData);

    // 4. 生成建议
    report.recommendations = await this.generateRecommendations(report);

    // 5. 格式化输出
    const formattedReport = await this.formatReport(report, args.format);

    // 6. 保存文件（如果指定）
    if (args.output) {
      await this.saveReport(formattedReport, args.output, args.format);
    }

    return formattedReport;
  }

  async collectTeamData(period) {
    const tasks = await getAllTasks();
    const periodMs = this.parsePeriod(period);
    const cutoffDate = new Date(Date.now() - periodMs);

    const relevantTasks = tasks.filter(task =>
      new Date(task.createdAt) >= cutoffDate
    );

    const teamMembers = [...new Set(relevantTasks.map(t => t.assignee).filter(Boolean))];

    return {
      totalTasks: relevantTasks.length,
      completedTasks: relevantTasks.filter(t => t.status === 'done').length,
      inProgressTasks: relevantTasks.filter(t => t.status === 'in-progress').length,
      teamSize: teamMembers.length,
      averageComplexity: this.calculateAverageComplexity(relevantTasks),
      velocityTrend: await this.calculateVelocityTrend(relevantTasks, period),
      collaborationScore: await this.calculateCollaborationScore(relevantTasks),
      members: teamMembers
    };
  }

  async generateIndividualMetrics(teamData) {
    const individualMetrics = {};

    for (const member of teamData.members) {
      const memberTasks = await this.getMemberTasks(member);

      individualMetrics[member] = {
        tasksCompleted: memberTasks.filter(t => t.status === 'done').length,
        tasksInProgress: memberTasks.filter(t => t.status === 'in-progress').length,
        averageComplexity: this.calculateAverageComplexity(memberTasks),
        productivity: await this.calculateProductivity(memberTasks),
        specializations: this.identifySpecializations(memberTasks),
        collaborationFrequency: await this.calculateMemberCollaboration(member)
      };
    }

    return individualMetrics;
  }

  async formatReport(report, format) {
    switch (format) {
      case 'json':
        return JSON.stringify(report, null, 2);

      case 'html':
        return await this.generateHTMLReport(report);

      case 'markdown':
      default:
        return await this.generateMarkdownReport(report);
    }
  }

  async generateMarkdownReport(report) {
    const md = [];

    md.push('# Team Performance Report');
    md.push('');
    md.push(`**Generated:** ${report.metadata.generatedAt}`);
    md.push(`**Period:** ${report.metadata.period}`);
    md.push('');

    // 摘要部分
    md.push('## Executive Summary');
    md.push('');
    md.push(`- **Total Tasks:** ${report.teamMetrics.totalTasks}`);
    md.push(`- **Completed:** ${report.teamMetrics.completedTasks} (${Math.round(report.teamMetrics.completedTasks / report.teamMetrics.totalTasks * 100)}%)`);
    md.push(`- **Team Size:** ${report.teamMetrics.teamSize} members`);
    md.push(`- **Average Complexity:** ${report.teamMetrics.averageComplexity.toFixed(1)}/10`);
    md.push(`- **Collaboration Score:** ${report.teamMetrics.collaborationScore.toFixed(1)}/10`);
    md.push('');

    // 团队指标
    md.push('## Team Metrics');
    md.push('');
    md.push('### Velocity Trend');
    md.push('```');
    md.push(this.generateVelocityChart(report.teamMetrics.velocityTrend));
    md.push('```');
    md.push('');

    // 个人指标
    md.push('## Individual Performance');
    md.push('');
    md.push('| Member | Completed | In Progress | Avg Complexity | Productivity |');
    md.push('|--------|-----------|-------------|----------------|--------------|');

    for (const [member, metrics] of Object.entries(report.individualMetrics)) {
      md.push(`| ${member} | ${metrics.tasksCompleted} | ${metrics.tasksInProgress} | ${metrics.averageComplexity.toFixed(1)} | ${metrics.productivity.toFixed(1)} |`);
    }
    md.push('');

    // 建议
    md.push('## Recommendations');
    md.push('');
    for (const recommendation of report.recommendations) {
      md.push(`### ${recommendation.title}`);
      md.push('');
      md.push(recommendation.description);
      md.push('');
      if (recommendation.actions && recommendation.actions.length > 0) {
        md.push('**Action Items:**');
        for (const action of recommendation.actions) {
          md.push(`- ${action}`);
        }
        md.push('');
      }
    }

    return md.join('\n');
  }

  parsePeriod(period) {
    const match = period.match(/^(\d+)([dwmy])$/);
    if (!match) {
      throw new Error(`Invalid period format: ${period}`);
    }

    const [, amount, unit] = match;
    const multipliers = {
      'd': 24 * 60 * 60 * 1000,      // 天
      'w': 7 * 24 * 60 * 60 * 1000,  // 周
      'm': 30 * 24 * 60 * 60 * 1000, // 月
      'y': 365 * 24 * 60 * 60 * 1000 // 年
    };

    return parseInt(amount) * multipliers[unit];
  }

  async postProcess(result, args, context) {
    const duration = Date.now() - context.startTime;
    console.log(`Team report generated in ${duration}ms`);

    if (args.output) {
      console.log(`Report saved to: ${args.output}`);
    } else {
      console.log('Report generated successfully');
    }

    return result;
  }
}

// 注册自定义命令
const registry = new CommandRegistry();
registry.register(new TeamReportCommand());
```

**高级自定义命令示例 - 智能任务推荐：**

```javascript
// plugins/custom-commands/smart-recommendations-command.js
class SmartRecommendationsCommand extends BaseTaskMasterCommand {
  constructor() {
    super('smart-recommend', 'AI-powered task recommendations based on context');

    this.addOption('context', {
      type: 'string',
      description: 'Current context (morning, afternoon, focused, interrupted)',
      default: 'auto'
    })
    .addOption('skills', {
      type: 'array',
      description: 'User skills/preferences'
    })
    .addOption('time-available', {
      type: 'number',
      description: 'Available time in minutes',
      validator: (value) => value > 0 && value <= 480 // 最多8小时
    })
    .addOption('energy-level', {
      type: 'string',
      description: 'Current energy level (high, medium, low)',
      default: 'medium',
      validator: (value) => ['high', 'medium', 'low'].includes(value)
    })
    .addOption('learning-mode', {
      type: 'boolean',
      default: false,
      description: 'Prefer tasks that involve learning new skills'
    });
  }

  async run(args, context) {
    // 1. 分析当前上下文
    const contextAnalysis = await this.analyzeContext(args);

    // 2. 获取可用任务
    const availableTasks = await this.getAvailableTasks();

    // 3. 应用智能过滤
    const filteredTasks = await this.applyIntelligentFiltering(
      availableTasks,
      contextAnalysis,
      args
    );

    // 4. 计算推荐分数
    const scoredTasks = await this.calculateRecommendationScores(
      filteredTasks,
      contextAnalysis,
      args
    );

    // 5. 生成推荐
    const recommendations = this.generateRecommendations(scoredTasks, args);

    return {
      context: contextAnalysis,
      recommendations: recommendations,
      reasoning: this.generateReasoning(recommendations, contextAnalysis)
    };
  }

  async analyzeContext(args) {
    const analysis = {
      timeOfDay: this.getTimeOfDay(),
      workingHours: this.isWorkingHours(),
      recentActivity: await this.getRecentActivity(),
      currentLoad: await this.getCurrentWorkload(),
      energyLevel: args['energy-level'],
      availableTime: args['time-available'] || this.estimateAvailableTime()
    };

    // 自动检测上下文
    if (args.context === 'auto') {
      analysis.detectedContext = this.detectContext(analysis);
    } else {
      analysis.detectedContext = args.context;
    }

    return analysis;
  }

  async applyIntelligentFiltering(tasks, context, args) {
    let filtered = tasks.filter(task => {
      // 基本过滤条件
      if (task.status !== 'pending') return false;
      if (this.isTaskBlocked(task)) return false;

      // 时间过滤
      if (context.availableTime && task.estimatedHours) {
        const taskMinutes = task.estimatedHours * 60;
        if (taskMinutes > context.availableTime * 1.2) return false; // 20%缓冲
      }

      // 能量级别过滤
      if (context.energyLevel === 'low' && task.complexity > 6) return false;
      if (context.energyLevel === 'high' && task.complexity < 3 && !args['learning-mode']) return false;

      return true;
    });

    // 上下文特定过滤
    switch (context.detectedContext) {
      case 'morning':
        // 早晨适合复杂任务
        filtered = filtered.filter(task => task.complexity >= 4);
        break;

      case 'afternoon':
        // 下午适合中等复杂度任务
        filtered = filtered.filter(task => task.complexity >= 3 && task.complexity <= 7);
        break;

      case 'focused':
        // 专注时间适合深度工作
        filtered = filtered.filter(task =>
          task.type !== 'meeting' && task.complexity >= 5
        );
        break;

      case 'interrupted':
        // 容易被打断时适合简单任务
        filtered = filtered.filter(task =>
          task.complexity <= 4 && task.estimatedHours <= 2
        );
        break;
    }

    return filtered;
  }

  async calculateRecommendationScores(tasks, context, args) {
    const scoredTasks = [];

    for (const task of tasks) {
      let score = 0;
      const factors = {};

      // 优先级权重 (30%)
      const priorityWeights = { high: 30, medium: 20, low: 10 };
      factors.priority = priorityWeights[task.priority] || 15;
      score += factors.priority;

      // 复杂度匹配 (25%)
      factors.complexity = this.calculateComplexityScore(task.complexity, context.energyLevel);
      score += factors.complexity;

      // 技能匹配 (20%)
      if (args.skills && args.skills.length > 0) {
        factors.skills = this.calculateSkillMatch(task, args.skills);
        score += factors.skills;
      } else {
        factors.skills = 10; // 默认分数
        score += factors.skills;
      }

      // 时间匹配 (15%)
      factors.timeMatch = this.calculateTimeMatch(task, context.availableTime);
      score += factors.timeMatch;

      // 学习机会 (10%)
      if (args['learning-mode']) {
        factors.learning = this.calculateLearningOpportunity(task);
        score += factors.learning;
      } else {
        factors.learning = 5; // 默认分数
        score += factors.learning;
      }

      scoredTasks.push({
        task,
        score,
        factors
      });
    }

    return scoredTasks.sort((a, b) => b.score - a.score);
  }

  calculateComplexityScore(taskComplexity, energyLevel) {
    const energyLevels = { low: 3, medium: 6, high: 9 };
    const optimalComplexity = energyLevels[energyLevel];

    // 计算复杂度匹配分数
    const diff = Math.abs(taskComplexity - optimalComplexity);
    return Math.max(0, 25 - diff * 3);
  }

  calculateSkillMatch(task, userSkills) {
    // 简化的技能匹配算法
    const taskSkills = this.extractTaskSkills(task);
    const matchCount = taskSkills.filter(skill =>
      userSkills.some(userSkill =>
        userSkill.toLowerCase().includes(skill.toLowerCase()) ||
        skill.toLowerCase().includes(userSkill.toLowerCase())
      )
    ).length;

    return Math.min(20, matchCount * 5);
  }

  extractTaskSkills(task) {
    // 从任务描述中提取技能关键词
    const skillKeywords = [
      'javascript', 'python', 'react', 'node', 'database', 'api',
      'frontend', 'backend', 'testing', 'deployment', 'design',
      'documentation', 'analysis', 'optimization'
    ];

    const text = (task.title + ' ' + task.description).toLowerCase();
    return skillKeywords.filter(skill => text.includes(skill));
  }

  generateRecommendations(scoredTasks, args) {
    const topTasks = scoredTasks.slice(0, 5);

    return topTasks.map((item, index) => ({
      rank: index + 1,
      task: item.task,
      score: Math.round(item.score),
      confidence: this.calculateConfidence(item.score),
      reasons: this.generateReasons(item.factors),
      estimatedDuration: item.task.estimatedHours || 'Unknown'
    }));
  }

  generateReasons(factors) {
    const reasons = [];

    if (factors.priority >= 25) {
      reasons.push('High priority task');
    }

    if (factors.complexity >= 20) {
      reasons.push('Good complexity match for current energy level');
    }

    if (factors.skills >= 15) {
      reasons.push('Matches your skill set');
    }

    if (factors.timeMatch >= 12) {
      reasons.push('Fits available time slot');
    }

    if (factors.learning >= 8) {
      reasons.push('Good learning opportunity');
    }

    return reasons;
  }

  calculateConfidence(score) {
    if (score >= 80) return 'Very High';
    if (score >= 65) return 'High';
    if (score >= 50) return 'Medium';
    if (score >= 35) return 'Low';
    return 'Very Low';
  }
}

// 注册命令
registry.register(new SmartRecommendationsCommand());
registry.alias('smart-recommend', 'recommend');
registry.alias('smart-recommend', 'suggest');
```

### API 集成模式和外部工具集成

#### Jira 集成

**Jira API 集成实现：**

```javascript
// integrations/jira-integration.js
class JiraIntegration {
  constructor(config) {
    this.baseUrl = config.baseUrl;
    this.username = config.username;
    this.apiToken = config.apiToken;
    this.projectKey = config.projectKey;
    this.axios = require('axios').create({
      baseURL: this.baseUrl,
      auth: {
        username: this.username,
        password: this.apiToken
      },
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
  }

  async syncTasksToJira(tasks, options = {}) {
    const syncResults = {
      created: [],
      updated: [],
      errors: [],
      summary: {}
    };

    for (const task of tasks) {
      try {
        const existingIssue = await this.findExistingIssue(task);

        if (existingIssue) {
          // 更新现有问题
          const updateResult = await this.updateJiraIssue(existingIssue.key, task);
          syncResults.updated.push({
            taskId: task.id,
            jiraKey: existingIssue.key,
            result: updateResult
          });
        } else {
          // 创建新问题
          const createResult = await this.createJiraIssue(task);
          syncResults.created.push({
            taskId: task.id,
            jiraKey: createResult.key,
            result: createResult
          });

          // 更新任务以包含 Jira 链接
          await this.updateTaskWithJiraLink(task.id, createResult.key);
        }
      } catch (error) {
        syncResults.errors.push({
          taskId: task.id,
          error: error.message
        });
      }
    }

    syncResults.summary = {
      total: tasks.length,
      created: syncResults.created.length,
      updated: syncResults.updated.length,
      errors: syncResults.errors.length
    };

    return syncResults;
  }

  async createJiraIssue(task) {
    const issueData = {
      fields: {
        project: { key: this.projectKey },
        summary: task.title,
        description: this.formatDescription(task),
        issuetype: { name: this.mapTaskTypeToJiraIssueType(task.type) },
        priority: { name: this.mapPriorityToJira(task.priority) },
        labels: this.generateLabels(task),
        customfield_10001: task.estimatedHours, // Story Points 或自定义字段
        assignee: task.assignee ? { name: task.assignee } : null
      }
    };

    const response = await this.axios.post('/rest/api/3/issue', issueData);
    return response.data;
  }

  async updateJiraIssue(issueKey, task) {
    const updateData = {
      fields: {
        summary: task.title,
        description: this.formatDescription(task),
        priority: { name: this.mapPriorityToJira(task.priority) },
        labels: this.generateLabels(task),
        assignee: task.assignee ? { name: task.assignee } : null
      }
    };

    // 更新状态
    const statusTransition = this.mapStatusToJiraTransition(task.status);
    if (statusTransition) {
      await this.transitionIssue(issueKey, statusTransition);
    }

    const response = await this.axios.put(`/rest/api/3/issue/${issueKey}`, updateData);
    return response.data;
  }

  async findExistingIssue(task) {
    // 通过自定义字段或标签查找现有问题
    const jql = `project = ${this.projectKey} AND labels = "taskmaster-${task.id}"`;

    const response = await this.axios.get('/rest/api/3/search', {
      params: {
        jql: jql,
        maxResults: 1
      }
    });

    return response.data.issues.length > 0 ? response.data.issues[0] : null;
  }

  formatDescription(task) {
    let description = task.description || '';

    // 添加验收标准
    if (task.acceptanceCriteria && task.acceptanceCriteria.length > 0) {
      description += '\n\n*Acceptance Criteria:*\n';
      task.acceptanceCriteria.forEach((criteria, index) => {
        description += `${index + 1}. ${criteria}\n`;
      });
    }

    // 添加依赖信息
    if (task.dependencies && task.dependencies.length > 0) {
      description += '\n\n*Dependencies:*\n';
      task.dependencies.forEach(depId => {
        description += `- Task #${depId}\n`;
      });
    }

    // 添加 Task Master 链接
    description += `\n\n_Managed by Task Master - Task ID: ${task.id}_`;

    return description;
  }

  mapTaskTypeToJiraIssueType(taskType) {
    const mapping = {
      'feature': 'Story',
      'bug': 'Bug',
      'task': 'Task',
      'epic': 'Epic',
      'subtask': 'Sub-task'
    };
    return mapping[taskType] || 'Task';
  }

  mapPriorityToJira(priority) {
    const mapping = {
      'low': 'Low',
      'medium': 'Medium',
      'high': 'High'
    };
    return mapping[priority] || 'Medium';
  }

  mapStatusToJiraTransition(status) {
    const mapping = {
      'pending': 'To Do',
      'in-progress': 'In Progress',
      'review': 'In Review',
      'done': 'Done',
      'cancelled': 'Cancelled'
    };
    return mapping[status];
  }

  generateLabels(task) {
    const labels = [`taskmaster-${task.id}`];

    if (task.complexity > 7) {
      labels.push('high-complexity');
    }

    if (task.tags) {
      labels.push(...task.tags);
    }

    return labels;
  }

  async syncFromJira(options = {}) {
    const jql = options.jql || `project = ${this.projectKey} AND labels ~ "taskmaster-"`;

    const response = await this.axios.get('/rest/api/3/search', {
      params: {
        jql: jql,
        maxResults: options.maxResults || 100,
        fields: 'summary,description,status,priority,assignee,labels,customfield_10001'
      }
    });

    const syncResults = {
      updated: [],
      created: [],
      errors: []
    };

    for (const issue of response.data.issues) {
      try {
        const taskId = this.extractTaskIdFromLabels(issue.fields.labels);

        if (taskId) {
          // 更新现有任务
          const updateResult = await this.updateTaskFromJira(taskId, issue);
          syncResults.updated.push(updateResult);
        } else {
          // 创建新任务（如果需要）
          if (options.createNewTasks) {
            const createResult = await this.createTaskFromJira(issue);
            syncResults.created.push(createResult);
          }
        }
      } catch (error) {
        syncResults.errors.push({
          issueKey: issue.key,
          error: error.message
        });
      }
    }

    return syncResults;
  }

  extractTaskIdFromLabels(labels) {
    const taskLabel = labels.find(label => label.startsWith('taskmaster-'));
    return taskLabel ? taskLabel.replace('taskmaster-', '') : null;
  }

  async updateTaskFromJira(taskId, jiraIssue) {
    const task = await getTask(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    const updates = {
      title: jiraIssue.fields.summary,
      status: this.mapJiraStatusToTaskStatus(jiraIssue.fields.status.name),
      priority: this.mapJiraPriorityToTaskPriority(jiraIssue.fields.priority.name),
      assignee: jiraIssue.fields.assignee ? jiraIssue.fields.assignee.displayName : null,
      estimatedHours: jiraIssue.fields.customfield_10001 || task.estimatedHours,
      jiraKey: jiraIssue.key,
      lastSyncedAt: new Date().toISOString()
    };

    return await updateTask(taskId, updates);
  }
}

// Jira 集成命令
class JiraSyncCommand extends BaseTaskMasterCommand {
  constructor() {
    super('jira-sync', 'Synchronize tasks with Jira');

    this.addOption('direction', {
      type: 'string',
      default: 'both',
      description: 'Sync direction: to-jira, from-jira, both',
      validator: (value) => ['to-jira', 'from-jira', 'both'].includes(value)
    })
    .addOption('project-key', {
      type: 'string',
      description: 'Jira project key'
    })
    .addOption('dry-run', {
      type: 'boolean',
      default: false,
      description: 'Preview changes without applying them'
    });
  }

  async run(args, context) {
    const config = await this.loadJiraConfig();
    const jira = new JiraIntegration(config);

    const results = {
      toJira: null,
      fromJira: null
    };

    if (args.direction === 'to-jira' || args.direction === 'both') {
      console.log('Syncing tasks to Jira...');
      const tasks = await getAllTasks();
      results.toJira = await jira.syncTasksToJira(tasks, { dryRun: args['dry-run'] });
    }

    if (args.direction === 'from-jira' || args.direction === 'both') {
      console.log('Syncing from Jira...');
      results.fromJira = await jira.syncFromJira({ dryRun: args['dry-run'] });
    }

    return results;
  }

  async loadJiraConfig() {
    const config = await getConfig();

    if (!config.integrations || !config.integrations.jira) {
      throw new Error('Jira integration not configured. Run: task-master config --setup-jira');
    }

    return config.integrations.jira;
  }
}
```

#### Slack 集成

**Slack 通知和交互系统：**

```javascript
// integrations/slack-integration.js
class SlackIntegration {
  constructor(config) {
    this.botToken = config.botToken;
    this.signingSecret = config.signingSecret;
    this.channel = config.defaultChannel;
    this.slack = require('@slack/web-api').WebClient(this.botToken);
  }

  async sendTaskNotification(task, event, options = {}) {
    const message = this.buildTaskMessage(task, event, options);

    const result = await this.slack.chat.postMessage({
      channel: options.channel || this.channel,
      ...message
    });

    return result;
  }

  buildTaskMessage(task, event, options) {
    const eventMessages = {
      'created': '🆕 New task created',
      'completed': '✅ Task completed',
      'started': '🚀 Task started',
      'blocked': '🚫 Task blocked',
      'overdue': '⏰ Task overdue',
      'assigned': '👤 Task assigned'
    };

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: eventMessages[event] || '📋 Task update'
        }
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Task:* ${task.title}`
          },
          {
            type: 'mrkdwn',
            text: `*ID:* #${task.id}`
          },
          {
            type: 'mrkdwn',
            text: `*Priority:* ${this.formatPriority(task.priority)}`
          },
          {
            type: 'mrkdwn',
            text: `*Status:* ${this.formatStatus(task.status)}`
          }
        ]
      }
    ];

    // 添加描述
    if (task.description) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Description:* ${task.description.substring(0, 200)}${task.description.length > 200 ? '...' : ''}`
        }
      });
    }

    // 添加分配信息
    if (task.assignee) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Assigned to:* ${task.assignee}`
        }
      });
    }

    // 添加操作按钮
    if (options.includeActions) {
      blocks.push({
        type: 'actions',
        elements: this.buildActionButtons(task)
      });
    }

    return {
      blocks: blocks,
      text: `${eventMessages[event]}: ${task.title}` // 备用文本
    };
  }

  buildActionButtons(task) {
    const buttons = [];

    if (task.status === 'pending') {
      buttons.push({
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'Start Task'
        },
        action_id: 'start_task',
        value: task.id.toString(),
        style: 'primary'
      });
    }

    if (task.status === 'in-progress') {
      buttons.push({
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'Complete Task'
        },
        action_id: 'complete_task',
        value: task.id.toString(),
        style: 'primary'
      });
    }

    buttons.push({
      type: 'button',
      text: {
        type: 'plain_text',
        text: 'View Details'
      },
      action_id: 'view_task',
      value: task.id.toString()
    });

    return buttons;
  }

  async handleSlackInteraction(payload) {
    const action = payload.actions[0];
    const taskId = parseInt(action.value);

    try {
      switch (action.action_id) {
        case 'start_task':
          await this.handleStartTask(taskId, payload);
          break;
        case 'complete_task':
          await this.handleCompleteTask(taskId, payload);
          break;
        case 'view_task':
          await this.handleViewTask(taskId, payload);
          break;
      }
    } catch (error) {
      await this.sendErrorMessage(payload.response_url, error.message);
    }
  }

  async handleStartTask(taskId, payload) {
    const task = await getTask(taskId);
    const user = payload.user.name;

    // 更新任务状态
    await updateTask(taskId, {
      status: 'in-progress',
      assignee: user,
      startedAt: new Date().toISOString()
    });

    // 更新 Slack 消息
    await this.slack.chat.update({
      channel: payload.channel.id,
      ts: payload.message.ts,
      text: `✅ Task started by ${user}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `✅ *Task #${taskId}* started by <@${payload.user.id}>`
          }
        }
      ]
    });
  }

  async sendDailyStandup(channel, teamMembers) {
    const standupData = await this.generateStandupData(teamMembers);

    const blocks = [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📊 Daily Standup Report'
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Date:* ${new Date().toLocaleDateString()}`
        }
      }
    ];

    // 团队概览
    blocks.push({
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Active Tasks:* ${standupData.activeTasks}`
        },
        {
          type: 'mrkdwn',
          text: `*Completed Yesterday:* ${standupData.completedYesterday}`
        },
        {
          type: 'mrkdwn',
          text: `*Blocked Tasks:* ${standupData.blockedTasks}`
        },
        {
          type: 'mrkdwn',
          text: `*Team Velocity:* ${standupData.velocity}`
        }
      ]
    });

    // 个人状态
    for (const member of standupData.members) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*${member.name}*\n• Working on: ${member.currentTasks.join(', ') || 'None'}\n• Completed: ${member.completedYesterday}\n• Blockers: ${member.blockers || 'None'}`
        }
      });
    }

    await this.slack.chat.postMessage({
      channel: channel,
      blocks: blocks
    });
  }

  formatPriority(priority) {
    const emojis = {
      'high': '🔴 High',
      'medium': '🟡 Medium',
      'low': '🟢 Low'
    };
    return emojis[priority] || priority;
  }

  formatStatus(status) {
    const emojis = {
      'pending': '⏳ Pending',
      'in-progress': '🔄 In Progress',
      'review': '👀 In Review',
      'done': '✅ Done',
      'cancelled': '❌ Cancelled'
    };
    return emojis[status] || status;
  }
}

// Slack 集成命令
class SlackNotifyCommand extends BaseTaskMasterCommand {
  constructor() {
    super('slack-notify', 'Send notifications to Slack');

    this.addOption('event', {
      type: 'string',
      required: true,
      description: 'Event type: created, completed, started, blocked, overdue'
    })
    .addOption('task-id', {
      type: 'number',
      required: true,
      description: 'Task ID to notify about'
    })
    .addOption('channel', {
      type: 'string',
      description: 'Slack channel (overrides default)'
    })
    .addOption('include-actions', {
      type: 'boolean',
      default: true,
      description: 'Include action buttons'
    });
  }

  async run(args, context) {
    const config = await this.loadSlackConfig();
    const slack = new SlackIntegration(config);

    const task = await getTask(args['task-id']);
    if (!task) {
      throw new Error(`Task ${args['task-id']} not found`);
    }

    const result = await slack.sendTaskNotification(task, args.event, {
      channel: args.channel,
      includeActions: args['include-actions']
    });

    return {
      success: true,
      messageTs: result.ts,
      channel: result.channel
    };
  }

  async loadSlackConfig() {
    const config = await getConfig();

    if (!config.integrations || !config.integrations.slack) {
      throw new Error('Slack integration not configured. Run: task-master config --setup-slack');
    }

    return config.integrations.slack;
  }
}
```

### 数据库迁移策略

#### 从 JSON 到关系数据库的迁移

**数据库架构设计：**

```sql
-- PostgreSQL 数据库架构
-- migrations/001_initial_schema.sql

-- 项目表
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    config JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'active'
);

-- 任务表
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    complexity INTEGER CHECK (complexity >= 1 AND complexity <= 10),
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    assignee VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    due_date TIMESTAMP WITH TIME ZONE,
    tags TEXT[],
    metadata JSONB DEFAULT '{}',
    external_links JSONB DEFAULT '{}' -- Jira, GitHub 等外部链接
);

-- 任务依赖关系表
CREATE TABLE task_dependencies (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    depends_on_task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    dependency_type VARCHAR(50) DEFAULT 'blocks', -- blocks, relates_to, duplicates
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(task_id, depends_on_task_id)
);

-- 子任务关系表
CREATE TABLE task_hierarchy (
    id SERIAL PRIMARY KEY,
    parent_task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    child_task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(parent_task_id, child_task_id)
);

-- 任务历史记录表
CREATE TABLE task_history (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    field_name VARCHAR(100) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    changed_by VARCHAR(255),
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    change_reason TEXT
);

-- 任务评论表
CREATE TABLE task_comments (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    author VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_system_comment BOOLEAN DEFAULT FALSE
);

-- 任务附件表
CREATE TABLE task_attachments (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    uploaded_by VARCHAR(255),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 团队成员表
CREATE TABLE team_members (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    username VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    email VARCHAR(255),
    role VARCHAR(50) DEFAULT 'member', -- admin, manager, member, viewer
    skills TEXT[],
    timezone VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(project_id, username)
);

-- 工作日志表
CREATE TABLE work_logs (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES tasks(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES team_members(id) ON DELETE CASCADE,
    hours_worked DECIMAL(4,2) NOT NULL,
    work_date DATE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_assignee ON tasks(assignee);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_task_dependencies_task_id ON task_dependencies(task_id);
CREATE INDEX idx_task_dependencies_depends_on ON task_dependencies(depends_on_task_id);
CREATE INDEX idx_task_history_task_id ON task_history(task_id);
CREATE INDEX idx_task_history_changed_at ON task_history(changed_at);

-- 触发器：自动更新 updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

**数据迁移工具：**

```javascript
// migrations/database-migrator.js
class DatabaseMigrator {
  constructor(config) {
    this.sourceType = config.sourceType; // 'json' or 'database'
    this.targetType = config.targetType; // 'json' or 'database'
    this.sourceConfig = config.source;
    this.targetConfig = config.target;
    this.batchSize = config.batchSize || 100;
  }

  async migrate(options = {}) {
    console.log(`🚀 Starting migration from ${this.sourceType} to ${this.targetType}...`);

    const migration = {
      startTime: new Date(),
      sourceType: this.sourceType,
      targetType: this.targetType,
      stats: {
        projects: 0,
        tasks: 0,
        dependencies: 0,
        comments: 0,
        errors: 0
      },
      errors: []
    };

    try {
      // 1. 初始化连接
      const source = await this.initializeSource();
      const target = await this.initializeTarget();

      // 2. 验证目标环境
      await this.validateTarget(target);

      // 3. 创建备份（如果需要）
      if (options.createBackup) {
        await this.createBackup(source);
      }

      // 4. 执行迁移
      await this.migrateProjects(source, target, migration);
      await this.migrateTasks(source, target, migration);
      await this.migrateDependencies(source, target, migration);
      await this.migrateComments(source, target, migration);

      // 5. 验证迁移结果
      await this.validateMigration(source, target, migration);

      migration.endTime = new Date();
      migration.duration = migration.endTime - migration.startTime;

      console.log('✅ Migration completed successfully!');
      return migration;

    } catch (error) {
      migration.errors.push({
        type: 'migration_failure',
        message: error.message,
        stack: error.stack
      });

      console.error('❌ Migration failed:', error.message);
      throw error;
    }
  }

  async initializeSource() {
    if (this.sourceType === 'json') {
      return new JSONDataSource(this.sourceConfig);
    } else if (this.sourceType === 'database') {
      return new DatabaseDataSource(this.sourceConfig);
    }
    throw new Error(`Unsupported source type: ${this.sourceType}`);
  }

  async initializeTarget() {
    if (this.targetType === 'json') {
      return new JSONDataTarget(this.targetConfig);
    } else if (this.targetType === 'database') {
      return new DatabaseDataTarget(this.targetConfig);
    }
    throw new Error(`Unsupported target type: ${this.targetType}`);
  }

  async migrateTasks(source, target, migration) {
    console.log('📋 Migrating tasks...');

    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const tasks = await source.getTasks(offset, this.batchSize);

      if (tasks.length === 0) {
        hasMore = false;
        break;
      }

      // 转换任务数据格式
      const convertedTasks = tasks.map(task => this.convertTaskFormat(task));

      // 批量插入
      try {
        await target.insertTasks(convertedTasks);
        migration.stats.tasks += convertedTasks.length;
        console.log(`  ✅ Migrated ${convertedTasks.length} tasks (total: ${migration.stats.tasks})`);
      } catch (error) {
        migration.errors.push({
          type: 'task_migration_error',
          batch: { offset, size: tasks.length },
          message: error.message
        });
        migration.stats.errors++;
      }

      offset += this.batchSize;
    }
  }

  convertTaskFormat(task) {
    // 从 JSON 格式转换为数据库格式
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      status: task.status || 'pending',
      priority: task.priority || 'medium',
      complexity: task.complexity,
      estimated_hours: task.estimatedHours,
      actual_hours: task.actualHours,
      assignee: task.assignee,
      created_at: task.createdAt ? new Date(task.createdAt) : new Date(),
      updated_at: task.updatedAt ? new Date(task.updatedAt) : new Date(),
      started_at: task.startedAt ? new Date(task.startedAt) : null,
      completed_at: task.completedAt ? new Date(task.completedAt) : null,
      due_date: task.dueDate ? new Date(task.dueDate) : null,
      tags: task.tags || [],
      metadata: {
        originalId: task.id,
        migrationDate: new Date().toISOString(),
        ...task.metadata
      },
      external_links: task.externalLinks || {}
    };
  }

  async validateMigration(source, target, migration) {
    console.log('🔍 Validating migration...');

    const validation = {
      passed: true,
      issues: []
    };

    // 验证任务数量
    const sourceTaskCount = await source.getTaskCount();
    const targetTaskCount = await target.getTaskCount();

    if (sourceTaskCount !== targetTaskCount) {
      validation.passed = false;
      validation.issues.push({
        type: 'count_mismatch',
        message: `Task count mismatch: source=${sourceTaskCount}, target=${targetTaskCount}`
      });
    }

    // 验证依赖关系完整性
    const dependencyValidation = await this.validateDependencies(source, target);
    if (!dependencyValidation.passed) {
      validation.passed = false;
      validation.issues.push(...dependencyValidation.issues);
    }

    // 验证数据完整性
    const integrityValidation = await this.validateDataIntegrity(target);
    if (!integrityValidation.passed) {
      validation.passed = false;
      validation.issues.push(...integrityValidation.issues);
    }

    migration.validation = validation;

    if (validation.passed) {
      console.log('✅ Migration validation passed');
    } else {
      console.log(`⚠️  Migration validation found ${validation.issues.length} issues`);
      validation.issues.forEach(issue => {
        console.log(`  - ${issue.message}`);
      });
    }

    return validation;
  }
}

// 数据库数据源
class DatabaseDataSource {
  constructor(config) {
    this.pool = new Pool(config);
  }

  async getTasks(offset, limit) {
    const query = `
      SELECT t.*,
             array_agg(DISTINCT td.depends_on_task_id) FILTER (WHERE td.depends_on_task_id IS NOT NULL) as dependencies,
             array_agg(DISTINCT th.parent_task_id) FILTER (WHERE th.parent_task_id IS NOT NULL) as parent_tasks
      FROM tasks t
      LEFT JOIN task_dependencies td ON t.id = td.task_id
      LEFT JOIN task_hierarchy th ON t.id = th.child_task_id
      GROUP BY t.id
      ORDER BY t.id
      LIMIT $1 OFFSET $2
    `;

    const result = await this.pool.query(query, [limit, offset]);
    return result.rows;
  }

  async getTaskCount() {
    const result = await this.pool.query('SELECT COUNT(*) FROM tasks');
    return parseInt(result.rows[0].count);
  }

  async close() {
    await this.pool.end();
  }
}

// 数据库数据目标
class DatabaseDataTarget {
  constructor(config) {
    this.pool = new Pool(config);
  }

  async insertTasks(tasks) {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      for (const task of tasks) {
        const query = `
          INSERT INTO tasks (
            title, description, status, priority, complexity,
            estimated_hours, actual_hours, assignee, created_at,
            updated_at, started_at, completed_at, due_date,
            tags, metadata, external_links
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
          ) RETURNING id
        `;

        const values = [
          task.title, task.description, task.status, task.priority,
          task.complexity, task.estimated_hours, task.actual_hours,
          task.assignee, task.created_at, task.updated_at,
          task.started_at, task.completed_at, task.due_date,
          task.tags, JSON.stringify(task.metadata),
          JSON.stringify(task.external_links)
        ];

        await client.query(query, values);
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async getTaskCount() {
    const result = await this.pool.query('SELECT COUNT(*) FROM tasks');
    return parseInt(result.rows[0].count);
  }
}
```

**迁移命令实现：**

```javascript
// commands/migrate-database.js
class MigrateDatabaseCommand extends BaseTaskMasterCommand {
  constructor() {
    super('migrate-database', 'Migrate data between storage backends');

    this.addOption('from', {
      type: 'string',
      required: true,
      description: 'Source type: json, postgresql, mysql, mongodb',
      validator: (value) => ['json', 'postgresql', 'mysql', 'mongodb'].includes(value)
    })
    .addOption('to', {
      type: 'string',
      required: true,
      description: 'Target type: json, postgresql, mysql, mongodb',
      validator: (value) => ['json', 'postgresql', 'mysql', 'mongodb'].includes(value)
    })
    .addOption('connection-string', {
      type: 'string',
      description: 'Database connection string for target'
    })
    .addOption('batch-size', {
      type: 'number',
      default: 100,
      description: 'Batch size for migration'
    })
    .addOption('create-backup', {
      type: 'boolean',
      default: true,
      description: 'Create backup before migration'
    })
    .addOption('dry-run', {
      type: 'boolean',
      default: false,
      description: 'Preview migration without executing'
    });
  }

  async run(args, context) {
    if (args.from === args.to) {
      throw new Error('Source and target types cannot be the same');
    }

    const migrationConfig = {
      sourceType: args.from,
      targetType: args.to,
      source: await this.buildSourceConfig(args.from),
      target: await this.buildTargetConfig(args.to, args['connection-string']),
      batchSize: args['batch-size']
    };

    const migrator = new DatabaseMigrator(migrationConfig);

    if (args['dry-run']) {
      return await this.performDryRun(migrator);
    } else {
      return await migrator.migrate({
        createBackup: args['create-backup']
      });
    }
  }

  async buildSourceConfig(sourceType) {
    switch (sourceType) {
      case 'json':
        return {
          tasksFile: path.join(process.cwd(), '.taskmaster', 'tasks.json'),
          configFile: path.join(process.cwd(), '.taskmaster', 'config.json')
        };
      case 'postgresql':
        return this.getPostgreSQLConfig();
      case 'mysql':
        return this.getMySQLConfig();
      case 'mongodb':
        return this.getMongoDBConfig();
      default:
        throw new Error(`Unsupported source type: ${sourceType}`);
    }
  }

  async buildTargetConfig(targetType, connectionString) {
    if (connectionString) {
      return { connectionString };
    }

    // 从配置文件或环境变量获取连接信息
    const config = await getConfig();
    const dbConfig = config.database && config.database[targetType];

    if (!dbConfig) {
      throw new Error(`No configuration found for ${targetType}. Provide --connection-string or configure database settings.`);
    }

    return dbConfig;
  }

  async performDryRun(migrator) {
    console.log('🔍 Performing dry run...');

    const source = await migrator.initializeSource();
    const analysis = {
      sourceType: migrator.sourceType,
      targetType: migrator.targetType,
      estimatedData: {},
      potentialIssues: [],
      estimatedDuration: 0
    };

    // 分析源数据
    analysis.estimatedData.tasks = await source.getTaskCount();
    analysis.estimatedData.dependencies = await source.getDependencyCount();
    analysis.estimatedData.comments = await source.getCommentCount();

    // 估算迁移时间
    const totalRecords = Object.values(analysis.estimatedData).reduce((sum, count) => sum + count, 0);
    analysis.estimatedDuration = Math.ceil(totalRecords / migrator.batchSize) * 2; // 秒

    // 检查潜在问题
    analysis.potentialIssues = await this.identifyPotentialIssues(source, migrator.targetType);

    console.log(`📊 Migration Analysis:`);
    console.log(`  Tasks: ${analysis.estimatedData.tasks}`);
    console.log(`  Dependencies: ${analysis.estimatedData.dependencies}`);
    console.log(`  Comments: ${analysis.estimatedData.comments}`);
    console.log(`  Estimated duration: ${analysis.estimatedDuration} seconds`);

    if (analysis.potentialIssues.length > 0) {
      console.log(`⚠️  Potential issues found:`);
      analysis.potentialIssues.forEach(issue => {
        console.log(`  - ${issue.message}`);
      });
    }

    return analysis;
  }
}
```

### 企业级部署架构

#### 多项目和企业部署架构

**企业级架构设计：**

```yaml
# docker-compose.enterprise.yml
version: '3.8'

services:
  # 负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - taskmaster-api-1
      - taskmaster-api-2
      - taskmaster-api-3
    networks:
      - taskmaster-network

  # Task Master API 服务集群
  taskmaster-api-1:
    image: taskmaster-enterprise:latest
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://taskmaster:${DB_PASSWORD}@postgres:5432/taskmaster
      - REDIS_URL=redis://redis:6379
      - API_PORT=3000
      - INSTANCE_ID=api-1
    volumes:
      - ./config:/app/config
      - taskmaster-uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - taskmaster-network

  taskmaster-api-2:
    image: taskmaster-enterprise:latest
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://taskmaster:${DB_PASSWORD}@postgres:5432/taskmaster
      - REDIS_URL=redis://redis:6379
      - API_PORT=3000
      - INSTANCE_ID=api-2
    volumes:
      - ./config:/app/config
      - taskmaster-uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - taskmaster-network

  taskmaster-api-3:
    image: taskmaster-enterprise:latest
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://taskmaster:${DB_PASSWORD}@postgres:5432/taskmaster
      - REDIS_URL=redis://redis:6379
      - API_PORT=3000
      - INSTANCE_ID=api-3
    volumes:
      - ./config:/app/config
      - taskmaster-uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - taskmaster-network

  # 后台任务处理器
  taskmaster-worker-1:
    image: taskmaster-enterprise:latest
    command: ["node", "worker.js"]
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://taskmaster:${DB_PASSWORD}@postgres:5432/taskmaster
      - REDIS_URL=redis://redis:6379
      - WORKER_TYPE=general
      - WORKER_ID=worker-1
    volumes:
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
    networks:
      - taskmaster-network

  taskmaster-worker-ai:
    image: taskmaster-enterprise:latest
    command: ["node", "ai-worker.js"]
    environment:
      - NODE_ENV=production
      - DATABASE_URL=postgresql://taskmaster:${DB_PASSWORD}@postgres:5432/taskmaster
      - REDIS_URL=redis://redis:6379
      - WORKER_TYPE=ai
      - WORKER_ID=ai-worker-1
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
    networks:
      - taskmaster-network

  # 数据库主从复制
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=taskmaster
      - POSTGRES_USER=taskmaster
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_REPLICATION_MODE=master
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=${REPLICATION_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
    ports:
      - "5432:5432"
    networks:
      - taskmaster-network

  postgres-replica:
    image: postgres:15
    environment:
      - POSTGRES_MASTER_SERVICE=postgres
      - POSTGRES_REPLICATION_MODE=slave
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=${REPLICATION_PASSWORD}
      - POSTGRES_MASTER_PORT_NUMBER=5432
    depends_on:
      - postgres
    networks:
      - taskmaster-network

  # Redis 集群
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --cluster-enabled yes
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - taskmaster-network

  # 监控和日志
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - taskmaster-network

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
    depends_on:
      - prometheus
    networks:
      - taskmaster-network

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - taskmaster-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - taskmaster-network

  # 备份服务
  backup-service:
    image: taskmaster-backup:latest
    environment:
      - DATABASE_URL=postgresql://taskmaster:${DB_PASSWORD}@postgres:5432/taskmaster
      - S3_BUCKET=${BACKUP_S3_BUCKET}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - BACKUP_SCHEDULE=0 2 * * * # 每天凌晨2点
    volumes:
      - ./backup:/app/backup
    depends_on:
      - postgres
    networks:
      - taskmaster-network

volumes:
  postgres-data:
  redis-data:
  prometheus-data:
  grafana-data:
  elasticsearch-data:
  taskmaster-uploads:

networks:
  taskmaster-network:
    driver: bridge
```

**Kubernetes 部署配置：**

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: taskmaster-enterprise

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: taskmaster-config
  namespace: taskmaster-enterprise
data:
  app.yaml: |
    server:
      port: 3000
      host: "0.0.0.0"
    database:
      type: postgresql
      host: postgres-service
      port: 5432
      database: taskmaster
      ssl: false
    redis:
      host: redis-service
      port: 6379
    ai:
      providers:
        - name: claude
          endpoint: https://api.anthropic.com
          model: claude-3-5-sonnet-20241022
        - name: openai
          endpoint: https://api.openai.com
          model: gpt-4
    monitoring:
      enabled: true
      prometheus:
        enabled: true
        port: 9090
      logging:
        level: info
        format: json

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: taskmaster-secrets
  namespace: taskmaster-enterprise
type: Opaque
data:
  database-password: <base64-encoded-password>
  anthropic-api-key: <base64-encoded-key>
  openai-api-key: <base64-encoded-key>
  jwt-secret: <base64-encoded-secret>

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: taskmaster-api
  namespace: taskmaster-enterprise
  labels:
    app: taskmaster-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: taskmaster-api
  template:
    metadata:
      labels:
        app: taskmaster-api
    spec:
      containers:
      - name: taskmaster-api
        image: taskmaster-enterprise:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: taskmaster-secrets
              key: database-password
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: taskmaster-secrets
              key: anthropic-api-key
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: taskmaster-secrets
              key: openai-api-key
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: uploads-volume
          mountPath: /app/uploads
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: taskmaster-config
      - name: uploads-volume
        persistentVolumeClaim:
          claimName: taskmaster-uploads-pvc

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: taskmaster-api-service
  namespace: taskmaster-enterprise
spec:
  selector:
    app: taskmaster-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: taskmaster-ingress
  namespace: taskmaster-enterprise
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - taskmaster.company.com
    secretName: taskmaster-tls
  rules:
  - host: taskmaster.company.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: taskmaster-api-service
            port:
              number: 80

---
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: taskmaster-api-hpa
  namespace: taskmaster-enterprise
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: taskmaster-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# k8s/postgres.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: taskmaster-enterprise
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: taskmaster
        - name: POSTGRES_USER
          value: taskmaster
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: taskmaster-secrets
              key: database-password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi

---
# k8s/postgres-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: taskmaster-enterprise
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
```

**企业级安全配置：**

```javascript
// security/enterprise-security.js
class EnterpriseSecurityManager {
  constructor(config) {
    this.config = config;
    this.authProvider = this.initializeAuthProvider();
    this.rbac = new RoleBasedAccessControl();
    this.auditLogger = new AuditLogger();
  }

  initializeAuthProvider() {
    switch (this.config.auth.provider) {
      case 'ldap':
        return new LDAPAuthProvider(this.config.auth.ldap);
      case 'saml':
        return new SAMLAuthProvider(this.config.auth.saml);
      case 'oauth2':
        return new OAuth2AuthProvider(this.config.auth.oauth2);
      case 'jwt':
        return new JWTAuthProvider(this.config.auth.jwt);
      default:
        throw new Error(`Unsupported auth provider: ${this.config.auth.provider}`);
    }
  }

  async authenticateUser(credentials) {
    try {
      const user = await this.authProvider.authenticate(credentials);

      // 记录认证事件
      await this.auditLogger.log({
        event: 'user_authentication',
        userId: user.id,
        success: true,
        timestamp: new Date(),
        ip: credentials.ip,
        userAgent: credentials.userAgent
      });

      return user;
    } catch (error) {
      // 记录认证失败
      await this.auditLogger.log({
        event: 'authentication_failure',
        username: credentials.username,
        success: false,
        timestamp: new Date(),
        ip: credentials.ip,
        error: error.message
      });

      throw error;
    }
  }

  async authorizeAction(user, resource, action) {
    const authorized = await this.rbac.checkPermission(user, resource, action);

    // 记录授权检查
    await this.auditLogger.log({
      event: 'authorization_check',
      userId: user.id,
      resource: resource,
      action: action,
      authorized: authorized,
      timestamp: new Date()
    });

    return authorized;
  }

  async encryptSensitiveData(data) {
    const crypto = require('crypto');
    const algorithm = 'aes-256-gcm';
    const key = Buffer.from(this.config.encryption.key, 'hex');
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipher(algorithm, key);
    cipher.setAAD(Buffer.from('taskmaster-enterprise'));

    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      encrypted: encrypted,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  async decryptSensitiveData(encryptedData) {
    const crypto = require('crypto');
    const algorithm = 'aes-256-gcm';
    const key = Buffer.from(this.config.encryption.key, 'hex');

    const decipher = crypto.createDecipher(algorithm, key);
    decipher.setAAD(Buffer.from('taskmaster-enterprise'));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  }
}

// 基于角色的访问控制
class RoleBasedAccessControl {
  constructor() {
    this.roles = new Map();
    this.permissions = new Map();
    this.initializeDefaultRoles();
  }

  initializeDefaultRoles() {
    // 系统管理员
    this.defineRole('system_admin', {
      name: 'System Administrator',
      permissions: ['*'] // 所有权限
    });

    // 项目管理员
    this.defineRole('project_admin', {
      name: 'Project Administrator',
      permissions: [
        'project:read', 'project:write', 'project:delete',
        'task:read', 'task:write', 'task:delete',
        'user:read', 'user:write',
        'report:read', 'report:write'
      ]
    });

    // 团队负责人
    this.defineRole('team_lead', {
      name: 'Team Lead',
      permissions: [
        'project:read',
        'task:read', 'task:write', 'task:assign',
        'user:read',
        'report:read'
      ]
    });

    // 开发者
    this.defineRole('developer', {
      name: 'Developer',
      permissions: [
        'project:read',
        'task:read', 'task:write_own',
        'user:read_basic'
      ]
    });

    // 只读用户
    this.defineRole('viewer', {
      name: 'Viewer',
      permissions: [
        'project:read',
        'task:read',
        'user:read_basic'
      ]
    });
  }

  defineRole(roleId, roleDefinition) {
    this.roles.set(roleId, roleDefinition);
  }

  async checkPermission(user, resource, action) {
    const userRoles = user.roles || [];

    for (const roleId of userRoles) {
      const role = this.roles.get(roleId);
      if (!role) continue;

      // 检查通配符权限
      if (role.permissions.includes('*')) {
        return true;
      }

      // 检查具体权限
      const permission = `${resource}:${action}`;
      if (role.permissions.includes(permission)) {
        return true;
      }

      // 检查资源级通配符
      const resourceWildcard = `${resource}:*`;
      if (role.permissions.includes(resourceWildcard)) {
        return true;
      }

      // 检查拥有者权限
      if (permission.endsWith('_own') && this.isResourceOwner(user, resource)) {
        return true;
      }
    }

    return false;
  }

  isResourceOwner(user, resource) {
    // 实现资源拥有者检查逻辑
    // 例如：检查任务是否分配给当前用户
    return false; // 简化实现
  }
}
```

## 高级使用模式和最佳实践

### 复杂工作流自动化实例

#### 实际场景：敏捷开发团队工作流

**场景描述：**
一个10人的敏捷开发团队，使用2周冲刺周期，需要自动化日常任务管理、代码审查跟踪、和发布管理。

```javascript
// workflows/agile-team-workflow.js
class AgileTeamWorkflow {
  constructor() {
    this.sprintDuration = 14; // 天
    this.teamSize = 10;
    this.automationRules = this.initializeAutomationRules();
  }

  initializeAutomationRules() {
    return [
      // 每日站会自动化
      {
        name: 'daily_standup',
        trigger: { type: 'schedule', cron: '0 9 * * 1-5' }, // 工作日上午9点
        actions: [
          'generate_standup_report',
          'send_slack_notification',
          'update_burndown_chart',
          'identify_blockers'
        ]
      },

      // 冲刺规划自动化
      {
        name: 'sprint_planning',
        trigger: { type: 'schedule', cron: '0 10 * * 1', condition: 'sprint_start_week' },
        actions: [
          'analyze_backlog_complexity',
          'suggest_sprint_capacity',
          'auto_assign_tasks',
          'create_sprint_board',
          'schedule_planning_meeting'
        ]
      },

      // 代码审查跟踪
      {
        name: 'code_review_tracking',
        trigger: { type: 'task_status_change', from: 'in-progress', to: 'review' },
        actions: [
          'create_pull_request',
          'assign_reviewers',
          'set_review_deadline',
          'notify_reviewers'
        ]
      },

      // 发布准备
      {
        name: 'release_preparation',
        trigger: { type: 'sprint_end', days_before: 2 },
        actions: [
          'validate_all_tasks_complete',
          'run_integration_tests',
          'generate_release_notes',
          'prepare_deployment_checklist'
        ]
      }
    ];
  }

  async executeWorkflow(workflowName, context = {}) {
    const workflow = this.automationRules.find(rule => rule.name === workflowName);
    if (!workflow) {
      throw new Error(`Workflow ${workflowName} not found`);
    }

    console.log(`🚀 Executing workflow: ${workflow.name}`);

    const results = {
      workflow: workflowName,
      startTime: new Date(),
      actions: [],
      success: true,
      errors: []
    };

    for (const actionName of workflow.actions) {
      try {
        console.log(`  ⚡ Executing action: ${actionName}`);
        const actionResult = await this.executeAction(actionName, context);

        results.actions.push({
          name: actionName,
          success: true,
          result: actionResult,
          duration: actionResult.duration
        });

      } catch (error) {
        console.error(`  ❌ Action failed: ${actionName} - ${error.message}`);

        results.actions.push({
          name: actionName,
          success: false,
          error: error.message
        });

        results.errors.push({
          action: actionName,
          error: error.message
        });

        // 根据错误严重程度决定是否继续
        if (this.isCriticalAction(actionName)) {
          results.success = false;
          break;
        }
      }
    }

    results.endTime = new Date();
    results.totalDuration = results.endTime - results.startTime;

    // 发送工作流完成通知
    await this.sendWorkflowNotification(results);

    return results;
  }

  async executeAction(actionName, context) {
    const startTime = Date.now();

    switch (actionName) {
      case 'generate_standup_report':
        return await this.generateStandupReport(context);
      case 'send_slack_notification':
        return await this.sendSlackNotification(context);
      case 'update_burndown_chart':
        return await this.updateBurndownChart(context);
      case 'identify_blockers':
        return await this.identifyBlockers(context);
      case 'analyze_backlog_complexity':
        return await this.analyzeBacklogComplexity(context);
      case 'suggest_sprint_capacity':
        return await this.suggestSprintCapacity(context);
      case 'auto_assign_tasks':
        return await this.autoAssignTasks(context);
      case 'create_sprint_board':
        return await this.createSprintBoard(context);
      case 'create_pull_request':
        return await this.createPullRequest(context);
      case 'assign_reviewers':
        return await this.assignReviewers(context);
      case 'validate_all_tasks_complete':
        return await this.validateAllTasksComplete(context);
      case 'generate_release_notes':
        return await this.generateReleaseNotes(context);
      default:
        throw new Error(`Unknown action: ${actionName}`);
    }
  }

  async generateStandupReport(context) {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const tasks = await getAllTasks();
    const teamMembers = await getTeamMembers();

    const report = {
      date: new Date().toISOString().split('T')[0],
      teamMetrics: {
        totalTasks: tasks.length,
        completedYesterday: tasks.filter(t =>
          t.status === 'done' &&
          new Date(t.completedAt) >= yesterday
        ).length,
        inProgress: tasks.filter(t => t.status === 'in-progress').length,
        blocked: tasks.filter(t => this.isTaskBlocked(t)).length
      },
      memberReports: []
    };

    for (const member of teamMembers) {
      const memberTasks = tasks.filter(t => t.assignee === member.username);
      const memberReport = {
        name: member.displayName || member.username,
        completedYesterday: memberTasks.filter(t =>
          t.status === 'done' &&
          new Date(t.completedAt) >= yesterday
        ),
        workingOn: memberTasks.filter(t => t.status === 'in-progress'),
        blockers: memberTasks.filter(t => this.isTaskBlocked(t)),
        plannedToday: await this.getPlannedTasksForMember(member.username)
      };

      report.memberReports.push(memberReport);
    }

    // 保存报告
    const reportPath = path.join(process.cwd(), '.taskmaster', 'reports', `standup-${report.date}.json`);
    await fs.ensureDir(path.dirname(reportPath));
    await fs.writeJson(reportPath, report, { spaces: 2 });

    return {
      report,
      reportPath,
      duration: Date.now() - Date.now()
    };
  }

  async autoAssignTasks(context) {
    const unassignedTasks = await getTasksByStatus('pending');
    const teamMembers = await getTeamMembers();
    const assignments = [];

    // 计算每个团队成员的当前工作负载
    const workloads = new Map();
    for (const member of teamMembers) {
      const memberTasks = await getTasksByAssignee(member.username);
      const activeTasks = memberTasks.filter(t =>
        ['pending', 'in-progress'].includes(t.status)
      );

      workloads.set(member.username, {
        taskCount: activeTasks.length,
        totalComplexity: activeTasks.reduce((sum, t) => sum + (t.complexity || 0), 0),
        skills: member.skills || [],
        availability: member.availability || 1.0
      });
    }

    // 智能任务分配算法
    for (const task of unassignedTasks) {
      const bestAssignee = this.findBestAssignee(task, workloads, teamMembers);

      if (bestAssignee) {
        await updateTask(task.id, { assignee: bestAssignee });
        assignments.push({
          taskId: task.id,
          taskTitle: task.title,
          assignee: bestAssignee,
          reason: this.getAssignmentReason(task, bestAssignee, workloads)
        });

        // 更新工作负载
        const workload = workloads.get(bestAssignee);
        workload.taskCount++;
        workload.totalComplexity += task.complexity || 0;
      }
    }

    return {
      assignments,
      totalAssigned: assignments.length,
      unassigned: unassignedTasks.length - assignments.length,
      duration: Date.now() - Date.now()
    };
  }

  findBestAssignee(task, workloads, teamMembers) {
    let bestScore = -1;
    let bestAssignee = null;

    for (const member of teamMembers) {
      if (!member.isActive) continue;

      const workload = workloads.get(member.username);
      let score = 0;

      // 技能匹配分数 (40%)
      const skillMatch = this.calculateSkillMatch(task, workload.skills);
      score += skillMatch * 0.4;

      // 工作负载平衡分数 (30%)
      const maxComplexity = Math.max(...Array.from(workloads.values()).map(w => w.totalComplexity));
      const loadBalance = maxComplexity > 0 ?
        (maxComplexity - workload.totalComplexity) / maxComplexity : 1;
      score += loadBalance * 0.3;

      // 可用性分数 (20%)
      score += workload.availability * 0.2;

      // 任务类型偏好分数 (10%)
      const typePreference = this.calculateTypePreference(task, member);
      score += typePreference * 0.1;

      if (score > bestScore) {
        bestScore = score;
        bestAssignee = member.username;
      }
    }

    return bestAssignee;
  }

  calculateSkillMatch(task, memberSkills) {
    const taskSkills = this.extractTaskSkills(task);
    if (taskSkills.length === 0) return 0.5; // 默认分数

    const matchingSkills = taskSkills.filter(skill =>
      memberSkills.some(memberSkill =>
        memberSkill.toLowerCase().includes(skill.toLowerCase()) ||
        skill.toLowerCase().includes(memberSkill.toLowerCase())
      )
    );

    return matchingSkills.length / taskSkills.length;
  }

  extractTaskSkills(task) {
    const skillKeywords = [
      'javascript', 'typescript', 'react', 'vue', 'angular', 'node',
      'python', 'django', 'flask', 'java', 'spring', 'kotlin',
      'go', 'rust', 'c++', 'c#', '.net', 'php', 'laravel',
      'database', 'sql', 'mongodb', 'redis', 'elasticsearch',
      'aws', 'azure', 'gcp', 'docker', 'kubernetes',
      'frontend', 'backend', 'fullstack', 'mobile', 'ios', 'android',
      'testing', 'qa', 'automation', 'ci/cd', 'devops',
      'ui/ux', 'design', 'api', 'microservices', 'security'
    ];

    const text = (task.title + ' ' + (task.description || '')).toLowerCase();
    return skillKeywords.filter(skill => text.includes(skill));
  }
}

// 使用示例
const agileWorkflow = new AgileTeamWorkflow();

// 设置自动化触发器
async function setupAgileAutomation() {
  // 每日站会自动化
  cron.schedule('0 9 * * 1-5', async () => {
    await agileWorkflow.executeWorkflow('daily_standup');
  });

  // 冲刺规划自动化
  cron.schedule('0 10 * * 1', async () => {
    const isSprintStartWeek = await checkIfSprintStartWeek();
    if (isSprintStartWeek) {
      await agileWorkflow.executeWorkflow('sprint_planning');
    }
  });

  // 任务状态变更触发器
  eventEmitter.on('task_status_changed', async (event) => {
    if (event.from === 'in-progress' && event.to === 'review') {
      await agileWorkflow.executeWorkflow('code_review_tracking', {
        taskId: event.taskId,
        assignee: event.assignee
      });
    }
  });
}
```

#### 团队入职和培训程序

**新团队成员入职自动化：**

```javascript
// workflows/team-onboarding.js
class TeamOnboardingWorkflow {
  constructor() {
    this.onboardingTemplates = this.initializeOnboardingTemplates();
  }

  initializeOnboardingTemplates() {
    return {
      developer: {
        name: 'Developer Onboarding',
        duration: 14, // 天
        phases: [
          {
            name: 'Environment Setup',
            duration: 2,
            tasks: [
              'Setup development environment',
              'Install required tools and IDEs',
              'Configure Git and SSH keys',
              'Access team repositories',
              'Setup Task Master CLI'
            ]
          },
          {
            name: 'Codebase Familiarization',
            duration: 5,
            tasks: [
              'Review architecture documentation',
              'Complete code walkthrough sessions',
              'Run and understand test suites',
              'Review coding standards and guidelines',
              'Complete first code review'
            ]
          },
          {
            name: 'Team Integration',
            duration: 3,
            tasks: [
              'Attend daily standups',
              'Participate in sprint planning',
              'Shadow experienced team member',
              'Complete team communication training',
              'Setup Slack and other tools'
            ]
          },
          {
            name: 'First Contributions',
            duration: 4,
            tasks: [
              'Complete first bug fix',
              'Implement small feature',
              'Write comprehensive tests',
              'Update documentation',
              'Present work to team'
            ]
          }
        ]
      },

      manager: {
        name: 'Manager Onboarding',
        duration: 10,
        phases: [
          {
            name: 'Team Overview',
            duration: 3,
            tasks: [
              'Meet all team members individually',
              'Review team structure and roles',
              'Understand current projects and priorities',
              'Review team performance metrics',
              'Access management tools and dashboards'
            ]
          },
          {
            name: 'Process Understanding',
            duration: 4,
            tasks: [
              'Learn agile processes and ceremonies',
              'Understand task management workflows',
              'Review reporting and communication protocols',
              'Complete management training modules',
              'Shadow current manager (if applicable)'
            ]
          },
          {
            name: 'Leadership Transition',
            duration: 3,
            tasks: [
              'Lead first team meeting',
              'Conduct first one-on-ones',
              'Review and approve task assignments',
              'Make first strategic decisions',
              'Establish personal management style'
            ]
          }
        ]
      }
    };
  }

  async createOnboardingPlan(newMember, role = 'developer') {
    const template = this.onboardingTemplates[role];
    if (!template) {
      throw new Error(`No onboarding template found for role: ${role}`);
    }

    console.log(`🎯 Creating onboarding plan for ${newMember.name} (${role})`);

    const onboardingPlan = {
      memberId: newMember.id,
      memberName: newMember.name,
      role: role,
      template: template.name,
      startDate: new Date(),
      expectedEndDate: new Date(Date.now() + template.duration * 24 * 60 * 60 * 1000),
      phases: [],
      totalTasks: 0,
      createdTasks: []
    };

    let currentDate = new Date(onboardingPlan.startDate);

    for (const phase of template.phases) {
      const phaseEndDate = new Date(currentDate.getTime() + phase.duration * 24 * 60 * 60 * 1000);

      const phaseData = {
        name: phase.name,
        startDate: new Date(currentDate),
        endDate: phaseEndDate,
        tasks: []
      };

      // 为每个阶段创建任务
      for (let i = 0; i < phase.tasks.length; i++) {
        const taskTitle = phase.tasks[i];
        const taskDueDate = new Date(
          currentDate.getTime() +
          ((i + 1) / phase.tasks.length) * phase.duration * 24 * 60 * 60 * 1000
        );

        const task = await this.createOnboardingTask({
          title: `${phase.name}: ${taskTitle}`,
          description: await this.generateTaskDescription(taskTitle, role, newMember),
          assignee: newMember.username,
          priority: 'high',
          complexity: this.estimateTaskComplexity(taskTitle),
          dueDate: taskDueDate,
          tags: ['onboarding', role, phase.name.toLowerCase().replace(/\s+/g, '-')],
          metadata: {
            onboardingPlan: true,
            phase: phase.name,
            newMember: newMember.id,
            mentor: await this.assignMentor(newMember, role)
          }
        });

        phaseData.tasks.push(task.id);
        onboardingPlan.createdTasks.push(task);
        onboardingPlan.totalTasks++;
      }

      onboardingPlan.phases.push(phaseData);
      currentDate = phaseEndDate;
    }

    // 创建入职跟踪任务
    const trackingTask = await this.createTrackingTask(onboardingPlan);
    onboardingPlan.trackingTaskId = trackingTask.id;

    // 安排入职检查点
    await this.scheduleOnboardingCheckpoints(onboardingPlan);

    // 通知相关人员
    await this.notifyOnboardingStart(onboardingPlan);

    return onboardingPlan;
  }

  async generateTaskDescription(taskTitle, role, newMember) {
    const descriptionTemplates = {
      'Setup development environment': `
Welcome to the team, ${newMember.name}! This task will help you set up your development environment.

**Steps to complete:**
1. Install Node.js (version 18+) and npm
2. Install Git and configure with your credentials
3. Clone the main repository: [repository-url]
4. Install project dependencies: \`npm install\`
5. Copy environment configuration: \`cp .env.example .env\`
6. Run the application locally: \`npm start\`
7. Verify everything works by accessing http://localhost:3000

**Resources:**
- [Development Setup Guide](link-to-guide)
- [Team Slack Channel](link-to-slack)
- Contact your mentor: ${await this.getMentorName(newMember, role)}

**Acceptance Criteria:**
- [ ] Development environment is fully functional
- [ ] Can run application locally without errors
- [ ] Can access all necessary tools and resources
      `,

      'Complete first code review': `
Time to participate in the code review process! This is a crucial part of our development workflow.

**Your task:**
1. Find an open pull request in the team repository
2. Review the code changes thoroughly
3. Provide constructive feedback using our review guidelines
4. Ask questions if anything is unclear
5. Approve the PR if it meets our standards

**What to look for:**
- Code follows our style guidelines
- Tests are included and comprehensive
- Documentation is updated if needed
- No obvious bugs or security issues
- Performance considerations are addressed

**Resources:**
- [Code Review Guidelines](link-to-guidelines)
- [How to Give Good Code Reviews](link-to-guide)

**Acceptance Criteria:**
- [ ] Reviewed at least one pull request
- [ ] Provided meaningful feedback
- [ ] Followed team review process
      `
    };

    return descriptionTemplates[taskTitle] || `
Complete the onboarding task: ${taskTitle}

This task is part of your ${role} onboarding process. Please reach out to your mentor or team lead if you need any assistance.

**Mentor:** ${await this.getMentorName(newMember, role)}
**Expected Duration:** 2-4 hours
**Priority:** High (Onboarding)

Please update this task with your progress and mark it complete when finished.
    `;
  }

  async assignMentor(newMember, role) {
    const teamMembers = await getTeamMembers();

    // 寻找合适的导师
    const potentialMentors = teamMembers.filter(member =>
      member.role === role &&
      member.isActive &&
      member.canMentor !== false &&
      member.username !== newMember.username
    );

    if (potentialMentors.length === 0) {
      // 如果没有同角色的导师，寻找高级成员
      const seniorMembers = teamMembers.filter(member =>
        member.seniority === 'senior' &&
        member.isActive &&
        member.username !== newMember.username
      );

      return seniorMembers.length > 0 ? seniorMembers[0].username : null;
    }

    // 选择当前导师负担最轻的成员
    let bestMentor = potentialMentors[0];
    let minMenteeCount = await this.getMenteeCount(bestMentor.username);

    for (const mentor of potentialMentors.slice(1)) {
      const menteeCount = await this.getMenteeCount(mentor.username);
      if (menteeCount < minMenteeCount) {
        bestMentor = mentor;
        minMenteeCount = menteeCount;
      }
    }

    return bestMentor.username;
  }

  async scheduleOnboardingCheckpoints(onboardingPlan) {
    const checkpoints = [
      { day: 3, name: 'First Week Check-in' },
      { day: 7, name: 'End of Week 1 Review' },
      { day: 14, name: 'Two Week Milestone' },
      { day: 30, name: 'One Month Review' }
    ];

    for (const checkpoint of checkpoints) {
      const checkpointDate = new Date(
        onboardingPlan.startDate.getTime() +
        checkpoint.day * 24 * 60 * 60 * 1000
      );

      // 创建检查点任务
      await this.createOnboardingTask({
        title: `Onboarding Checkpoint: ${checkpoint.name}`,
        description: `
Scheduled check-in for ${onboardingPlan.memberName}'s onboarding progress.

**Agenda:**
- Review completed tasks and progress
- Discuss any challenges or blockers
- Provide feedback and guidance
- Adjust onboarding plan if needed
- Set goals for next phase

**Participants:**
- ${onboardingPlan.memberName} (New team member)
- ${await this.getMentorName({ id: onboardingPlan.memberId }, onboardingPlan.role)} (Mentor)
- Team Lead

**Duration:** 30-45 minutes
        `,
        assignee: await this.getMentorName({ id: onboardingPlan.memberId }, onboardingPlan.role),
        dueDate: checkpointDate,
        priority: 'medium',
        tags: ['onboarding', 'checkpoint', 'meeting'],
        metadata: {
          onboardingCheckpoint: true,
          newMemberId: onboardingPlan.memberId,
          checkpointDay: checkpoint.day
        }
      });
    }
  }
}
```

## 比较分析和迁移指南

### 性能比较分析

#### 不同命令方法的性能对比

**任务列表查询性能对比：**

| 方法 | 小规模 (< 100 tasks) | 中规模 (100-1000 tasks) | 大规模 (> 1000 tasks) | 内存使用 | 推荐场景 |
|------|---------------------|------------------------|----------------------|----------|----------|
| `list` | 50ms | 200ms | 1.2s | 低 | 基本查看 |
| `list --format=json` | 45ms | 180ms | 1.0s | 低 | 脚本处理 |
| `list pending` | 30ms | 120ms | 600ms | 低 | 状态过滤 |
| `list --with-subtasks` | 80ms | 400ms | 2.5s | 中 | 详细视图 |
| `list --tree` | 100ms | 500ms | 3.2s | 中 | 层次结构 |
| `status` | 25ms | 100ms | 400ms | 低 | 快速概览 |

**AI 提供商性能对比：**

| 提供商 | 平均响应时间 | 成本 (每1K tokens) | 准确性 | 推荐用途 |
|--------|-------------|-------------------|--------|----------|
| Claude 3.5 Sonnet | 2.3s | $0.003 | 95% | 复杂任务分析、代码生成 |
| GPT-4 | 3.1s | $0.03 | 93% | 通用任务处理 |
| GPT-3.5 Turbo | 1.8s | $0.002 | 88% | 简单任务、快速响应 |
| Perplexity | 4.2s | $0.001 | 90% | 研究增强模式 |

**最佳实践建议：**

```javascript
// 性能优化配置示例
const performanceConfig = {
  // 根据项目规模选择合适的策略
  smallProject: {
    taskCount: '< 100',
    recommendations: [
      '使用默认 JSON 存储',
      '启用基本缓存',
      '使用 GPT-3.5 Turbo 降低成本'
    ]
  },

  mediumProject: {
    taskCount: '100-1000',
    recommendations: [
      '考虑 SQLite 数据库',
      '启用智能缓存',
      '使用 Claude 3.5 Sonnet 提高准确性',
      '实施批量操作'
    ]
  },

  largeProject: {
    taskCount: '> 1000',
    recommendations: [
      '迁移到 PostgreSQL',
      '实施数据分页',
      '使用连接池',
      '启用分布式缓存',
      '考虑微服务架构'
    ]
  }
};

// 自动性能调优
class PerformanceOptimizer {
  static async autoOptimize() {
    const taskCount = await getTaskCount();
    const config = this.getOptimalConfig(taskCount);

    console.log(`📊 检测到 ${taskCount} 个任务，应用 ${config.level} 级别优化`);

    // 应用优化配置
    await this.applyOptimizations(config);
  }

  static getOptimalConfig(taskCount) {
    if (taskCount < 100) return performanceConfig.smallProject;
    if (taskCount < 1000) return performanceConfig.mediumProject;
    return performanceConfig.largeProject;
  }
}
```

### 从其他工具的迁移路径

#### 从 Jira 迁移

**迁移策略：**

```javascript
// migrations/jira-migration.js
class JiraMigrationTool {
  constructor(jiraConfig) {
    this.jira = new JiraAPI(jiraConfig);
    this.migrationMap = new Map();
  }

  async migrateFromJira(projectKey, options = {}) {
    console.log(`🔄 开始从 Jira 项目 ${projectKey} 迁移...`);

    const migration = {
      startTime: new Date(),
      projectKey,
      stats: { issues: 0, epics: 0, subtasks: 0, errors: 0 },
      mapping: {},
      errors: []
    };

    try {
      // 1. 获取所有问题
      const issues = await this.fetchAllIssues(projectKey);
      console.log(`📋 找到 ${issues.length} 个 Jira 问题`);

      // 2. 分析问题类型和层次结构
      const analysis = this.analyzeIssueStructure(issues);

      // 3. 创建迁移映射
      const mappingStrategy = this.createMappingStrategy(analysis);

      // 4. 执行迁移
      for (const issue of issues) {
        try {
          const task = await this.convertJiraIssueToTask(issue, mappingStrategy);
          const createdTask = await createTask(task);

          this.migrationMap.set(issue.key, createdTask.id);
          migration.mapping[issue.key] = createdTask.id;
          migration.stats.issues++;

        } catch (error) {
          migration.errors.push({
            issueKey: issue.key,
            error: error.message
          });
          migration.stats.errors++;
        }
      }

      // 5. 处理依赖关系
      await this.migrateDependencies(issues);

      // 6. 迁移历史记录
      if (options.includeHistory) {
        await this.migrateHistory(issues);
      }

      migration.endTime = new Date();
      migration.duration = migration.endTime - migration.startTime;

      console.log(`✅ 迁移完成！成功: ${migration.stats.issues}, 错误: ${migration.stats.errors}`);

      return migration;

    } catch (error) {
      console.error(`❌ 迁移失败:`, error.message);
      throw error;
    }
  }

  convertJiraIssueToTask(issue, mappingStrategy) {
    const task = {
      title: issue.fields.summary,
      description: this.convertJiraDescription(issue.fields.description),
      status: mappingStrategy.statusMapping[issue.fields.status.name] || 'pending',
      priority: mappingStrategy.priorityMapping[issue.fields.priority.name] || 'medium',
      complexity: this.estimateComplexityFromJira(issue),
      estimatedHours: this.extractTimeEstimate(issue),
      assignee: issue.fields.assignee ? issue.fields.assignee.displayName : null,
      tags: this.extractTags(issue),
      metadata: {
        jiraKey: issue.key,
        jiraType: issue.fields.issuetype.name,
        jiraProject: issue.fields.project.key,
        migratedAt: new Date().toISOString(),
        originalCreated: issue.fields.created,
        originalUpdated: issue.fields.updated
      }
    };

    // 处理 Epic 链接
    if (issue.fields.epic) {
      task.metadata.epicKey = issue.fields.epic.key;
    }

    // 处理自定义字段
    if (issue.fields.customfield_10001) { // Story Points
      task.complexity = Math.min(10, Math.max(1, issue.fields.customfield_10001));
    }

    return task;
  }

  createMappingStrategy(analysis) {
    return {
      statusMapping: {
        'To Do': 'pending',
        'In Progress': 'in-progress',
        'In Review': 'review',
        'Done': 'done',
        'Closed': 'done',
        'Cancelled': 'cancelled'
      },

      priorityMapping: {
        'Highest': 'high',
        'High': 'high',
        'Medium': 'medium',
        'Low': 'low',
        'Lowest': 'low'
      },

      typeMapping: {
        'Epic': 'epic',
        'Story': 'feature',
        'Task': 'task',
        'Bug': 'bug',
        'Sub-task': 'subtask'
      }
    };
  }
}

// 使用示例
const jiraMigration = new JiraMigrationTool({
  host: 'https://company.atlassian.net',
  username: '<EMAIL>',
  password: 'api-token'
});

await jiraMigration.migrateFromJira('PROJ', {
  includeHistory: true,
  includeAttachments: false
});
```

#### 从 Trello 迁移

```javascript
// migrations/trello-migration.js
class TrelloMigrationTool {
  constructor(trelloConfig) {
    this.trello = new TrelloAPI(trelloConfig);
  }

  async migrateFromTrello(boardId, options = {}) {
    console.log(`🔄 开始从 Trello 看板迁移...`);

    // 获取看板信息
    const board = await this.trello.getBoard(boardId);
    const lists = await this.trello.getLists(boardId);
    const cards = await this.trello.getCards(boardId);

    // 创建状态映射
    const statusMapping = this.createTrelloStatusMapping(lists);

    const migration = {
      boardName: board.name,
      totalCards: cards.length,
      converted: [],
      errors: []
    };

    for (const card of cards) {
      try {
        const task = {
          title: card.name,
          description: card.desc || '',
          status: statusMapping[card.idList] || 'pending',
          priority: this.extractPriorityFromTrello(card),
          complexity: this.estimateComplexityFromTrello(card),
          assignee: card.members.length > 0 ? card.members[0].fullName : null,
          tags: card.labels.map(label => label.name),
          dueDate: card.due ? new Date(card.due) : null,
          metadata: {
            trelloId: card.id,
            trelloUrl: card.url,
            boardName: board.name,
            listName: lists.find(l => l.id === card.idList)?.name
          }
        };

        const createdTask = await createTask(task);
        migration.converted.push({
          trelloId: card.id,
          taskId: createdTask.id,
          title: card.name
        });

      } catch (error) {
        migration.errors.push({
          cardId: card.id,
          cardName: card.name,
          error: error.message
        });
      }
    }

    return migration;
  }

  createTrelloStatusMapping(lists) {
    const mapping = {};

    lists.forEach(list => {
      const listName = list.name.toLowerCase();

      if (listName.includes('todo') || listName.includes('backlog')) {
        mapping[list.id] = 'pending';
      } else if (listName.includes('doing') || listName.includes('progress')) {
        mapping[list.id] = 'in-progress';
      } else if (listName.includes('review') || listName.includes('testing')) {
        mapping[list.id] = 'review';
      } else if (listName.includes('done') || listName.includes('complete')) {
        mapping[list.id] = 'done';
      } else {
        mapping[list.id] = 'pending'; // 默认状态
      }
    });

    return mapping;
  }
}
```

### 最终最佳实践总结

#### 项目设置最佳实践

```bash
# 1. 项目初始化最佳实践
task-master init --name="Project Name" --template=agile-team

# 2. AI 提供商配置
task-master models --setup
# 配置主要提供商（Claude）用于复杂任务
# 配置备用提供商（GPT-3.5）用于简单任务
# 配置研究提供商（Perplexity）用于需求分析

# 3. 团队配置
task-master config --set team.size=10
task-master config --set team.timezone="UTC+8"
task-master config --set team.workingHours="9:00-18:00"

# 4. 集成配置
task-master config --setup-jira
task-master config --setup-slack
task-master config --setup-github
```

#### 日常工作流最佳实践

```bash
# 晨间启动流程
task-master workflows/smart-flow morning

# 或手动执行
task-master status
task-master list blocked
task-master next --context=morning --energy-level=high

# 任务开始
task-master set-status --id=5 --status=in-progress --start-timer

# 任务完成
task-master set-status --id=5 --status=done --log-time=4.5h
task-master next

# 日终总结
task-master workflows/smart-flow evening
task-master sync-readme
```

#### 团队协作最佳实践

```bash
# 团队状态检查
task-master status --team-view
task-master utils/analyze team

# 工作负载平衡
task-master list unassigned
task-master workflows/auto-assign --balance-workload

# 依赖管理
task-master validate-dependencies --fix-auto
task-master utils/analyze dependencies --critical-path

# 性能监控
task-master utils/benchmark --save-baseline
task-master utils/analyze velocity
```

#### 扩展和维护最佳实践

```bash
# 定期维护
task-master utils/cleanup --older-than=90d
task-master utils/backup --compress
task-master utils/optimize --auto

# 性能监控
task-master diagnostics --full
task-master utils/analyze performance

# 数据迁移（当需要时）
task-master migrate-database --from=json --to=postgresql --dry-run
task-master migrate-database --from=json --to=postgresql --create-backup
```

#### 安全和合规最佳实践

```javascript
// 企业安全配置示例
const securityConfig = {
  authentication: {
    provider: 'ldap', // 或 'saml', 'oauth2'
    sessionTimeout: 8 * 60 * 60 * 1000, // 8小时
    requireMFA: true
  },

  authorization: {
    rbac: true,
    defaultRole: 'viewer',
    adminRoles: ['system_admin', 'project_admin']
  },

  dataProtection: {
    encryptSensitiveFields: true,
    auditLogging: true,
    dataRetention: 365, // 天
    backupEncryption: true
  },

  compliance: {
    gdprCompliant: true,
    hipaaCompliant: false,
    sox: true
  }
};
```

## 结论

这个 Task Master 命令系统代表了现代项目管理工具的巅峰实现，它不仅仅是一个任务管理工具，更是一个完整的智能项目管理生态系统。通过本文档的详细分析，我们可以看到：

### 核心优势

1. **AI 驱动的智能化**：从自然语言处理到智能任务推荐，AI 技术贯穿整个系统
2. **高度可扩展性**：从个人项目到企业级部署，支持各种规模的需求
3. **完整的生命周期管理**：从需求分析到项目交付的全流程覆盖
4. **强大的集成能力**：与现有工具和流程的无缝集成
5. **企业级安全性**：完善的安全机制和合规性支持

### 技术创新

1. **自然语言命令接口**：降低学习成本，提高使用效率
2. **智能工作流引擎**：基于上下文的自动化决策
3. **多 AI 提供商支持**：灵活的 AI 服务选择和切换
4. **实时依赖分析**：复杂项目的智能管理
5. **性能优化策略**：从小规模到大规模的自动优化

### 实际价值

对于开发团队而言，这个系统能够：
- **提高效率**：自动化重复性工作，智能任务分配
- **改善协作**：透明的进度跟踪，有效的沟通机制
- **降低风险**：依赖分析，瓶颈识别，风险预警
- **提升质量**：标准化流程，最佳实践集成
- **支持决策**：数据驱动的项目洞察和建议

这个系统不仅解决了当前项目管理的痛点，更为未来的智能化项目管理奠定了基础。它展示了 AI 技术在项目管理领域的巨大潜力，为团队提供了一个真正智能、高效、可扩展的项目管理解决方案。
```
```
```
```
```
```
```
```
```
```
```
```
```

#### 依赖管理系统技术实现

**依赖图数据结构：**
```javascript
class DependencyGraph {
  constructor() {
    this.nodes = new Map(); // taskId -> task
    this.edges = new Map(); // taskId -> Set of dependent taskIds
    this.reverseEdges = new Map(); // taskId -> Set of dependency taskIds
  }

  addTask(task) {
    this.nodes.set(task.id, task);
    if (!this.edges.has(task.id)) {
      this.edges.set(task.id, new Set());
    }
    if (!this.reverseEdges.has(task.id)) {
      this.reverseEdges.set(task.id, new Set());
    }
  }

  addDependency(taskId, dependsOnId) {
    // taskId depends on dependsOnId
    this.reverseEdges.get(taskId).add(dependsOnId);
    this.edges.get(dependsOnId).add(taskId);
  }

  removeDependency(taskId, dependsOnId) {
    this.reverseEdges.get(taskId).delete(dependsOnId);
    this.edges.get(dependsOnId).delete(taskId);
  }

  // 检测循环依赖
  detectCycles() {
    const visited = new Set();
    const recursionStack = new Set();
    const cycles = [];

    for (const taskId of this.nodes.keys()) {
      if (!visited.has(taskId)) {
        this.dfsDetectCycle(taskId, visited, recursionStack, [], cycles);
      }
    }

    return cycles;
  }

  dfsDetectCycle(taskId, visited, recursionStack, path, cycles) {
    visited.add(taskId);
    recursionStack.add(taskId);
    path.push(taskId);

    for (const dependentId of this.edges.get(taskId)) {
      if (!visited.has(dependentId)) {
        this.dfsDetectCycle(dependentId, visited, recursionStack, path, cycles);
      } else if (recursionStack.has(dependentId)) {
        // 找到循环
        const cycleStart = path.indexOf(dependentId);
        const cycle = path.slice(cycleStart).concat([dependentId]);
        cycles.push(cycle);
      }
    }

    recursionStack.delete(taskId);
    path.pop();
  }

  // 计算关键路径
  calculateCriticalPath() {
    const distances = new Map();
    const predecessors = new Map();

    // 初始化距离
    for (const taskId of this.nodes.keys()) {
      distances.set(taskId, 0);
      predecessors.set(taskId, null);
    }

    // 拓扑排序
    const sorted = this.topologicalSort();

    // 计算最长路径
    for (const taskId of sorted) {
      const task = this.nodes.get(taskId);
      const currentDistance = distances.get(taskId);

      for (const dependentId of this.edges.get(taskId)) {
        const newDistance = currentDistance + (task.estimatedHours || 0);
        if (newDistance > distances.get(dependentId)) {
          distances.set(dependentId, newDistance);
          predecessors.set(dependentId, taskId);
        }
      }
    }

    // 找到最长路径
    let maxDistance = 0;
    let endTask = null;
    for (const [taskId, distance] of distances) {
      if (distance > maxDistance) {
        maxDistance = distance;
        endTask = taskId;
      }
    }

    // 重建路径
    const path = [];
    let current = endTask;
    while (current !== null) {
      path.unshift(current);
      current = predecessors.get(current);
    }

    return {
      path,
      totalHours: maxDistance,
      tasks: path.map(id => this.nodes.get(id))
    };
  }

  topologicalSort() {
    const inDegree = new Map();
    const queue = [];
    const result = [];

    // 计算入度
    for (const taskId of this.nodes.keys()) {
      inDegree.set(taskId, this.reverseEdges.get(taskId).size);
      if (inDegree.get(taskId) === 0) {
        queue.push(taskId);
      }
    }

    while (queue.length > 0) {
      const taskId = queue.shift();
      result.push(taskId);

      for (const dependentId of this.edges.get(taskId)) {
        inDegree.set(dependentId, inDegree.get(dependentId) - 1);
        if (inDegree.get(dependentId) === 0) {
          queue.push(dependentId);
        }
      }
    }

    return result;
  }
}
```

**validate-dependencies.md 实现：**
```javascript
async function validateDependencies(options = {}) {
  const tasks = await getAllTasks();
  const graph = new DependencyGraph();

  // 构建依赖图
  for (const task of tasks) {
    graph.addTask(task);
    for (const depId of task.dependencies || []) {
      graph.addDependency(task.id, depId);
    }
  }

  const validationResults = {
    errors: [],
    warnings: [],
    info: [],
    graph: graph
  };

  // 1. 检测循环依赖
  const cycles = graph.detectCycles();
  for (const cycle of cycles) {
    validationResults.errors.push({
      type: 'circular_dependency',
      cycle: cycle,
      message: `Circular dependency detected: ${cycle.map(id => `#${id}`).join(' → ')}`
    });
  }

  // 2. 检测缺失依赖
  for (const task of tasks) {
    for (const depId of task.dependencies || []) {
      if (!graph.nodes.has(depId)) {
        validationResults.errors.push({
          type: 'missing_dependency',
          taskId: task.id,
          missingDepId: depId,
          message: `Task #${task.id} depends on non-existent task #${depId}`
        });
      }
    }
  }

  // 3. 检测逻辑问题
  for (const task of tasks) {
    for (const depId of task.dependencies || []) {
      const depTask = graph.nodes.get(depId);
      if (depTask) {
        // 已完成任务不应依赖未完成任务
        if (task.status === 'done' && depTask.status !== 'done') {
          validationResults.warnings.push({
            type: 'logical_inconsistency',
            taskId: task.id,
            depId: depId,
            message: `Completed task #${task.id} depends on incomplete task #${depId}`
          });
        }

        // 取消的任务在依赖链中
        if (depTask.status === 'cancelled') {
          validationResults.warnings.push({
            type: 'cancelled_dependency',
            taskId: task.id,
            depId: depId,
            message: `Task #${task.id} depends on cancelled task #${depId}`
          });
        }
      }
    }
  }

  // 4. 复杂度警告
  for (const task of tasks) {
    const depCount = task.dependencies?.length || 0;
    const dependentCount = graph.edges.get(task.id).size;

    if (depCount > 5) {
      validationResults.warnings.push({
        type: 'high_dependency_count',
        taskId: task.id,
        count: depCount,
        message: `Task #${task.id} has ${depCount} dependencies (consider breaking down)`
      });
    }

    if (dependentCount > 5) {
      validationResults.warnings.push({
        type: 'high_dependent_count',
        taskId: task.id,
        count: dependentCount,
        message: `Task #${task.id} blocks ${dependentCount} other tasks (potential bottleneck)`
      });
    }
  }

  // 5. 关键路径分析
  const criticalPath = graph.calculateCriticalPath();
  validationResults.info.push({
    type: 'critical_path',
    path: criticalPath.path,
    totalHours: criticalPath.totalHours,
    message: `Critical path: ${criticalPath.path.map(id => `#${id}`).join(' → ')} (${criticalPath.totalHours} hours)`
  });

  return validationResults;
}
```

**fix-dependencies.md 自动修复实现：**
```javascript
class DependencyFixer {
  constructor(validationResults) {
    this.results = validationResults;
    this.fixes = [];
    this.manualReviewRequired = [];
  }

  async autoFix() {
    // 1. 修复缺失依赖
    await this.fixMissingDependencies();

    // 2. 修复简单循环依赖
    await this.fixSimpleCycles();

    // 3. 清理重复依赖
    await this.cleanupDuplicateDependencies();

    // 4. 移除自依赖
    await this.removeSelfDependencies();

    return {
      automaticFixes: this.fixes,
      manualReviewRequired: this.manualReviewRequired,
      summary: this.generateFixSummary()
    };
  }

  async fixMissingDependencies() {
    const missingDepErrors = this.results.errors.filter(e => e.type === 'missing_dependency');

    for (const error of missingDepErrors) {
      const task = await getTask(error.taskId);
      const updatedDependencies = task.dependencies.filter(id => id !== error.missingDepId);

      await updateTask(error.taskId, { dependencies: updatedDependencies });

      this.fixes.push({
        type: 'removed_missing_dependency',
        taskId: error.taskId,
        removedDepId: error.missingDepId,
        description: `Removed reference to non-existent task #${error.missingDepId}`
      });
    }
  }

  async fixSimpleCycles() {
    const cycles = this.results.errors.filter(e => e.type === 'circular_dependency');

    for (const cycleError of cycles) {
      const cycle = cycleError.cycle;

      if (cycle.length === 2) {
        // 简单的双向依赖，移除其中一个
        const [taskA, taskB] = cycle;
        const taskAObj = await getTask(taskA);
        const taskBObj = await getTask(taskB);

        // 选择优先级较低的任务移除依赖
        let taskToModify, depToRemove;
        if (this.getPriorityValue(taskAObj.priority) <= this.getPriorityValue(taskBObj.priority)) {
          taskToModify = taskA;
          depToRemove = taskB;
        } else {
          taskToModify = taskB;
          depToRemove = taskA;
        }

        const task = await getTask(taskToModify);
        const updatedDependencies = task.dependencies.filter(id => id !== depToRemove);
        await updateTask(taskToModify, { dependencies: updatedDependencies });

        this.fixes.push({
          type: 'resolved_simple_cycle',
          modifiedTask: taskToModify,
          removedDependency: depToRemove,
          description: `Broke cycle by removing dependency: #${taskToModify} no longer depends on #${depToRemove}`
        });
      } else {
        // 复杂循环需要手动审查
        this.manualReviewRequired.push({
          type: 'complex_cycle',
          cycle: cycle,
          suggestion: `Consider removing dependency from #${cycle[cycle.length - 1]} to #${cycle[0]}`,
          description: `Complex circular dependency requires manual review: ${cycle.map(id => `#${id}`).join(' → ')}`
        });
      }
    }
  }

  getPriorityValue(priority) {
    const values = { low: 1, medium: 2, high: 3 };
    return values[priority] || 1;
  }
}
```

#### 工作流自动化系统

**smart-workflow.md 上下文分析引擎：**
```javascript
class WorkflowContextAnalyzer {
  constructor() {
    this.patterns = new Map();
    this.userBehavior = new Map();
    this.timePatterns = new Map();
  }

  async analyzeContext() {
    const context = {
      recentCommands: await this.getRecentCommands(),
      projectState: await this.getProjectState(),
      timeContext: this.getTimeContext(),
      userPatterns: await this.getUserPatterns(),
      workingSession: await this.getWorkingSession()
    };

    return this.generateWorkflowRecommendation(context);
  }

  async getRecentCommands(limit = 10) {
    const commandHistory = await readCommandHistory();
    return commandHistory.slice(-limit).map(cmd => ({
      command: cmd.command,
      timestamp: cmd.timestamp,
      success: cmd.success,
      context: cmd.context
    }));
  }

  async getProjectState() {
    const tasks = await getAllTasks();
    const inProgress = tasks.filter(t => t.status === 'in-progress');
    const pending = tasks.filter(t => t.status === 'pending');
    const blocked = tasks.filter(t => this.isTaskBlocked(t));

    return {
      totalTasks: tasks.length,
      inProgress: inProgress.length,
      pending: pending.length,
      blocked: blocked.length,
      completionRate: tasks.filter(t => t.status === 'done').length / tasks.length,
      avgComplexity: tasks.reduce((sum, t) => sum + (t.complexity || 0), 0) / tasks.length
    };
  }

  getTimeContext() {
    const now = new Date();
    const hour = now.getHours();
    const dayOfWeek = now.getDay();

    return {
      timeOfDay: this.categorizeTimeOfDay(hour),
      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek],
      isWorkingHours: hour >= 9 && hour <= 17 && dayOfWeek >= 1 && dayOfWeek <= 5,
      sessionDuration: this.calculateSessionDuration()
    };
  }

  async generateWorkflowRecommendation(context) {
    const recommendations = [];

    // 基于最近命令的推荐
    const lastCommand = context.recentCommands[0];
    if (lastCommand) {
      switch (lastCommand.command) {
        case 'status':
          if (context.timeContext.timeOfDay === 'morning') {
            recommendations.push({
              type: 'daily_standup',
              priority: 'high',
              reason: 'Morning status check suggests starting daily workflow'
            });
          }
          break;

        case 'complete':
          recommendations.push({
            type: 'find_next_task',
            priority: 'high',
            reason: 'Task completed, ready for next work item'
          });
          break;

        case 'list':
          if (context.projectState.pending > 10) {
            recommendations.push({
              type: 'sprint_planning',
              priority: 'medium',
              reason: 'Many pending tasks suggest need for planning'
            });
          }
          break;
      }
    }

    // 基于项目状态的推荐
    if (context.projectState.blocked > 0) {
      recommendations.push({
        type: 'dependency_resolution',
        priority: 'high',
        reason: `${context.projectState.blocked} tasks are blocked`
      });
    }

    if (context.projectState.inProgress === 0 && context.projectState.pending > 0) {
      recommendations.push({
        type: 'start_next_task',
        priority: 'high',
        reason: 'No active work, ready to start new task'
      });
    }

    // 基于时间模式的推荐
    if (context.timeContext.timeOfDay === 'end_of_day') {
      recommendations.push({
        type: 'daily_wrap_up',
        priority: 'medium',
        reason: 'End of day - time for status update and planning'
      });
    }

    return this.prioritizeRecommendations(recommendations);
  }

  async executeWorkflow(workflowType, context) {
    const workflows = {
      daily_standup: () => this.executeDailyStandup(context),
      find_next_task: () => this.executeFindNextTask(context),
      sprint_planning: () => this.executeSprintPlanning(context),
      dependency_resolution: () => this.executeDependencyResolution(context),
      start_next_task: () => this.executeStartNextTask(context),
      daily_wrap_up: () => this.executeDailyWrapUp(context)
    };

    const workflow = workflows[workflowType];
    if (workflow) {
      return await workflow();
    } else {
      throw new Error(`Unknown workflow type: ${workflowType}`);
    }
  }

  async executeDailyStandup(context) {
    const steps = [
      { name: 'Show project status', command: 'status' },
      { name: 'List in-progress tasks', command: 'list in-progress' },
      { name: 'Check for blockers', command: 'list blocked' },
      { name: 'Recommend next actions', command: 'next' }
    ];

    const results = [];
    for (const step of steps) {
      try {
        const result = await executeCommand(step.command);
        results.push({ step: step.name, success: true, result });
      } catch (error) {
        results.push({ step: step.name, success: false, error: error.message });
      }
    }

    return {
      workflowType: 'daily_standup',
      steps: results,
      summary: this.generateStandupSummary(results),
      nextActions: this.generateNextActions(results)
    };
  }
}
```

**command-pipeline.md 管道执行引擎：**
```javascript
class CommandPipeline {
  constructor() {
    this.variables = new Map();
    this.context = new Map();
  }

  async execute(pipelineSpec) {
    const pipeline = this.parsePipelineSpec(pipelineSpec);
    const results = [];

    for (const stage of pipeline.stages) {
      try {
        const result = await this.executeStage(stage);
        results.push(result);

        // 更新上下文变量
        if (stage.outputVariable) {
          this.variables.set(stage.outputVariable, result.output);
        }

        // 检查条件分支
        if (stage.condition && !this.evaluateCondition(stage.condition)) {
          break;
        }

      } catch (error) {
        if (stage.errorHandling === 'continue') {
          results.push({ stage: stage.name, error: error.message, skipped: true });
          continue;
        } else if (stage.errorHandling === 'retry') {
          const retryResult = await this.retryStage(stage);
          results.push(retryResult);
        } else {
          throw error;
        }
      }
    }

    return {
      pipeline: pipeline.name,
      results,
      variables: Object.fromEntries(this.variables),
      summary: this.generatePipelineSummary(results)
    };
  }

  parsePipelineSpec(spec) {
    // 解析管道规范
    // 支持格式：command1 → command2 → if:condition → command3
    const stages = [];
    const parts = spec.split('→').map(p => p.trim());

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];

      if (part.startsWith('if:')) {
        // 条件分支
        const condition = part.substring(3);
        stages.push({
          type: 'condition',
          condition: condition,
          name: `Condition: ${condition}`
        });
      } else if (part.startsWith('for:')) {
        // 循环
        const iterator = part.substring(4);
        stages.push({
          type: 'loop',
          iterator: iterator,
          name: `Loop: ${iterator}`
        });
      } else if (part.startsWith('parallel:')) {
        // 并行执行
        const commands = part.substring(9).split('|').map(c => c.trim());
        stages.push({
          type: 'parallel',
          commands: commands,
          name: `Parallel: ${commands.join(', ')}`
        });
      } else {
        // 普通命令
        stages.push({
          type: 'command',
          command: part,
          name: part
        });
      }
    }

    return {
      name: `Pipeline_${Date.now()}`,
      stages
    };
  }

  async executeStage(stage) {
    switch (stage.type) {
      case 'command':
        return await this.executeCommand(stage.command);
      case 'condition':
        return await this.evaluateCondition(stage.condition);
      case 'loop':
        return await this.executeLoop(stage);
      case 'parallel':
        return await this.executeParallel(stage);
      default:
        throw new Error(`Unknown stage type: ${stage.type}`);
    }
  }

  async executeCommand(command) {
    // 替换变量
    const expandedCommand = this.expandVariables(command);

    // 执行命令
    const startTime = Date.now();
    const result = await taskMasterCLI.execute(expandedCommand);
    const endTime = Date.now();

    return {
      command: expandedCommand,
      result,
      duration: endTime - startTime,
      timestamp: new Date().toISOString()
    };
  }

  expandVariables(command) {
    let expanded = command;
    for (const [key, value] of this.variables) {
      expanded = expanded.replace(new RegExp(`\\$${key}`, 'g'), value);
    }
    return expanded;
  }
}
```

**实际管道使用示例：**
```bash
# 项目设置管道
$ task-master pipeline "init → parse-prd requirements.md → analyze-complexity → expand-all → status"
🔄 Executing Pipeline: Project Setup
├── ✅ Stage 1: init (2.3s)
├── ✅ Stage 2: parse-prd requirements.md (15.7s)
│   └── Generated 12 tasks
├── ✅ Stage 3: analyze-complexity (8.2s)
│   └── Found 3 high-complexity tasks
├── ✅ Stage 4: expand-all (12.1s)
│   └── Expanded 3 tasks into 14 subtasks
└── ✅ Stage 5: status (1.1s)

📊 Pipeline Summary:
   Total time: 39.4 seconds
   Tasks created: 12 + 14 subtasks
   Ready for development: 8 tasks
   Needs review: 3 high-complexity expansions

# 条件管道
$ task-master pipeline "status → if:pending>10 → sprint-plan → else → next"
🔄 Executing Conditional Pipeline
├── ✅ Stage 1: status (0.8s)
│   └── Found 15 pending tasks
├── ✅ Stage 2: Condition: pending>10 (0.1s)
│   └── Condition met: 15 > 10
├── ✅ Stage 3: sprint-plan (5.2s)
│   └── Created 2-week sprint with 12 tasks
└── ⏭️  Stage 4: Skipped (else branch)

# 并行管道
$ task-master pipeline "parallel:[analyze-complexity | validate-dependencies | generate-tasks]"
🔄 Executing Parallel Pipeline
├── ⚡ Parallel execution started
│   ├── ✅ analyze-complexity (8.1s)
│   ├── ✅ validate-dependencies (3.2s)
│   └── ✅ generate-tasks (12.5s)
└── ✅ Parallel execution completed (12.5s)

Results merged:
- Complexity analysis: 3 tasks need expansion
- Dependencies: All valid, no issues
- Generated: 45 task files in .taskmaster/tasks/
```
```
```
