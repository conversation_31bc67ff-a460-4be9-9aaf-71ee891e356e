import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Alert,
  Divider,
  List,
  Tag,
  message
} from 'antd';
import {
  LockOutlined,
  SafetyOutlined,
  PhoneOutlined,
  MailOutlined,
  KeyOutlined,
  ShieldOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';

const { Title, Text } = Typography;

const SecurityItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .item-info {
    flex: 1;
    
    .item-title {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .item-desc {
      color: #8c8c8c;
      font-size: 14px;
    }
  }
  
  .item-status {
    margin-right: 16px;
  }
`;

const ProfileSecurity: React.FC = () => {
  const { user } = useAuthStore();
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handlePasswordChange = async (values: any) => {
    try {
      setLoading(true);
      // 这里应该调用修改密码的API
      // const response = await userService.changePassword(values);
      // if (response.success) {
      //   message.success('密码修改成功');
      //   passwordForm.resetFields();
      // }
      
      // 模拟API调用
      setTimeout(() => {
        message.success('密码修改成功');
        passwordForm.resetFields();
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('修改密码失败');
      setLoading(false);
    }
  };

  const securityItems = [
    {
      icon: <PhoneOutlined />,
      title: '手机号码',
      description: '已绑定手机号，用于登录和安全验证',
      value: user?.phone,
      status: user?.phone_verified ? 'verified' : 'unverified',
      action: '更换'
    },
    {
      icon: <MailOutlined />,
      title: '邮箱地址',
      description: '用于接收重要通知和找回密码',
      value: user?.email || '未绑定',
      status: user?.email_verified ? 'verified' : 'unverified',
      action: user?.email ? '更换' : '绑定'
    },
    {
      icon: <LockOutlined />,
      title: '登录密码',
      description: '定期更换密码可以提高账户安全性',
      value: '已设置',
      status: 'set',
      action: '修改'
    }
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'verified':
        return <Tag color="green">已验证</Tag>;
      case 'unverified':
        return <Tag color="orange">未验证</Tag>;
      case 'set':
        return <Tag color="blue">已设置</Tag>;
      default:
        return <Tag color="default">未设置</Tag>;
    }
  };

  return (
    <div>
      <Card style={{ marginBottom: 24 }}>
        <Title level={3}>账户安全</Title>
        
        <Alert
          message="安全提示"
          description="为了您的账户安全，建议定期更换密码，并绑定手机号和邮箱用于安全验证。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <div>
          {securityItems.map((item, index) => (
            <SecurityItem key={index}>
              <div className="item-info">
                <div className="item-title">
                  <Space>
                    {item.icon}
                    {item.title}
                  </Space>
                </div>
                <div className="item-desc">{item.description}</div>
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary">{item.value}</Text>
                </div>
              </div>
              <div className="item-status">
                {getStatusTag(item.status)}
              </div>
              <Button size="small" type="link">
                {item.action}
              </Button>
            </SecurityItem>
          ))}
        </div>
      </Card>

      <Card>
        <Title level={4}>修改密码</Title>
        
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handlePasswordChange}
          style={{ maxWidth: 400 }}
        >
          <Form.Item
            name="currentPassword"
            label="当前密码"
            rules={[
              { required: true, message: '请输入当前密码' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入当前密码"
            />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' },
              { max: 20, message: '密码长度不能超过20位' }
            ]}
          >
            <Input.Password
              prefix={<KeyOutlined />}
              placeholder="请输入新密码"
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<ShieldOutlined />}
              placeholder="请再次输入新密码"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                修改密码
              </Button>
              <Button onClick={() => passwordForm.resetFields()}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ProfileSecurity;
