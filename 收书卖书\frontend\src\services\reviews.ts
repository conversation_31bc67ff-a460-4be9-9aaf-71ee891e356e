import api from './api';
import { ApiResponse, PaginationResponse } from '../types';

export interface Review {
  id: string;
  user_id: string;
  book_id: string;
  order_id?: string;
  rating: number;
  title?: string;
  content: string;
  images: string[];
  helpful_count: number;
  status: 'pending' | 'approved' | 'rejected';
  is_anonymous: boolean;
  reply_to?: string;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    username: string;
    avatar?: string;
  };
  book?: {
    id: string;
    title: string;
    cover_image?: string;
    author?: string;
  };
  replies?: Review[];
  user_helpful?: boolean | null;
}

export interface ReviewStats {
  total_reviews: number;
  average_rating: number;
  rating_distribution: Record<string, number>;
}

export interface CreateReviewData {
  book_id: string;
  order_id?: string;
  rating: number;
  title?: string;
  content: string;
  images?: string[];
  is_anonymous?: boolean;
  reply_to?: string;
}

export const reviewsService = {
  // 获取图书评论列表
  async getBookReviews(
    bookId: string,
    params: {
      page?: number;
      limit?: number;
      sort?: string;
      rating?: number;
      has_images?: boolean;
    } = {}
  ): Promise<PaginationResponse<Review>> {
    const response = await api.get(`/reviews/book/${bookId}`, { params });
    return response.data;
  },

  // 获取图书评论统计
  async getBookReviewStats(bookId: string): Promise<ApiResponse<ReviewStats>> {
    const response = await api.get(`/reviews/book/${bookId}/stats`);
    return response.data;
  },

  // 创建评论
  async createReview(data: CreateReviewData): Promise<ApiResponse<Review>> {
    const response = await api.post('/reviews', data);
    return response.data;
  },

  // 标记评论有用/无用
  async markHelpful(
    reviewId: string, 
    isHelpful: boolean
  ): Promise<ApiResponse<{ is_helpful: boolean | null }>> {
    const response = await api.post(`/reviews/${reviewId}/helpful`, {
      is_helpful: isHelpful
    });
    return response.data;
  },

  // 删除评论
  async deleteReview(reviewId: string): Promise<ApiResponse> {
    const response = await api.delete(`/reviews/${reviewId}`);
    return response.data;
  },

  // 获取用户的评论列表
  async getUserReviews(params: {
    page?: number;
    limit?: number;
  } = {}): Promise<PaginationResponse<Review>> {
    const response = await api.get('/reviews/user/my', { params });
    return response.data;
  },

  // 回复评论
  async replyToReview(
    reviewId: string,
    content: string
  ): Promise<ApiResponse<Review>> {
    const response = await api.post('/reviews', {
      reply_to: reviewId,
      content
    });
    return response.data;
  },

  // 获取评论详情
  async getReview(reviewId: string): Promise<ApiResponse<Review>> {
    const response = await api.get(`/reviews/${reviewId}`);
    return response.data;
  },

  // 更新评论
  async updateReview(
    reviewId: string,
    data: Partial<CreateReviewData>
  ): Promise<ApiResponse<Review>> {
    const response = await api.put(`/reviews/${reviewId}`, data);
    return response.data;
  },

  // 举报评论
  async reportReview(
    reviewId: string,
    reason: string
  ): Promise<ApiResponse> {
    const response = await api.post(`/reviews/${reviewId}/report`, {
      reason
    });
    return response.data;
  },

  // 获取评论的回复列表
  async getReviewReplies(
    reviewId: string,
    params: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<PaginationResponse<Review>> {
    const response = await api.get(`/reviews/${reviewId}/replies`, { params });
    return response.data;
  }
};
