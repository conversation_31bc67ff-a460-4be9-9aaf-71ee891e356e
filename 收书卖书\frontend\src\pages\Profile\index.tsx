import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import ProfileLayout from './ProfileLayout';
import ProfileInfo from './ProfileInfo';
import ProfileOrders from './ProfileOrders';
import ProfileSecurity from './ProfileSecurity';

const Profile: React.FC = () => {
  return (
    <ProfileLayout>
      <Routes>
        <Route index element={<Navigate to="/profile/info" replace />} />
        <Route path="info" element={<ProfileInfo />} />
        <Route path="orders" element={<ProfileOrders />} />
        <Route path="security" element={<ProfileSecurity />} />
        <Route path="*" element={<Navigate to="/profile/info" replace />} />
      </Routes>
    </ProfileLayout>
  );
};

export default Profile;
