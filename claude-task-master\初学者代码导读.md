# Task Master AI 初学者代码导读

## 📚 学习指南目录

1. [学习路径规划](#学习路径规划)
2. [系统架构可视化](#系统架构可视化)
3. [核心概念解释](#核心概念解释)
4. [逐文件详细解读](#逐文件详细解读)
5. [实践练习](#实践练习)
6. [常见问题解答](#常见问题解答)

---

## 🎯 学习路径规划

### 初学者学习顺序（按难度递增）

```
第一阶段：基础理解 (1-2周)
├── 1. 项目整体架构理解
├── 2. 配置文件解读 (config/)
├── 3. 工具函数学习 (src/utils/)
└── 4. 基础数据结构 (src/types/)

第二阶段：核心功能 (2-3周)
├── 5. 应用入口点 (src/cli.js)
├── 6. 命令基类 (src/commands/base/)
├── 7. 简单命令实现 (src/commands/task/)
└── 8. 数据管理 (src/core/data/)

第三阶段：高级特性 (3-4周)
├── 9. AI 服务集成 (src/core/ai/)
├── 10. 工作流引擎 (src/core/workflow/)
├── 11. 外部集成 (src/integrations/)
└── 12. 插件系统 (src/plugins/)

第四阶段：企业级功能 (4-5周)
├── 13. 安全和权限 (src/core/security/)
├── 14. 监控和日志 (src/utils/monitoring/)
├── 15. 性能优化
└── 16. 部署和运维
```

### 🚀 快速开始建议

**如果你是编程新手：**
- 先从 `src/utils/` 目录开始，理解基础工具函数
- 然后学习 `src/cli.js`，了解程序如何启动
- 接着看简单的命令实现，如 `src/commands/task/ListCommand.js`

**如果你有一定编程经验：**
- 直接从系统架构图开始理解整体设计
- 重点学习 `src/core/` 目录下的核心业务逻辑
- 深入研究 AI 集成和工作流引擎

**如果你想快速上手开发：**
- 先看配置文件了解如何设置环境
- 学习命令开发模式，尝试创建自己的命令
- 研究插件系统，开发自定义功能

---

## 🏗️ 系统架构可视化

### 整体架构图

```
                    Task Master AI 系统架构
    ┌─────────────────────────────────────────────────────────────┐
    │                     用户交互层                                │
    │  ┌─────────────────┐              ┌─────────────────┐        │
    │  │   命令行界面     │              │   Web API 接口   │        │
    │  │   (CLI Entry)   │              │   (REST API)    │        │
    │  │                 │              │                 │        │
    │  │  • 命令解析      │              │  • HTTP 路由     │        │
    │  │  • 参数验证      │              │  • 请求处理      │        │
    │  │  • 结果展示      │              │  • 响应格式化    │        │
    │  └─────────────────┘              └─────────────────┘        │
    └─────────────┬─────────────────────────────┬─────────────────┘
                  │                             │
    ┌─────────────▼─────────────────────────────▼─────────────────┐
    │                     命令处理层                                │
    │  ┌─────────────────────────────────────────────────────────┐ │
    │  │              CommandRegistry.js                         │ │
    │  │              (命令注册中心)                               │ │
    │  │                                                         │ │
    │  │  • 命令注册和管理    • 权限验证    • 生命周期控制          │ │
    │  │  • 别名处理         • 中间件执行   • 性能监控             │ │
    │  └─────────────────────────────────────────────────────────┘ │
    │  ┌─────────────────────────────────────────────────────────┐ │
    │  │               BaseCommand.js                            │ │
    │  │               (命令基类)                                 │ │
    │  │                                                         │ │
    │  │  • 模板方法模式     • 参数解析     • 错误处理             │ │
    │  │  • 钩子函数        • 结果格式化   • 日志记录             │ │
    │  └─────────────────────────────────────────────────────────┘ │
    └─────────────┬─────────────────────────────────────────────────┘
                  │
    ┌─────────────▼─────────────────────────────────────────────────┐
    │                     具体命令层                                │
    │                                                               │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
    │  │   任务命令   │  │   分析命令   │  │  工作流命令  │           │
    │  │             │  │             │  │             │           │
    │  │ • 创建任务   │  │ • 复杂度分析 │  │ • 智能推荐   │           │
    │  │ • 列表显示   │  │ • 依赖验证   │  │ • 自动执行   │           │
    │  │ • 状态更新   │  │ • 报告生成   │  │ • 流程管理   │           │
    │  └─────────────┘  └─────────────┘  └─────────────┘           │
    │                                                               │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
    │  │   集成命令   │  │   工具命令   │  │  插件命令    │           │
    │  │             │  │             │  │             │           │
    │  │ • Jira 同步  │  │ • 配置管理   │  │ • 动态加载   │           │
    │  │ • Slack 通知 │  │ • 诊断检查   │  │ • 扩展功能   │           │
    │  │ • GitHub 集成│  │ • 备份恢复   │  │ • 自定义命令 │           │
    │  └─────────────┘  └─────────────┘  └─────────────┘           │
    └─────────────┬─────────────────────────────────────────────────┘
                  │
    ┌─────────────▼─────────────────────────────────────────────────┐
    │                     核心业务层                                │
    │                                                               │
    │  ┌─────────────────────────────────────────────────────────┐ │
    │  │              AIProviderManager.js                       │ │
    │  │              (AI 服务管理器)                             │ │
    │  │                                                         │ │
    │  │  • 多提供商管理     • 智能路由     • 故障转移            │ │
    │  │  • 成本优化        • 性能监控     • 缓存管理            │ │
    │  └─────────────────────────────────────────────────────────┘ │
    │                                                               │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
    │  │  任务管理器  │  │  工作流引擎  │  │  分析引擎    │           │
    │  │             │  │             │  │             │           │
    │  │ • CRUD 操作  │  │ • 流程定义   │  │ • 复杂度计算 │           │
    │  │ • 依赖管理   │  │ • 上下文分析 │  │ • 依赖分析   │           │
    │  │ • 状态跟踪   │  │ • 自动化执行 │  │ • 性能评估   │           │
    │  └─────────────┘  └─────────────┘  └─────────────┘           │
    └─────────────┬─────────────────────────────────────────────────┘
                  │
    ┌─────────────▼─────────────────────────────────────────────────┐
    │                     数据访问层                                │
    │                                                               │
    │  ┌─────────────────────────────────────────────────────────┐ │
    │  │              DatabaseManager.js                         │ │
    │  │              (数据库管理器)                               │ │
    │  │                                                         │ │
    │  │  • 连接池管理       • 事务处理     • 查询优化            │ │
    │  │  • 多数据库支持     • 迁移管理     • 备份恢复            │ │
    │  └─────────────────────────────────────────────────────────┘ │
    │                                                               │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
    │  │  缓存管理器  │  │  配置管理器  │  │  文件管理器  │           │
    │  │             │  │             │  │             │           │
    │  │ • Redis 集成 │  │ • 环境配置   │  │ • 文件操作   │           │
    │  │ • 多级缓存   │  │ • 动态更新   │  │ • 路径管理   │           │
    │  │ • 过期策略   │  │ • 验证检查   │  │ • 权限控制   │           │
    │  └─────────────┘  └─────────────┘  └─────────────┘           │
    └─────────────┬─────────────────────────────────────────────────┘
                  │
    ┌─────────────▼─────────────────────────────────────────────────┐
    │                     基础设施层                                │
    │                                                               │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
    │  │   日志系统   │  │   监控系统   │  │   安全系统   │           │
    │  │             │  │             │  │             │           │
    │  │ • 结构化日志 │  │ • 性能指标   │  │ • 认证授权   │           │
    │  │ • 多级别输出 │  │ • 健康检查   │  │ • 数据加密   │           │
    │  │ • 日志轮转   │  │ • 告警通知   │  │ • 审计跟踪   │           │
    │  └─────────────┘  └─────────────┘  └─────────────┘           │
    │                                                               │
    │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
    │  │   工具函数   │  │   验证器     │  │   格式化器   │           │
    │  │             │  │             │  │             │           │
    │  │ • 通用工具   │  │ • 数据验证   │  │ • 输出格式化 │           │
    │  │ • 辅助函数   │  │ • 类型检查   │  │ • 国际化支持 │           │
    │  │ • 常量定义   │  │ • 业务规则   │  │ • 主题样式   │           │
    │  └─────────────┘  └─────────────┘  └─────────────┘           │
    └─────────────────────────────────────────────────────────────┘
```

### 数据流向图

```
用户输入命令
    │
    ▼
┌─────────────────┐
│   CLI 解析器     │ ← 解析命令行参数和选项
│   (cli.js)      │
└─────────────────┘
    │
    ▼
┌─────────────────┐
│  命令注册器      │ ← 查找对应的命令处理器
│ (CommandRegistry)│
└─────────────────┘
    │
    ▼
┌─────────────────┐
│   命令基类      │ ← 执行通用的前置处理
│  (BaseCommand)  │   • 参数验证
└─────────────────┘   • 权限检查
    │                 • 日志记录
    ▼
┌─────────────────┐
│   具体命令      │ ← 执行具体的业务逻辑
│ (如 AddTask)    │
└─────────────────┘
    │
    ▼
┌─────────────────┐
│   核心服务      │ ← 调用核心业务服务
│ (TaskManager,   │   • 数据处理
│  AIProvider)    │   • 业务计算
└─────────────────┘   • 外部调用
    │
    ▼
┌─────────────────┐
│   数据层        │ ← 数据持久化和检索
│ (Database,      │   • 数据库操作
│  Cache, Files)  │   • 缓存管理
└─────────────────┘   • 文件操作
    │
    ▼
┌─────────────────┐
│   结果返回      │ ← 格式化并返回结果
│  (格式化输出)    │   • JSON/表格/图表
└─────────────────┘   • 错误处理
    │                 • 成功提示
    ▼
  用户看到结果
```

---

## 💡 核心概念解释

### 1. 什么是命令模式 (Command Pattern)？

**简单解释：** 把每个操作（如"创建任务"、"列出任务"）包装成一个独立的对象，这样可以：
- 统一处理不同的操作
- 容易添加新的操作
- 支持撤销和重做
- 记录操作历史

**在项目中的应用：**
```javascript
// 每个命令都是一个独立的类
class AddTaskCommand extends BaseCommand {
  async execute(args, options) {
    // 具体的创建任务逻辑
  }
}

class ListTasksCommand extends BaseCommand {
  async execute(args, options) {
    // 具体的列出任务逻辑
  }
}
```

### 2. 什么是依赖注入 (Dependency Injection)？

**简单解释：** 不在类内部直接创建依赖对象，而是从外部传入，这样：
- 代码更容易测试
- 组件之间耦合度更低
- 可以灵活替换实现

**举例说明：**
```javascript
// ❌ 不好的方式：直接在内部创建依赖
class TaskManager {
  constructor() {
    this.database = new PostgreSQLDatabase(); // 硬编码依赖
  }
}

// ✅ 好的方式：通过构造函数注入依赖
class TaskManager {
  constructor(database) {
    this.database = database; // 从外部注入
  }
}

// 使用时
const database = new PostgreSQLDatabase();
const taskManager = new TaskManager(database);
```

### 3. 什么是事件驱动架构 (Event-Driven Architecture)？

**简单解释：** 当某件事情发生时，系统会发出一个"事件"，其他组件可以监听这个事件并做出响应：
- 组件之间松耦合
- 容易扩展新功能
- 支持异步处理

**在项目中的应用：**
```javascript
// 当任务创建时，发出事件
eventBus.emit('task:created', task);

// 其他组件可以监听这个事件
eventBus.on('task:created', async (task) => {
  // 自动分析任务复杂度
  await analyzeComplexity(task);
  
  // 发送通知
  await sendNotification(task);
  
  // 更新统计
  await updateStats(task);
});
```

---

## 📖 逐文件详细解读

### 🚀 第一个文件：应用入口 (src/cli.js)

**为什么从这里开始？**
- 这是程序的起点，理解了这里就知道程序如何启动
- 包含了最基础的概念，适合初学者
- 代码结构相对简单，容易理解

#### 完整代码解读

```javascript
#!/usr/bin/env node
// ↑ 这一行叫做 "shebang"，告诉系统用 node 来执行这个文件
// 这样就可以直接运行 ./cli.js 而不需要输入 node cli.js

/**
 * Task Master AI CLI 应用主入口文件
 * 
 * 🎯 这个文件的主要职责：
 * 1. 启动整个 CLI 应用
 * 2. 注册所有可用的命令
 * 3. 处理全局配置和选项
 * 4. 管理应用的生命周期
 */

// 📦 导入必要的模块
const { Command } = require('commander');        // CLI 框架，用于解析命令行
const chalk = require('chalk');                  // 用于给终端输出添加颜色
const figlet = require('figlet');               // 用于生成 ASCII 艺术字
const { CommandRegistry } = require('./commands/base/CommandRegistry');  // 命令注册器
const { ConfigManager } = require('./core/data/ConfigManager');          // 配置管理器
const { Logger } = require('./utils/Logger');                            // 日志系统
const { ErrorHandler } = require('./utils/ErrorHandler');               // 错误处理器

/**
 * 🏗️ TaskMasterCLI 类 - 整个 CLI 应用的主控制器
 * 
 * 💭 为什么要用类？
 * - 可以把相关的数据和方法组织在一起
 * - 便于管理应用的状态
 * - 支持继承和扩展
 */
class TaskMasterCLI {
  /**
   * 🔧 构造函数 - 创建 CLI 应用实例时执行
   */
  constructor() {
    // 创建 Commander.js 程序实例，用于处理命令行
    this.program = new Command();
    
    // 创建命令注册器，用于管理所有命令
    this.registry = new CommandRegistry();
    
    // 创建配置管理器，用于处理应用配置
    this.config = new ConfigManager();
    
    // 创建日志记录器，用于记录应用运行信息
    this.logger = new Logger();
    
    // 创建错误处理器，用于统一处理错误
    this.errorHandler = new ErrorHandler();
  }

  /**
   * 🚀 初始化方法 - 设置整个应用
   * 
   * 💡 为什么要分离初始化？
   * - 构造函数应该保持简单
   * - 初始化可能包含异步操作
   * - 便于错误处理和测试
   */
  async initialize() {
    try {
      // 1️⃣ 显示欢迎信息
      this.displayBanner();
      
      // 2️⃣ 加载配置文件
      await this.loadConfiguration();
      
      // 3️⃣ 注册所有可用命令
      await this.registerCommands();
      
      // 4️⃣ 设置全局选项（如 --verbose, --help）
      this.setupGlobalOptions();
      
      // 5️⃣ 配置错误处理
      this.setupErrorHandling();
      
      this.logger.info('✅ CLI 应用初始化完成');
      
    } catch (error) {
      this.logger.error('❌ CLI 应用初始化失败:', error);
      throw error;
    }
  }

  /**
   * 🎨 显示启动横幅
   * 
   * 💭 为什么要显示横幅？
   * - 让用户知道程序已经启动
   * - 显示版本信息
   * - 提升用户体验
   */
  displayBanner() {
    // 使用 figlet 生成 ASCII 艺术字
    console.log(chalk.cyan(figlet.textSync('Task Master AI', {
      font: 'Standard',           // 字体样式
      horizontalLayout: 'default', // 水平布局
      verticalLayout: 'default'    // 垂直布局
    })));
    
    // 显示版本信息，使用灰色
    console.log(chalk.gray('智能项目管理系统 v2.0.0\n'));
  }

  /**
   * ⚙️ 加载应用配置
   * 
   * 🤔 什么是配置？
   * - 数据库连接信息
   * - API 密钥
   * - 默认设置
   * - 环境变量
   */
  async loadConfiguration() {
    try {
      // 尝试加载配置文件
      await this.config.load();
      this.logger.info('📋 配置加载完成');
      
    } catch (error) {
      // 如果加载失败，使用默认配置
      this.logger.warn('⚠️ 配置加载失败，使用默认配置', error);
      await this.config.loadDefaults();
    }
  }

  /**
   * 📝 注册所有可用命令
   * 
   * 💡 什么是命令注册？
   * - 告诉系统有哪些命令可用
   * - 设置命令的处理函数
   * - 配置命令的参数和选项
   */
  async registerCommands() {
    // 📋 定义所有命令模块的路径
    const commandModules = [
      // 任务管理命令
      require('./commands/task/AddTaskCommand'),      // 添加任务
      require('./commands/task/ListCommand'),         // 列出任务
      require('./commands/task/ShowCommand'),         // 显示任务详情
      require('./commands/task/UpdateCommand'),       // 更新任务
      require('./commands/task/DeleteCommand'),       // 删除任务
      
      // 分析功能命令
      require('./commands/analysis/AnalyzeComplexityCommand'),  // 复杂度分析
      require('./commands/analysis/ValidateDependenciesCommand'), // 依赖验证
      require('./commands/analysis/GenerateReportCommand'),     // 生成报告
      
      // 工作流命令
      require('./commands/workflow/SmartWorkflowCommand'),      // 智能工作流
      require('./commands/workflow/AutoImplementCommand'),      // 自动实现
      
      // 集成命令
      require('./commands/integration/JiraSyncCommand'),        // Jira 同步
      require('./commands/integration/SlackNotifyCommand'),     // Slack 通知
      
      // 工具命令
      require('./commands/utils/ConfigCommand'),               // 配置管理
      require('./commands/utils/DiagnosticsCommand')           // 诊断工具
    ];

    // 🔄 遍历所有命令模块，逐个注册
    for (const CommandClass of commandModules) {
      try {
        // 创建命令实例
        const command = new CommandClass();
        
        // 注册到命令注册器
        this.registry.register(command);
        
        // 添加到 Commander.js 程序
        this.program.addCommand(command.getCommand());
        
      } catch (error) {
        this.logger.error(`❌ 注册命令失败: ${CommandClass.name}`, error);
      }
    }
    
    // 📊 显示注册统计
    this.logger.info(`📝 已注册 ${this.registry.getCommandCount()} 个命令`);
  }

  /**
   * 🛠️ 设置全局选项
   * 
   * 💭 什么是全局选项？
   * - 所有命令都可以使用的选项
   * - 如 --verbose（详细输出）、--help（帮助）
   */
  setupGlobalOptions() {
    this.program
      .name('task-master')                                    // 程序名称
      .description('Task Master AI - 智能项目管理系统')        // 程序描述
      .version(require('../package.json').version)           // 版本号
      .option('-v, --verbose', '详细输出模式')                // 详细输出选项
      .option('-q, --quiet', '静默模式')                      // 静默模式选项
      .option('-c, --config <path>', '指定配置文件路径')       // 配置文件路径
      .option('--log-level <level>', '设置日志级别', 'info')   // 日志级别
      .option('--no-color', '禁用彩色输出')                   // 禁用颜色
      .hook('preAction', this.preActionHook.bind(this))      // 命令执行前钩子
      .hook('postAction', this.postActionHook.bind(this));   // 命令执行后钩子
  }

  /**
   * 🎣 命令执行前钩子
   * 
   * 🤔 什么是钩子？
   * - 在特定时机自动执行的函数
   * - 用于处理通用逻辑
   * - 如日志记录、权限检查等
   */
  async preActionHook(thisCommand, actionCommand) {
    const options = thisCommand.opts();
    
    // 🔧 根据选项设置日志级别
    if (options.verbose) {
      this.logger.setLevel('debug');
    } else if (options.quiet) {
      this.logger.setLevel('error');
    } else {
      this.logger.setLevel(options.logLevel);
    }
    
    // 📝 记录命令执行
    this.logger.debug(`🚀 执行命令: ${actionCommand.name()}`, {
      args: actionCommand.args,      // 命令参数
      options: actionCommand.opts()  // 命令选项
    });
  }

  /**
   * 🎣 命令执行后钩子
   */
  async postActionHook(thisCommand, actionCommand) {
    // 📝 记录命令完成
    this.logger.debug(`✅ 命令执行完成: ${actionCommand.name()}`);
    
    // 🧹 清理资源
    await this.cleanup();
  }

  /**
   * 🚨 设置错误处理
   * 
   * 💡 为什么需要全局错误处理？
   * - 捕获未处理的错误
   * - 提供友好的错误信息
   * - 防止程序崩溃
   */
  setupErrorHandling() {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      this.errorHandler.handleUncaughtException(error);
      process.exit(1);  // 退出程序
    });

    // 处理未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
      this.errorHandler.handleUnhandledRejection(reason, promise);
      process.exit(1);
    });

    // 处理程序中断信号（Ctrl+C）
    process.on('SIGINT', async () => {
      this.logger.info('📡 接收到中断信号，正在清理...');
      await this.cleanup();
      process.exit(0);
    });
  }

  /**
   * 🧹 清理资源
   * 
   * 🤔 为什么需要清理？
   * - 关闭数据库连接
   * - 清理临时文件
   * - 保存未保存的数据
   */
  async cleanup() {
    try {
      // 保存配置更改
      await this.config.save();
      
      this.logger.info('🧹 资源清理完成');
    } catch (error) {
      this.logger.error('❌ 资源清理失败:', error);
    }
  }

  /**
   * 🏃 启动 CLI 应用
   * 
   * 这是整个应用的入口点
   */
  async run() {
    try {
      // 1️⃣ 初始化应用
      await this.initialize();
      
      // 2️⃣ 解析命令行参数并执行相应命令
      await this.program.parseAsync(process.argv);
      
    } catch (error) {
      // 🚨 如果启动失败，显示错误并退出
      this.errorHandler.handleError(error);
      process.exit(1);
    }
  }
}

// 🚀 创建并启动 CLI 应用实例
const cli = new TaskMasterCLI();
cli.run().catch(error => {
  console.error(chalk.red('💥 应用启动失败:'), error.message);
  process.exit(1);
});

// 📤 导出类，供其他模块使用
module.exports = TaskMasterCLI;
```

#### 🤔 思考题

1. **为什么要使用类而不是简单的函数？**
   - 提示：想想状态管理和代码组织

2. **如果要添加一个新的全局选项 `--debug`，应该在哪里修改？**
   - 提示：查看 `setupGlobalOptions` 方法

3. **程序是如何知道用户输入了什么命令的？**
   - 提示：关注 `this.program.parseAsync(process.argv)` 这一行

#### 🛠️ 试试看

1. **修改启动横幅：**
   ```javascript
   // 在 displayBanner() 方法中，尝试修改横幅文字
   console.log(chalk.cyan(figlet.textSync('My Task Master', {
     font: 'Standard'
   })));
   ```

2. **添加新的全局选项：**
   ```javascript
   // 在 setupGlobalOptions() 方法中添加
   .option('--debug', '启用调试模式')
   ```

3. **修改日志级别：**
   ```javascript
   // 在 preActionHook() 方法中添加
   if (options.debug) {
     this.logger.setLevel('debug');
   }
   ```

### 🏗️ 第二个文件：命令基类 (src/commands/base/BaseCommand.js)

**为什么学习这个文件？**
- 理解所有命令的共同基础
- 学习面向对象编程的继承概念
- 掌握模板方法设计模式

#### 核心概念：什么是基类？

**简单解释：** 基类就像是一个"模板"或"蓝图"，定义了所有命令都应该有的基本功能：

```
        BaseCommand (基类)
        ┌─────────────────┐
        │ • 参数解析       │
        │ • 错误处理       │
        │ • 日志记录       │
        │ • 结果格式化     │
        └─────────────────┘
               │
               │ 继承 (extends)
               ▼
    ┌─────────────────────────────────┐
    │                                 │
    ▼                                 ▼
AddTaskCommand                  ListCommand
┌─────────────────┐            ┌─────────────────┐
│ • 继承基类功能   │            │ • 继承基类功能   │
│ • 实现创建任务   │            │ • 实现列出任务   │
└─────────────────┘            └─────────────────┘
```

#### 完整代码解读

```javascript
/**
 * 🏗️ BaseCommand - 所有命令的基类
 *
 * 🎯 这个类的作用：
 * 1. 定义所有命令的通用接口
 * 2. 提供统一的错误处理
 * 3. 实现模板方法模式
 * 4. 管理命令的生命周期
 */

// 📦 导入必要的模块
const { Command } = require('commander');    // Commander.js 命令类
const chalk = require('chalk');              // 终端颜色
const ora = require('ora');                  // 加载动画
const { Logger } = require('../../utils/Logger');           // 日志系统
const { Validator } = require('../../utils/Validator');     // 数据验证
const { ErrorHandler } = require('../../utils/ErrorHandler'); // 错误处理

/**
 * 🏗️ BaseCommand 类 - 命令基类
 *
 * 💡 设计模式：模板方法模式
 * - 定义算法的骨架，让子类实现具体步骤
 * - 保证所有命令都遵循相同的执行流程
 */
class BaseCommand {
  /**
   * 🔧 构造函数
   *
   * @param {string} name - 命令名称（如 'add-task'）
   * @param {string} description - 命令描述
   */
  constructor(name, description) {
    // 📝 保存命令基本信息
    this.name = name;                    // 命令名称
    this.description = description;      // 命令描述

    // 🎛️ 创建 Commander.js 命令对象
    this.command = new Command(name);

    // 🛠️ 创建工具实例
    this.logger = new Logger();         // 日志记录器
    this.validator = new Validator();   // 数据验证器
    this.errorHandler = new ErrorHandler(); // 错误处理器

    // 📋 存储命令选项和参数的定义
    this.options = new Map();           // 选项映射 (--verbose, --format 等)
    this.arguments = [];                 // 参数数组 (task-id, file-path 等)

    // 🎣 钩子函数数组 - 在特定时机执行的函数
    this.hooks = {
      beforeValidation: [],    // 验证前执行
      afterValidation: [],     // 验证后执行
      beforeExecution: [],     // 执行前执行
      afterExecution: [],      // 执行后执行
      onError: []             // 错误时执行
    };

    // 🔗 中间件数组 - 类似于 Express.js 的中间件
    this.middleware = [];

    // ⚙️ 初始化命令
    this.setupCommand();
  }

  /**
   * ⚙️ 设置命令基本信息
   *
   * 💭 为什么要单独一个方法？
   * - 保持构造函数简洁
   * - 便于子类重写
   */
  setupCommand() {
    this.command
      .description(this.description)                    // 设置描述
      .action(this.executeCommand.bind(this));         // 设置执行函数

    // 🔗 绑定 this 的原因：
    // JavaScript 中，方法作为回调传递时会丢失 this 上下文
    // bind(this) 确保方法内部的 this 指向当前实例
  }

  /**
   * 🎛️ 添加命令选项
   *
   * 💡 什么是选项？
   * - 以 -- 开头的参数，如 --verbose, --format=json
   * - 通常是可选的，有默认值
   *
   * @param {string} flags - 选项标志 (如 '-v, --verbose')
   * @param {string} description - 选项描述
   * @param {*} defaultValue - 默认值
   * @param {Function} validator - 验证函数
   */
  addOption(flags, description, defaultValue, validator) {
    // 📝 添加到 Commander.js
    this.command.option(flags, description, defaultValue);

    // 💾 存储选项元数据，用于后续验证
    const optionName = this.extractOptionName(flags);
    this.options.set(optionName, {
      flags,           // 原始标志
      description,     // 描述
      defaultValue,    // 默认值
      validator        // 验证函数
    });

    // 🔗 返回 this 支持链式调用
    return this;
  }

  /**
   * 📝 添加命令参数
   *
   * 💡 什么是参数？
   * - 不以 -- 开头的参数，如 task-id, file-path
   * - 通常是必需的，按位置传递
   *
   * @param {string} name - 参数名称
   * @param {string} description - 参数描述
   * @param {Function} validator - 验证函数
   */
  addArgument(name, description, validator) {
    // 📝 添加到 Commander.js
    this.command.argument(name, description);

    // 💾 存储参数元数据
    this.arguments.push({
      name,
      description,
      validator
    });

    return this;
  }

  /**
   * 🎣 添加钩子函数
   *
   * 💡 什么是钩子？
   * - 在特定时机自动执行的函数
   * - 用于扩展功能，不修改核心逻辑
   *
   * @param {string} hookName - 钩子名称
   * @param {Function} handler - 处理函数
   */
  addHook(hookName, handler) {
    if (this.hooks[hookName]) {
      this.hooks[hookName].push(handler);
    }
    return this;
  }

  /**
   * 🔗 添加中间件
   *
   * 💡 什么是中间件？
   * - 在主要逻辑执行前运行的函数
   * - 可以修改请求、添加功能、进行检查
   *
   * @param {Function} middleware - 中间件函数
   */
  use(middleware) {
    this.middleware.push(middleware);
    return this;
  }

  /**
   * 🚀 执行命令的主流程 (模板方法)
   *
   * 💡 模板方法模式：
   * - 定义算法骨架，具体步骤由子类实现
   * - 保证所有命令都遵循相同的执行流程
   *
   * 📋 执行流程：
   * 1. 解析参数
   * 2. 验证参数
   * 3. 执行中间件
   * 4. 执行主要逻辑
   * 5. 显示结果
   */
  async executeCommand(...args) {
    // 🎭 创建加载动画
    const spinner = ora('正在执行命令...').start();
    const startTime = Date.now();

    try {
      // 1️⃣ 解析参数和选项
      const { parsedArgs, options } = this.parseArguments(args);

      // 2️⃣ 执行前置钩子
      await this.executeHooks('beforeValidation', { args: parsedArgs, options });

      // 3️⃣ 验证参数
      await this.validateArguments(parsedArgs, options);

      // 4️⃣ 执行后置验证钩子
      await this.executeHooks('afterValidation', { args: parsedArgs, options });

      // 5️⃣ 执行中间件
      const context = { args: parsedArgs, options, command: this };
      await this.executeMiddleware(context);

      // 6️⃣ 执行前置执行钩子
      await this.executeHooks('beforeExecution', context);

      // 7️⃣ 执行主要逻辑 (由子类实现)
      const result = await this.execute(parsedArgs, options, context);

      // 8️⃣ 执行后置执行钩子
      await this.executeHooks('afterExecution', { ...context, result });

      // 9️⃣ 显示结果
      await this.displayResult(result, options);

      // ✅ 显示成功信息
      spinner.succeed(chalk.green('命令执行成功'));

      // 📊 记录性能指标
      const duration = Date.now() - startTime;
      this.logger.debug(`⏱️ 命令执行耗时: ${duration}ms`);

      return result;

    } catch (error) {
      // ❌ 显示失败信息
      spinner.fail(chalk.red('命令执行失败'));

      // 🎣 执行错误钩子
      await this.executeHooks('onError', { error, args });

      // 🚨 处理错误
      await this.handleError(error);

      // 🔄 重新抛出错误，让上层处理
      throw error;
    }
  }

  /**
   * 📊 解析命令参数和选项
   *
   * 💡 Commander.js 的参数传递方式：
   * - args[0], args[1], ... 是位置参数
   * - args[最后一个] 是 Commander.js 的命令对象，包含选项
   */
  parseArguments(args) {
    const command = args[args.length - 1];  // 最后一个是命令对象
    const options = command.opts();         // 获取选项
    const parsedArgs = args.slice(0, -1);   // 前面的是位置参数

    return { parsedArgs, options };
  }

  /**
   * ✅ 验证参数和选项
   *
   * 💡 为什么需要验证？
   * - 确保数据格式正确
   * - 提前发现错误
   * - 提供友好的错误信息
   */
  async validateArguments(args, options) {
    // 🔍 验证位置参数
    for (let i = 0; i < this.arguments.length; i++) {
      const argDef = this.arguments[i];    // 参数定义
      const argValue = args[i];            // 实际值

      // 如果有验证函数，就执行验证
      if (argDef.validator) {
        const isValid = await argDef.validator(argValue);
        if (!isValid) {
          throw new Error(`❌ 参数 ${argDef.name} 无效: ${argValue}`);
        }
      }
    }

    // 🔍 验证选项
    for (const [optionName, optionDef] of this.options) {
      const optionValue = options[optionName];

      // 如果选项有值且有验证函数，就执行验证
      if (optionDef.validator && optionValue !== undefined) {
        const isValid = await optionDef.validator(optionValue);
        if (!isValid) {
          throw new Error(`❌ 选项 ${optionName} 无效: ${optionValue}`);
        }
      }
    }
  }

  /**
   * 🎣 执行钩子函数
   *
   * @param {string} hookName - 钩子名称
   * @param {Object} context - 上下文数据
   */
  async executeHooks(hookName, context) {
    const hooks = this.hooks[hookName] || [];

    // 🔄 依次执行所有钩子
    for (const hook of hooks) {
      await hook(context);
    }
  }

  /**
   * 🔗 执行中间件
   *
   * @param {Object} context - 上下文数据
   */
  async executeMiddleware(context) {
    // 🔄 依次执行所有中间件
    for (const middleware of this.middleware) {
      await middleware(context);
    }
  }

  /**
   * 📺 显示执行结果
   *
   * 💡 为什么要单独一个方法？
   * - 支持不同的输出格式 (JSON, 表格, 图表)
   * - 子类可以重写自定义显示方式
   *
   * @param {*} result - 执行结果
   * @param {Object} options - 命令选项
   */
  async displayResult(result, options) {
    // 🤫 静默模式不显示结果
    if (options.quiet) {
      return;
    }

    // 📄 JSON 格式输出
    if (options.format === 'json') {
      console.log(JSON.stringify(result, null, 2));
    } else {
      // 🎨 格式化显示 (由子类实现)
      this.displayFormattedResult(result, options);
    }
  }

  /**
   * 🎨 格式化显示结果
   *
   * ⚠️ 抽象方法：子类必须实现
   *
   * @param {*} result - 执行结果
   * @param {Object} options - 命令选项
   */
  displayFormattedResult(result, options) {
    // 🔧 默认实现：直接输出
    console.log(result);
  }

  /**
   * 🚨 处理错误
   *
   * @param {Error} error - 错误对象
   */
  async handleError(error) {
    this.errorHandler.handle(error, {
      command: this.name,
      context: 'command_execution'
    });
  }

  /**
   * 📤 获取 Commander.js 命令对象
   *
   * 💡 为什么需要这个方法？
   * - 让外部可以访问 Commander.js 对象
   * - 用于注册到主程序
   */
  getCommand() {
    return this.command;
  }

  /**
   * 🔍 从选项标志中提取选项名称
   *
   * 例如：'-v, --verbose' → 'verbose'
   *
   * @param {string} flags - 选项标志
   * @returns {string} 选项名称
   */
  extractOptionName(flags) {
    const match = flags.match(/--([a-zA-Z0-9-]+)/);
    return match ? match[1].replace(/-/g, '') : null;
  }

  /**
   * 🏗️ 抽象方法：主要执行逻辑
   *
   * ⚠️ 子类必须实现这个方法
   *
   * @param {Array} args - 解析后的参数
   * @param {Object} options - 解析后的选项
   * @param {Object} context - 执行上下文
   * @returns {*} 执行结果
   */
  async execute(args, options, context) {
    throw new Error('🚨 子类必须实现 execute 方法');
  }
}

// 📤 导出基类
module.exports = BaseCommand;
```

#### 🎯 关键概念解释

**1. 模板方法模式 (Template Method Pattern)**

```javascript
// 基类定义算法骨架
class BaseCommand {
  async executeCommand() {
    // 1. 解析参数 (固定步骤)
    const { args, options } = this.parseArguments();

    // 2. 验证参数 (固定步骤)
    await this.validateArguments(args, options);

    // 3. 执行主要逻辑 (由子类实现)
    const result = await this.execute(args, options);

    // 4. 显示结果 (固定步骤)
    await this.displayResult(result);
  }

  // 抽象方法：子类必须实现
  async execute(args, options) {
    throw new Error('子类必须实现');
  }
}

// 子类实现具体逻辑
class AddTaskCommand extends BaseCommand {
  async execute(args, options) {
    // 具体的创建任务逻辑
    return await this.createTask(args[0]);
  }
}
```

**2. 钩子函数 (Hooks)**

```javascript
// 添加钩子
command.addHook('beforeExecution', async (context) => {
  console.log('即将执行命令...');
});

command.addHook('afterExecution', async (context) => {
  console.log('命令执行完成！');
});

// 钩子会在相应时机自动执行
```

**3. 中间件 (Middleware)**

```javascript
// 添加认证中间件
command.use(async (context) => {
  if (!context.user) {
    throw new Error('需要登录');
  }
});

// 添加日志中间件
command.use(async (context) => {
  console.log(`用户 ${context.user.name} 执行命令 ${context.command.name}`);
});
```

#### 🤔 思考题

1. **为什么要使用模板方法模式？**
   - 提示：想想代码复用和一致性

2. **钩子函数和中间件有什么区别？**
   - 提示：看看它们的执行时机和用途

3. **如果要添加一个新的钩子 `beforeDisplay`，需要修改哪些地方？**
   - 提示：查看钩子的定义和执行位置

#### 🛠️ 试试看

1. **创建一个简单的命令：**
   ```javascript
   class HelloCommand extends BaseCommand {
     constructor() {
       super('hello', '打招呼命令');
       this.addArgument('<name>', '姓名');
       this.addOption('-l, --loud', '大声说话', false);
     }

     async execute(args, options) {
       const name = args[0];
       const message = `Hello, ${name}!`;
       return options.loud ? message.toUpperCase() : message;
     }

     displayFormattedResult(result) {
       console.log(`🎉 ${result}`);
     }
   }
   ```

2. **添加验证：**
   ```javascript
   this.addArgument('<name>', '姓名', (name) => {
     return name && name.length > 0;
   });
   ```

3. **添加钩子：**
   ```javascript
   this.addHook('beforeExecution', async (context) => {
     console.log('准备打招呼...');
   });
   ```

### 📋 第三个文件：简单命令实现 (src/commands/task/ListCommand.js)

**为什么选择这个文件？**
- 相对简单，容易理解
- 展示了如何继承基类
- 包含了实际的业务逻辑
- 演示了数据格式化和显示

#### 完整代码解读

```javascript
/**
 * 📋 ListCommand - 列出任务命令
 *
 * 🎯 这个命令的作用：
 * 1. 获取任务列表
 * 2. 支持过滤和排序
 * 3. 支持多种显示格式
 * 4. 提供分页功能
 */

// 📦 导入必要的模块
const BaseCommand = require('../base/BaseCommand');      // 基类
const { TaskManager } = require('../../core/data/TaskManager');  // 任务管理器
const { Table } = require('cli-table3');                // 表格显示
const chalk = require('chalk');                          // 颜色

/**
 * 📋 ListCommand 类 - 继承自 BaseCommand
 *
 * 💡 继承的好处：
 * - 自动获得基类的所有功能
 * - 只需要实现特定的业务逻辑
 * - 保持代码一致性
 */
class ListCommand extends BaseCommand {
  /**
   * 🔧 构造函数
   */
  constructor() {
    // 📞 调用父类构造函数
    super('list', '列出任务');

    // 🛠️ 创建任务管理器实例
    this.taskManager = new TaskManager();

    // ⚙️ 设置命令选项和参数
    this.setupOptions();

    // 🎣 设置钩子函数
    this.setupHooks();
  }

  /**
   * ⚙️ 设置命令选项
   *
   * 💡 选项设计原则：
   * - 提供合理的默认值
   * - 支持常用的过滤和排序
   * - 考虑用户体验
   */
  setupOptions() {
    // 🔍 过滤选项
    this.addOption(
      '-s, --status <status>',           // 选项标志
      '按状态过滤 (pending|in-progress|done|blocked)',  // 描述
      null,                              // 默认值 (null 表示不过滤)
      this.validateStatus.bind(this)     // 验证函数
    );

    this.addOption(
      '-a, --assignee <assignee>',       // 按负责人过滤
      '按负责人过滤',
      null
    );

    this.addOption(
      '-p, --priority <priority>',       // 按优先级过滤
      '按优先级过滤 (low|medium|high)',
      null,
      this.validatePriority.bind(this)
    );

    // 📊 排序选项
    this.addOption(
      '--sort <field>',                  // 排序字段
      '排序字段 (id|title|status|priority|created|updated)',
      'created',                         // 默认按创建时间排序
      this.validateSortField.bind(this)
    );

    this.addOption(
      '--order <order>',                 // 排序顺序
      '排序顺序 (asc|desc)',
      'desc',                           // 默认降序 (最新的在前)
      this.validateSortOrder.bind(this)
    );

    // 📄 显示选项
    this.addOption(
      '-f, --format <format>',           // 输出格式
      '输出格式 (table|json|csv|markdown)',
      'table',                          // 默认表格格式
      this.validateFormat.bind(this)
    );

    this.addOption(
      '-l, --limit <number>',            // 限制数量
      '限制显示数量',
      50,                               // 默认显示 50 个
      this.validateLimit.bind(this)
    );

    this.addOption(
      '--offset <number>',               // 偏移量 (用于分页)
      '偏移量 (用于分页)',
      0,
      this.validateOffset.bind(this)
    );

    // 🔧 功能选项
    this.addOption(
      '--with-subtasks',                 // 包含子任务
      '包含子任务',
      false
    );

    this.addOption(
      '--with-dependencies',             // 包含依赖信息
      '显示依赖关系',
      false
    );

    this.addOption(
      '--show-archived',                 // 显示已归档任务
      '显示已归档任务',
      false
    );
  }

  /**
   * 🎣 设置钩子函数
   *
   * 💡 钩子的用途：
   * - 记录用户行为
   * - 性能监控
   * - 缓存管理
   */
  setupHooks() {
    // 📊 执行前记录
    this.addHook('beforeExecution', async (context) => {
      this.logger.info('📋 开始获取任务列表', {
        filters: this.extractFilters(context.options),
        user: context.user?.name
      });
    });

    // 📈 执行后统计
    this.addHook('afterExecution', async (context) => {
      const { result } = context;
      this.logger.info('✅ 任务列表获取完成', {
        count: result.tasks?.length || 0,
        total: result.total || 0
      });
    });
  }

  /**
   * 🚀 主要执行逻辑
   *
   * 💡 执行步骤：
   * 1. 构建查询条件
   * 2. 获取任务数据
   * 3. 处理关联数据
   * 4. 格式化结果
   *
   * @param {Array} args - 命令参数 (这个命令没有位置参数)
   * @param {Object} options - 命令选项
   * @param {Object} context - 执行上下文
   * @returns {Object} 任务列表结果
   */
  async execute(args, options, context) {
    try {
      // 1️⃣ 构建查询条件
      const query = this.buildQuery(options);

      // 2️⃣ 获取任务列表
      const tasks = await this.taskManager.getTasks(query);

      // 3️⃣ 获取总数 (用于分页)
      const total = await this.taskManager.getTaskCount(query.filters);

      // 4️⃣ 处理关联数据
      const enrichedTasks = await this.enrichTasks(tasks, options);

      // 5️⃣ 构建结果对象
      const result = {
        tasks: enrichedTasks,           // 任务列表
        total: total,                   // 总数
        count: enrichedTasks.length,    // 当前页数量
        offset: query.offset,           // 偏移量
        limit: query.limit,             // 限制数量
        hasMore: (query.offset + enrichedTasks.length) < total,  // 是否还有更多
        filters: query.filters,         // 应用的过滤条件
        sort: query.sort               // 排序信息
      };

      return result;

    } catch (error) {
      // 🚨 增强错误信息
      throw new Error(`获取任务列表失败: ${error.message}`);
    }
  }

  /**
   * 🔍 构建查询条件
   *
   * 💡 查询对象结构：
   * {
   *   filters: { status: 'pending', assignee: 'john' },
   *   sort: { field: 'created', order: 'desc' },
   *   limit: 50,
   *   offset: 0
   * }
   *
   * @param {Object} options - 命令选项
   * @returns {Object} 查询对象
   */
  buildQuery(options) {
    // 🔍 构建过滤条件
    const filters = {};

    // 状态过滤
    if (options.status) {
      filters.status = options.status;
    }

    // 负责人过滤
    if (options.assignee) {
      filters.assignee = options.assignee;
    }

    // 优先级过滤
    if (options.priority) {
      filters.priority = options.priority;
    }

    // 是否显示已归档
    if (!options.showArchived) {
      filters.archived = false;
    }

    // 📊 构建排序条件
    const sort = {
      field: options.sort || 'created',
      order: options.order || 'desc'
    };

    // 📄 构建分页条件
    const limit = parseInt(options.limit) || 50;
    const offset = parseInt(options.offset) || 0;

    return {
      filters,
      sort,
      limit,
      offset
    };
  }

  /**
   * 🔗 丰富任务数据
   *
   * 💡 什么是数据丰富？
   * - 添加关联数据 (如子任务、依赖)
   * - 计算衍生字段 (如进度百分比)
   * - 格式化显示字段
   *
   * @param {Array} tasks - 原始任务列表
   * @param {Object} options - 命令选项
   * @returns {Array} 丰富后的任务列表
   */
  async enrichTasks(tasks, options) {
    const enrichedTasks = [];

    for (const task of tasks) {
      // 📋 复制基本信息
      const enrichedTask = { ...task };

      // 🔗 添加子任务信息
      if (options.withSubtasks) {
        enrichedTask.subtasks = await this.taskManager.getSubtasks(task.id);
        enrichedTask.subtaskCount = enrichedTask.subtasks.length;

        // 计算子任务完成进度
        const completedSubtasks = enrichedTask.subtasks.filter(st => st.status === 'done');
        enrichedTask.subtaskProgress = enrichedTask.subtasks.length > 0
          ? Math.round((completedSubtasks.length / enrichedTask.subtasks.length) * 100)
          : 0;
      }

      // 🔗 添加依赖信息
      if (options.withDependencies) {
        enrichedTask.dependencies = await this.taskManager.getDependencies(task.id);
        enrichedTask.dependents = await this.taskManager.getDependents(task.id);
        enrichedTask.isBlocked = enrichedTask.dependencies.some(dep => dep.status !== 'done');
      }

      // 📅 格式化日期
      enrichedTask.createdAtFormatted = this.formatDate(task.createdAt);
      enrichedTask.updatedAtFormatted = this.formatDate(task.updatedAt);
      if (task.dueDate) {
        enrichedTask.dueDateFormatted = this.formatDate(task.dueDate);
        enrichedTask.isOverdue = new Date(task.dueDate) < new Date();
      }

      // ⏱️ 计算耗时
      if (task.startedAt) {
        const endTime = task.completedAt ? new Date(task.completedAt) : new Date();
        const startTime = new Date(task.startedAt);
        enrichedTask.duration = Math.round((endTime - startTime) / (1000 * 60 * 60)); // 小时
      }

      enrichedTasks.push(enrichedTask);
    }

    return enrichedTasks;
  }

  /**
   * 🎨 格式化显示结果
   *
   * 💡 支持多种格式：
   * - table: 表格格式 (默认)
   * - json: JSON 格式
   * - csv: CSV 格式
   * - markdown: Markdown 格式
   *
   * @param {Object} result - 执行结果
   * @param {Object} options - 命令选项
   */
  displayFormattedResult(result, options) {
    const { tasks, total, count, hasMore } = result;

    // 📊 显示统计信息
    this.displaySummary(result);

    // 📋 根据格式显示任务列表
    switch (options.format) {
      case 'table':
        this.displayTable(tasks, options);
        break;
      case 'csv':
        this.displayCSV(tasks);
        break;
      case 'markdown':
        this.displayMarkdown(tasks);
        break;
      default:
        // JSON 格式在基类中已经处理
        break;
    }

    // 📄 显示分页信息
    if (hasMore) {
      console.log(chalk.gray(`\n💡 显示 ${count} 个任务，共 ${total} 个。使用 --offset ${result.offset + result.limit} 查看更多。`));
    }
  }

  /**
   * 📊 显示摘要信息
   *
   * @param {Object} result - 执行结果
   */
  displaySummary(result) {
    const { tasks, total, filters } = result;

    // 📈 统计各状态任务数量
    const statusCounts = {};
    tasks.forEach(task => {
      statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
    });

    // 🎨 显示统计信息
    console.log(chalk.bold('\n📊 任务统计:'));
    console.log(`总计: ${total} 个任务`);

    if (Object.keys(statusCounts).length > 0) {
      Object.entries(statusCounts).forEach(([status, count]) => {
        const color = this.getStatusColor(status);
        console.log(`  ${color(this.getStatusIcon(status))} ${this.getStatusName(status)}: ${count} 个`);
      });
    }

    // 🔍 显示应用的过滤条件
    if (Object.keys(filters).length > 0) {
      console.log(chalk.gray('\n🔍 过滤条件:'));
      Object.entries(filters).forEach(([key, value]) => {
        console.log(chalk.gray(`  ${key}: ${value}`));
      });
    }
  }

  /**
   * 📋 显示表格格式
   *
   * @param {Array} tasks - 任务列表
   * @param {Object} options - 显示选项
   */
  displayTable(tasks, options) {
    if (tasks.length === 0) {
      console.log(chalk.yellow('\n📭 没有找到匹配的任务'));
      return;
    }

    // 🏗️ 创建表格
    const table = new Table({
      head: this.getTableHeaders(options),
      colWidths: this.getTableWidths(options),
      style: {
        head: ['cyan'],
        border: ['gray']
      }
    });

    // 📝 添加任务行
    tasks.forEach(task => {
      const row = this.buildTableRow(task, options);
      table.push(row);
    });

    console.log('\n' + table.toString());
  }

  /**
   * 📋 获取表格标题
   *
   * @param {Object} options - 显示选项
   * @returns {Array} 标题数组
   */
  getTableHeaders(options) {
    const headers = ['ID', '标题', '状态', '优先级', '负责人', '创建时间'];

    if (options.withSubtasks) {
      headers.push('子任务');
    }

    if (options.withDependencies) {
      headers.push('依赖');
    }

    return headers;
  }

  /**
   * 📏 获取表格列宽
   *
   * @param {Object} options - 显示选项
   * @returns {Array} 列宽数组
   */
  getTableWidths(options) {
    const widths = [8, 40, 12, 10, 15, 12];

    if (options.withSubtasks) {
      widths.push(10);
    }

    if (options.withDependencies) {
      widths.push(10);
    }

    return widths;
  }

  /**
   * 📝 构建表格行
   *
   * @param {Object} task - 任务对象
   * @param {Object} options - 显示选项
   * @returns {Array} 行数据数组
   */
  buildTableRow(task, options) {
    const row = [
      task.id,
      this.truncateText(task.title, 35),
      this.formatStatus(task.status),
      this.formatPriority(task.priority),
      task.assignee || '-',
      task.createdAtFormatted
    ];

    if (options.withSubtasks) {
      row.push(task.subtaskCount ? `${task.subtaskCount} (${task.subtaskProgress}%)` : '-');
    }

    if (options.withDependencies) {
      const depCount = task.dependencies?.length || 0;
      const blockedIcon = task.isBlocked ? '🚫' : '';
      row.push(depCount > 0 ? `${depCount} ${blockedIcon}` : '-');
    }

    return row;
  }

  // 🎨 格式化和工具方法

  /**
   * 🎨 格式化状态显示
   */
  formatStatus(status) {
    const color = this.getStatusColor(status);
    const icon = this.getStatusIcon(status);
    return color(`${icon} ${this.getStatusName(status)}`);
  }

  /**
   * 🎨 格式化优先级显示
   */
  formatPriority(priority) {
    const colors = {
      high: chalk.red,
      medium: chalk.yellow,
      low: chalk.green
    };
    const icons = {
      high: '🔴',
      medium: '🟡',
      low: '🟢'
    };

    const color = colors[priority] || chalk.gray;
    const icon = icons[priority] || '⚪';

    return color(`${icon} ${priority || 'none'}`);
  }

  /**
   * 🎨 获取状态颜色
   */
  getStatusColor(status) {
    const colors = {
      pending: chalk.gray,
      'in-progress': chalk.blue,
      review: chalk.yellow,
      done: chalk.green,
      blocked: chalk.red,
      cancelled: chalk.gray
    };
    return colors[status] || chalk.gray;
  }

  /**
   * 🎨 获取状态图标
   */
  getStatusIcon(status) {
    const icons = {
      pending: '⏳',
      'in-progress': '🔄',
      review: '👀',
      done: '✅',
      blocked: '🚫',
      cancelled: '❌'
    };
    return icons[status] || '❓';
  }

  /**
   * 🎨 获取状态中文名称
   */
  getStatusName(status) {
    const names = {
      pending: '待处理',
      'in-progress': '进行中',
      review: '审查中',
      done: '已完成',
      blocked: '已阻塞',
      cancelled: '已取消'
    };
    return names[status] || status;
  }

  /**
   * ✂️ 截断文本
   */
  truncateText(text, maxLength) {
    if (!text) return '-';
    return text.length > maxLength ? text.substring(0, maxLength - 3) + '...' : text;
  }

  /**
   * 📅 格式化日期
   */
  formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // ✅ 验证函数

  /**
   * ✅ 验证状态值
   */
  validateStatus(status) {
    const validStatuses = ['pending', 'in-progress', 'review', 'done', 'blocked', 'cancelled'];
    return validStatuses.includes(status);
  }

  /**
   * ✅ 验证优先级值
   */
  validatePriority(priority) {
    const validPriorities = ['low', 'medium', 'high'];
    return validPriorities.includes(priority);
  }

  /**
   * ✅ 验证排序字段
   */
  validateSortField(field) {
    const validFields = ['id', 'title', 'status', 'priority', 'created', 'updated', 'assignee'];
    return validFields.includes(field);
  }

  /**
   * ✅ 验证排序顺序
   */
  validateSortOrder(order) {
    return ['asc', 'desc'].includes(order);
  }

  /**
   * ✅ 验证输出格式
   */
  validateFormat(format) {
    const validFormats = ['table', 'json', 'csv', 'markdown'];
    return validFormats.includes(format);
  }

  /**
   * ✅ 验证限制数量
   */
  validateLimit(limit) {
    const num = parseInt(limit);
    return !isNaN(num) && num > 0 && num <= 1000;
  }

  /**
   * ✅ 验证偏移量
   */
  validateOffset(offset) {
    const num = parseInt(offset);
    return !isNaN(num) && num >= 0;
  }

  /**
   * 🔍 提取过滤条件 (用于日志)
   */
  extractFilters(options) {
    const filters = {};
    if (options.status) filters.status = options.status;
    if (options.assignee) filters.assignee = options.assignee;
    if (options.priority) filters.priority = options.priority;
    return filters;
  }
}

// 📤 导出命令类
module.exports = ListCommand;
```

#### 🎯 关键学习点

**1. 继承的使用**
```javascript
// 继承基类
class ListCommand extends BaseCommand {
  constructor() {
    // 调用父类构造函数
    super('list', '列出任务');

    // 添加自己的初始化逻辑
    this.taskManager = new TaskManager();
  }
}
```

**2. 选项设计模式**
```javascript
// 好的选项设计
this.addOption(
  '-s, --status <status>',              // 短选项和长选项
  '按状态过滤 (pending|in-progress|done)', // 清晰的描述
  null,                                 // 合理的默认值
  this.validateStatus.bind(this)        // 验证函数
);
```

**3. 数据处理流程**
```javascript
// 标准的数据处理流程
async execute(args, options, context) {
  // 1. 构建查询条件
  const query = this.buildQuery(options);

  // 2. 获取原始数据
  const tasks = await this.taskManager.getTasks(query);

  // 3. 丰富数据
  const enrichedTasks = await this.enrichTasks(tasks, options);

  // 4. 构建结果
  return { tasks: enrichedTasks, total, count };
}
```

#### 🤔 思考题

1. **为什么要分离 `buildQuery` 和 `enrichTasks` 方法？**
   - 提示：想想单一职责原则和代码复用

2. **如果要添加一个新的过滤条件 `--tag`，需要修改哪些地方？**
   - 提示：查看现有过滤条件的实现

3. **表格显示和 JSON 显示有什么区别？各适用于什么场景？**
   - 提示：想想人类阅读 vs 程序处理

#### 🛠️ 试试看

1. **添加新的过滤选项：**
   ```javascript
   this.addOption(
     '--tag <tag>',
     '按标签过滤',
     null
   );

   // 在 buildQuery 中添加
   if (options.tag) {
     filters.tags = { contains: options.tag };
   }
   ```

2. **修改表格显示：**
   ```javascript
   // 添加新列
   getTableHeaders(options) {
     const headers = ['ID', '标题', '状态', '优先级', '标签', '负责人'];
     return headers;
   }
   ```

3. **添加新的输出格式：**
   ```javascript
   // 在 displayFormattedResult 中添加
   case 'simple':
     this.displaySimple(tasks);
     break;

   displaySimple(tasks) {
     tasks.forEach(task => {
       console.log(`${task.id}: ${task.title} [${task.status}]`);
     });
   }
   ```

---

## 🎯 实践练习

### 练习 1：创建你的第一个命令 (初级)

**目标：** 创建一个简单的 `hello` 命令，学习基本的命令结构。

**步骤：**

1. **创建文件：** `src/commands/practice/HelloCommand.js`

```javascript
const BaseCommand = require('../base/BaseCommand');
const chalk = require('chalk');

class HelloCommand extends BaseCommand {
  constructor() {
    // TODO: 调用父类构造函数，设置命令名称和描述

    // TODO: 添加一个必需的参数 <name>

    // TODO: 添加一个可选的选项 --loud (大声说话)
  }

  async execute(args, options, context) {
    // TODO: 获取用户输入的名字

    // TODO: 根据 --loud 选项决定是否大写

    // TODO: 返回问候消息
  }

  displayFormattedResult(result, options) {
    // TODO: 用彩色输出显示结果
  }
}

module.exports = HelloCommand;
```

**参考答案：**
```javascript
const BaseCommand = require('../base/BaseCommand');
const chalk = require('chalk');

class HelloCommand extends BaseCommand {
  constructor() {
    super('hello', '向用户打招呼');

    this.addArgument('<name>', '要打招呼的人的名字');
    this.addOption('-l, --loud', '大声说话', false);
  }

  async execute(args, options, context) {
    const name = args[0];
    let message = `Hello, ${name}!`;

    if (options.loud) {
      message = message.toUpperCase();
    }

    return {
      message,
      name,
      loud: options.loud,
      timestamp: new Date().toISOString()
    };
  }

  displayFormattedResult(result, options) {
    const icon = result.loud ? '📢' : '👋';
    const color = result.loud ? chalk.red : chalk.green;
    console.log(color(`${icon} ${result.message}`));
  }
}

module.exports = HelloCommand;
```

**测试命令：**
```bash
# 普通问候
node src/cli.js hello Alice

# 大声问候
node src/cli.js hello Bob --loud

# JSON 格式输出
node src/cli.js hello Charlie --format=json
```

### 练习 2：添加数据验证 (中级)

**目标：** 为 `hello` 命令添加输入验证，学习数据验证的重要性。

**任务：**
1. 名字不能为空
2. 名字长度不能超过 20 个字符
3. 名字只能包含字母和空格

```javascript
// 在构造函数中添加验证
this.addArgument('<name>', '要打招呼的人的名字', this.validateName.bind(this));

// 添加验证方法
validateName(name) {
  // 检查是否为空
  if (!name || name.trim().length === 0) {
    throw new Error('名字不能为空');
  }

  // 检查长度
  if (name.length > 20) {
    throw new Error('名字长度不能超过 20 个字符');
  }

  // 检查字符
  const nameRegex = /^[a-zA-Z\s\u4e00-\u9fa5]+$/;
  if (!nameRegex.test(name)) {
    throw new Error('名字只能包含字母、空格和中文字符');
  }

  return true;
}
```

### 练习 3：添加钩子函数 (中级)

**目标：** 学习如何使用钩子函数扩展功能。

**任务：** 添加使用统计功能

```javascript
constructor() {
  super('hello', '向用户打招呼');

  // 设置钩子
  this.setupHooks();
}

setupHooks() {
  // 执行前记录
  this.addHook('beforeExecution', async (context) => {
    console.log(chalk.gray('🚀 准备打招呼...'));
    this.startTime = Date.now();
  });

  // 执行后统计
  this.addHook('afterExecution', async (context) => {
    const duration = Date.now() - this.startTime;
    console.log(chalk.gray(`⏱️ 耗时: ${duration}ms`));

    // 记录使用统计
    await this.recordUsage(context.args[0], context.options.loud);
  });
}

async recordUsage(name, loud) {
  // 简单的使用统计
  const fs = require('fs').promises;
  const statsFile = '.taskmaster/hello-stats.json';

  try {
    let stats = {};
    try {
      const data = await fs.readFile(statsFile, 'utf8');
      stats = JSON.parse(data);
    } catch (error) {
      // 文件不存在，使用空对象
    }

    stats.totalUses = (stats.totalUses || 0) + 1;
    stats.loudUses = (stats.loudUses || 0) + (loud ? 1 : 0);
    stats.lastUsed = new Date().toISOString();

    await fs.mkdir('.taskmaster', { recursive: true });
    await fs.writeFile(statsFile, JSON.stringify(stats, null, 2));

  } catch (error) {
    this.logger.warn('记录使用统计失败:', error.message);
  }
}
```

### 练习 4：创建复杂命令 (高级)

**目标：** 创建一个 `weather` 命令，学习外部 API 调用和错误处理。

**功能要求：**
1. 获取指定城市的天气信息
2. 支持多种显示格式
3. 缓存结果避免频繁请求
4. 处理网络错误和 API 限制

```javascript
const BaseCommand = require('../base/BaseCommand');
const axios = require('axios');
const chalk = require('chalk');

class WeatherCommand extends BaseCommand {
  constructor() {
    super('weather', '获取天气信息');

    this.addArgument('<city>', '城市名称');
    this.addOption('-f, --format <format>', '输出格式 (simple|detailed|json)', 'simple');
    this.addOption('--no-cache', '不使用缓存', false);

    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10分钟缓存
  }

  async execute(args, options, context) {
    const city = args[0];
    const cacheKey = `weather_${city}`;

    // 检查缓存
    if (!options.noCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        console.log(chalk.gray('📦 使用缓存数据'));
        return cached.data;
      }
    }

    try {
      // 调用天气 API (这里使用模拟数据)
      const weatherData = await this.fetchWeather(city);

      // 缓存结果
      if (!options.noCache) {
        this.cache.set(cacheKey, {
          data: weatherData,
          timestamp: Date.now()
        });
      }

      return weatherData;

    } catch (error) {
      if (error.code === 'ENOTFOUND') {
        throw new Error('网络连接失败，请检查网络设置');
      } else if (error.response?.status === 429) {
        throw new Error('API 请求过于频繁，请稍后再试');
      } else if (error.response?.status === 404) {
        throw new Error(`找不到城市 "${city}" 的天气信息`);
      } else {
        throw new Error(`获取天气信息失败: ${error.message}`);
      }
    }
  }

  async fetchWeather(city) {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 模拟数据
    return {
      city,
      temperature: Math.round(Math.random() * 30 + 5),
      humidity: Math.round(Math.random() * 50 + 30),
      condition: ['晴天', '多云', '小雨', '阴天'][Math.floor(Math.random() * 4)],
      windSpeed: Math.round(Math.random() * 20 + 5),
      timestamp: new Date().toISOString()
    };
  }

  displayFormattedResult(result, options) {
    switch (options.format) {
      case 'simple':
        console.log(`🌤️  ${result.city}: ${result.temperature}°C, ${result.condition}`);
        break;

      case 'detailed':
        console.log(chalk.bold(`\n🌤️  ${result.city} 天气信息:`));
        console.log(`🌡️  温度: ${result.temperature}°C`);
        console.log(`💧 湿度: ${result.humidity}%`);
        console.log(`☁️  天气: ${result.condition}`);
        console.log(`💨 风速: ${result.windSpeed} km/h`);
        console.log(chalk.gray(`📅 更新时间: ${new Date(result.timestamp).toLocaleString()}`));
        break;

      default:
        // JSON 格式由基类处理
        break;
    }
  }
}

module.exports = WeatherCommand;
```

---

## ❓ 常见问题解答

### Q1: 为什么要使用类而不是简单的函数？

**A:** 类提供了更好的代码组织方式：

```javascript
// ❌ 函数方式 - 难以管理状态和扩展
function listTasks(options) {
  // 所有逻辑都在一个函数里
  // 难以复用和扩展
}

// ✅ 类方式 - 清晰的结构和状态管理
class ListCommand extends BaseCommand {
  constructor() {
    // 初始化状态
  }

  async execute() {
    // 主要逻辑
  }

  displayResult() {
    // 显示逻辑
  }
}
```

**优势：**
- 📦 **封装性**: 相关的数据和方法组织在一起
- 🔄 **继承性**: 可以继承基类的通用功能
- 🔧 **扩展性**: 容易添加新功能和修改现有功能
- 🧪 **可测试性**: 每个方法可以单独测试

### Q2: 什么时候使用 async/await？

**A:** 当需要等待异步操作完成时：

```javascript
// ❌ 错误用法 - 没有等待异步操作
async execute() {
  const tasks = this.taskManager.getTasks(); // 返回 Promise
  console.log(tasks); // 输出 Promise 对象，不是实际数据
}

// ✅ 正确用法 - 等待异步操作完成
async execute() {
  const tasks = await this.taskManager.getTasks(); // 等待 Promise 完成
  console.log(tasks); // 输出实际的任务数据
}
```

**常见异步操作：**
- 🗄️ 数据库查询
- 🌐 网络请求
- 📁 文件读写
- ⏱️ 定时器

### Q3: 如何处理错误？

**A:** 使用 try-catch 和有意义的错误信息：

```javascript
// ❌ 不好的错误处理
async execute() {
  const tasks = await this.taskManager.getTasks();
  return tasks;
}

// ✅ 好的错误处理
async execute() {
  try {
    const tasks = await this.taskManager.getTasks();
    return tasks;
  } catch (error) {
    // 提供有意义的错误信息
    if (error.code === 'ECONNREFUSED') {
      throw new Error('数据库连接失败，请检查数据库服务是否启动');
    } else if (error.code === 'ENOTFOUND') {
      throw new Error('网络连接失败，请检查网络设置');
    } else {
      throw new Error(`获取任务列表失败: ${error.message}`);
    }
  }
}
```

### Q4: 如何调试代码？

**A:** 使用多种调试方法：

```javascript
// 1. 使用 console.log 调试
async execute(args, options) {
  console.log('🐛 调试信息:', { args, options });

  const query = this.buildQuery(options);
  console.log('🐛 查询条件:', query);

  const tasks = await this.taskManager.getTasks(query);
  console.log('🐛 获取到的任务:', tasks.length);

  return tasks;
}

// 2. 使用 debugger 断点
async execute(args, options) {
  debugger; // 浏览器或 Node.js 调试器会在这里暂停

  const query = this.buildQuery(options);
  const tasks = await this.taskManager.getTasks(query);

  return tasks;
}

// 3. 使用日志系统
async execute(args, options) {
  this.logger.debug('开始执行命令', { args, options });

  try {
    const result = await this.doSomething();
    this.logger.info('命令执行成功', { resultCount: result.length });
    return result;
  } catch (error) {
    this.logger.error('命令执行失败', error);
    throw error;
  }
}
```

**调试技巧：**
- 🔍 **逐步调试**: 一步一步检查每个变量的值
- 📝 **日志记录**: 记录关键步骤和变量状态
- 🧪 **单元测试**: 为每个方法编写测试
- 🔧 **使用调试工具**: VS Code 调试器、Chrome DevTools

### Q5: 如何优化性能？

**A:** 从多个方面考虑性能优化：

```javascript
// 1. 避免不必要的数据库查询
class ListCommand extends BaseCommand {
  async execute(args, options) {
    // ❌ 每次都查询总数
    const tasks = await this.taskManager.getTasks(query);
    const total = await this.taskManager.getTaskCount(query.filters);

    // ✅ 一次查询获取所有需要的数据
    const result = await this.taskManager.getTasksWithCount(query);
    return result;
  }
}

// 2. 使用缓存
class WeatherCommand extends BaseCommand {
  constructor() {
    super('weather', '获取天气信息');
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10分钟
  }

  async execute(args, options) {
    const cacheKey = `weather_${args[0]}`;

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    // 获取新数据并缓存
    const data = await this.fetchWeather(args[0]);
    this.cache.set(cacheKey, { data, timestamp: Date.now() });

    return data;
  }
}

// 3. 批量处理
async enrichTasks(tasks, options) {
  // ❌ 逐个查询子任务
  for (const task of tasks) {
    task.subtasks = await this.taskManager.getSubtasks(task.id);
  }

  // ✅ 批量查询子任务
  const taskIds = tasks.map(t => t.id);
  const allSubtasks = await this.taskManager.getSubtasksBatch(taskIds);

  tasks.forEach(task => {
    task.subtasks = allSubtasks.filter(st => st.parentId === task.id);
  });
}
```

### Q6: 如何编写可测试的代码？

**A:** 遵循一些基本原则：

```javascript
// ❌ 难以测试的代码
class ListCommand extends BaseCommand {
  async execute() {
    // 直接创建依赖，难以模拟
    const taskManager = new TaskManager();
    const tasks = await taskManager.getTasks();

    // 直接输出，难以验证
    console.log(tasks);

    return tasks;
  }
}

// ✅ 易于测试的代码
class ListCommand extends BaseCommand {
  constructor(taskManager) {
    super('list', '列出任务');
    // 依赖注入，便于测试时替换
    this.taskManager = taskManager || new TaskManager();
  }

  async execute(args, options) {
    // 纯函数，输入输出明确
    const query = this.buildQuery(options);
    const tasks = await this.taskManager.getTasks(query);

    // 返回数据而不是直接输出
    return { tasks, query };
  }

  // 可以单独测试的纯函数
  buildQuery(options) {
    const filters = {};
    if (options.status) filters.status = options.status;
    return { filters };
  }
}

// 测试示例
describe('ListCommand', () => {
  it('should build correct query', () => {
    const command = new ListCommand();
    const query = command.buildQuery({ status: 'pending' });

    expect(query.filters.status).toBe('pending');
  });

  it('should get tasks from task manager', async () => {
    // 模拟 TaskManager
    const mockTaskManager = {
      getTasks: jest.fn().mockResolvedValue([{ id: 1, title: 'Test' }])
    };

    const command = new ListCommand(mockTaskManager);
    const result = await command.execute([], { status: 'pending' });

    expect(mockTaskManager.getTasks).toHaveBeenCalled();
    expect(result.tasks).toHaveLength(1);
  });
});
```

**测试原则：**
- 🔧 **依赖注入**: 便于替换依赖进行测试
- 🧮 **纯函数**: 相同输入总是产生相同输出
- 📤 **返回数据**: 而不是直接输出，便于验证结果
- 🎯 **单一职责**: 每个方法只做一件事，便于测试

---

## 📚 扩展阅读

### 推荐学习资源

**JavaScript 基础：**
- 📖 [MDN JavaScript 指南](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide)
- 📚 《JavaScript 高级程序设计》
- 🎥 [JavaScript 异步编程](https://javascript.info/async)

**Node.js 开发：**
- 📖 [Node.js 官方文档](https://nodejs.org/zh-cn/docs/)
- 📚 《深入浅出 Node.js》
- 🛠️ [Commander.js 文档](https://github.com/tj/commander.js)

**设计模式：**
- 📚 《JavaScript 设计模式》
- 🎯 [设计模式在线教程](https://refactoringguru.cn/design-patterns)

**测试：**
- 🧪 [Jest 测试框架](https://jestjs.io/zh-Hans/)
- 📖 [测试驱动开发](https://testdriven.io/)

### 下一步学习建议

1. **完成所有练习** - 动手实践是最好的学习方式
2. **阅读更多源码** - 深入理解 AI 服务集成和工作流引擎
3. **编写测试** - 为你创建的命令编写单元测试
4. **创建插件** - 尝试开发自己的插件扩展功能
5. **参与贡献** - 向项目提交 bug 报告或功能改进

记住：**编程是一门实践的艺术，多写代码，多思考，多总结！** 🚀

---

## 🔧 高级主题深入解读

### 第四个文件：AI 服务管理器 (src/core/ai/AIProviderManager.js)

**为什么学习这个文件？**
- 理解如何集成外部 API 服务
- 学习智能路由和负载均衡
- 掌握错误处理和故障转移
- 了解缓存和性能优化策略

#### 核心概念：什么是 AI 服务管理器？

**简单解释：** AI 服务管理器就像一个"智能调度员"，它管理多个 AI 服务提供商（如 Claude、OpenAI、Perplexity），根据不同的需求选择最合适的服务。

```
        用户请求
           │
           ▼
    ┌─────────────────┐
    │ AI 服务管理器    │ ← 智能调度员
    │                 │
    │ • 选择最佳提供商 │
    │ • 处理故障转移   │
    │ • 管理成本优化   │
    │ • 缓存响应结果   │
    └─────────────────┘
           │
           ▼
    ┌─────────────────────────────────┐
    │                                 │
    ▼                ▼                ▼
┌─────────┐    ┌─────────┐    ┌─────────┐
│ Claude  │    │ OpenAI  │    │Perplexity│
│ 提供商   │    │ 提供商   │    │ 提供商   │
└─────────┘    └─────────┘    └─────────┘
```

#### 关键代码片段解读

```javascript
/**
 * 🧠 AIProviderManager - AI 服务的"大脑"
 *
 * 🎯 主要职责：
 * 1. 管理多个 AI 提供商
 * 2. 智能选择最佳服务
 * 3. 处理故障和重试
 * 4. 优化成本和性能
 */
class AIProviderManager extends EventEmitter {
  constructor(config = {}) {
    super();

    // 📋 存储所有注册的 AI 提供商
    this.providers = new Map();

    // 🎯 路由策略映射
    this.routingStrategies = {
      cost: this.costBasedRouting.bind(this),        // 基于成本选择
      performance: this.performanceBasedRouting.bind(this), // 基于性能选择
      capability: this.capabilityBasedRouting.bind(this),   // 基于能力选择
      roundRobin: this.roundRobinRouting.bind(this)         // 轮询选择
    };

    // 📊 使用统计
    this.usageStats = {
      totalRequests: 0,      // 总请求数
      successfulRequests: 0, // 成功请求数
      failedRequests: 0,     // 失败请求数
      totalCost: 0,         // 总成本
      providerUsage: new Map() // 各提供商使用情况
    };
  }

  /**
   * 🚀 智能请求处理 - 这是最核心的方法
   *
   * 💡 处理流程：
   * 1. 检查缓存
   * 2. 选择最佳提供商
   * 3. 发送请求
   * 4. 处理故障转移
   * 5. 缓存结果
   */
  async request(prompt, options = {}) {
    const startTime = Date.now();
    const requestId = this.generateRequestId(); // 生成唯一请求 ID

    try {
      // 1️⃣ 检查缓存 - 避免重复请求
      const cacheKey = this.generateCacheKey(prompt, options);
      const cachedResponse = await this.cache.get(cacheKey);

      if (cachedResponse && !options.skipCache) {
        this.logger.debug('💾 使用缓存响应', { requestId });
        return cachedResponse;
      }

      // 2️⃣ 选择最佳提供商
      const provider = await this.selectProvider(prompt, options);

      if (!provider) {
        throw new Error('❌ 没有可用的 AI 提供商');
      }

      // 3️⃣ 检查速率限制
      await this.checkRateLimit(provider.name);

      // 4️⃣ 发送请求
      const response = await this.executeRequest(provider, prompt, options, requestId);

      // 5️⃣ 缓存响应
      if (options.cache !== false) {
        await this.cache.set(cacheKey, response, {
          ttl: options.cacheTTL || 3600 // 默认缓存 1 小时
        });
      }

      // 6️⃣ 更新统计信息
      this.updateStats(provider.name, true, Date.now() - startTime, response.cost);

      return response;

    } catch (error) {
      // 🔄 尝试故障转移
      if (options.enableFailover !== false) {
        const fallbackResponse = await this.handleFailover(prompt, options, error, requestId);
        if (fallbackResponse) {
          return fallbackResponse;
        }
      }

      // 📊 更新失败统计
      this.updateStats(null, false, Date.now() - startTime, 0);

      throw error;
    }
  }

  /**
   * 🎯 基于能力的路由策略
   *
   * 💡 工作原理：
   * 1. 分析请求内容，推断需要的能力
   * 2. 过滤具有该能力的提供商
   * 3. 按优先级和健康分数排序
   * 4. 选择最佳提供商
   */
  async capabilityBasedRouting(providers, prompt, options) {
    // 🔍 推断需要的能力
    const requiredCapability = options.capability || this.inferCapability(prompt);

    this.logger.debug('🎯 推断所需能力', {
      capability: requiredCapability,
      prompt: prompt.substring(0, 100) + '...'
    });

    // 🔍 过滤具有所需能力的提供商
    const capableProviders = providers.filter(provider =>
      provider.metadata.capabilities.includes(requiredCapability)
    );

    if (capableProviders.length === 0) {
      this.logger.warn('⚠️ 没有提供商支持所需能力，使用默认提供商', {
        capability: requiredCapability
      });
      return providers[0]; // 回退到第一个可用提供商
    }

    // 📊 按优先级和健康分数排序
    capableProviders.sort((a, b) => {
      // 综合评分 = 优先级 × 健康分数
      const scoreA = a.metadata.priority * a.metadata.healthScore;
      const scoreB = b.metadata.priority * b.metadata.healthScore;
      return scoreB - scoreA; // 降序排列
    });

    const selectedProvider = capableProviders[0];
    this.logger.debug('✅ 选择提供商', {
      provider: selectedProvider.name,
      capability: requiredCapability,
      healthScore: selectedProvider.metadata.healthScore
    });

    return selectedProvider;
  }

  /**
   * 🧠 推断请求能力需求
   *
   * 💡 通过关键词分析判断需要什么类型的 AI 能力
   */
  inferCapability(prompt) {
    const lowerPrompt = prompt.toLowerCase();

    // 🔍 研究能力关键词
    if (lowerPrompt.includes('research') ||
        lowerPrompt.includes('search') ||
        lowerPrompt.includes('find') ||
        lowerPrompt.includes('查找') ||
        lowerPrompt.includes('搜索')) {
      return 'research';
    }

    // 📊 分析能力关键词
    if (lowerPrompt.includes('analyze') ||
        lowerPrompt.includes('complexity') ||
        lowerPrompt.includes('evaluate') ||
        lowerPrompt.includes('分析') ||
        lowerPrompt.includes('评估')) {
      return 'analysis';
    }

    // ✍️ 生成能力关键词
    if (lowerPrompt.includes('generate') ||
        lowerPrompt.includes('create') ||
        lowerPrompt.includes('write') ||
        lowerPrompt.includes('生成') ||
        lowerPrompt.includes('创建')) {
      return 'generation';
    }

    // 🤔 推理能力关键词
    if (lowerPrompt.includes('reason') ||
        lowerPrompt.includes('logic') ||
        lowerPrompt.includes('solve') ||
        lowerPrompt.includes('推理') ||
        lowerPrompt.includes('解决')) {
      return 'reasoning';
    }

    // 🔧 默认返回分析能力
    return 'analysis';
  }

  /**
   * 🔄 处理故障转移
   *
   * 💡 当主要提供商失败时，自动尝试备用提供商
   */
  async handleFailover(prompt, options, originalError, requestId) {
    this.logger.warn('🔄 开始故障转移', {
      requestId,
      originalError: originalError.message
    });

    // 📋 获取备用提供商列表
    const fallbackProviders = this.getFallbackProviders(options.excludeProvider);

    // 🔄 依次尝试每个备用提供商
    for (const provider of fallbackProviders) {
      try {
        this.logger.debug('🔄 尝试备用提供商', {
          provider: provider.name,
          requestId
        });

        const response = await this.executeRequest(provider, prompt, {
          ...options,
          isFailover: true // 标记为故障转移请求
        }, requestId);

        this.logger.info('✅ 故障转移成功', {
          requestId,
          fallbackProvider: provider.name,
          originalError: originalError.message
        });

        return response;

      } catch (fallbackError) {
        this.logger.warn('❌ 备用提供商也失败了', {
          requestId,
          provider: provider.name,
          error: fallbackError.message
        });
        continue; // 尝试下一个备用提供商
      }
    }

    // 🚨 所有备用提供商都失败了
    this.logger.error('💥 所有故障转移尝试都失败了', { requestId });
    return null;
  }

  /**
   * 📊 更新使用统计
   *
   * 💡 记录每次请求的详细信息，用于分析和优化
   */
  updateStats(providerName, success, duration, cost) {
    // 📈 更新总体统计
    this.usageStats.totalRequests++;

    if (success) {
      this.usageStats.successfulRequests++;
      this.usageStats.totalCost += cost || 0;

      // 📊 更新平均响应时间
      const totalTime = this.usageStats.averageResponseTime * (this.usageStats.successfulRequests - 1) + duration;
      this.usageStats.averageResponseTime = totalTime / this.usageStats.successfulRequests;

      // 📋 更新提供商统计
      if (providerName) {
        const providerStats = this.usageStats.providerUsage.get(providerName);
        if (providerStats) {
          providerStats.requests++;
          providerStats.successes++;
          providerStats.totalCost += cost || 0;

          // 计算提供商平均响应时间
          const providerTotalTime = providerStats.averageResponseTime * (providerStats.successes - 1) + duration;
          providerStats.averageResponseTime = providerTotalTime / providerStats.successes;
        }
      }
    } else {
      this.usageStats.failedRequests++;

      if (providerName) {
        const providerStats = this.usageStats.providerUsage.get(providerName);
        if (providerStats) {
          providerStats.requests++;
          providerStats.failures++;
        }
      }
    }

    // 📊 触发统计更新事件
    this.emit('statsUpdated', {
      provider: providerName,
      success,
      duration,
      cost,
      totalRequests: this.usageStats.totalRequests
    });
  }
}
```

#### 🎯 关键学习点

**1. 事件驱动架构**
```javascript
// 继承 EventEmitter，支持事件发布订阅
class AIProviderManager extends EventEmitter {
  constructor() {
    super(); // 调用父类构造函数
  }

  // 发布事件
  someMethod() {
    this.emit('eventName', eventData);
  }
}

// 使用时监听事件
const aiManager = new AIProviderManager();
aiManager.on('statsUpdated', (data) => {
  console.log('统计信息更新:', data);
});
```

**2. 策略模式**
```javascript
// 定义多种路由策略
this.routingStrategies = {
  cost: this.costBasedRouting.bind(this),
  performance: this.performanceBasedRouting.bind(this),
  capability: this.capabilityBasedRouting.bind(this)
};

// 根据配置选择策略
const strategy = this.routingStrategies[options.strategy || 'capability'];
const provider = await strategy(providers, prompt, options);
```

**3. 错误处理和重试机制**
```javascript
// 多层错误处理
try {
  // 主要逻辑
  const response = await this.executeRequest(provider, prompt, options);
  return response;
} catch (error) {
  // 第一层：故障转移
  const fallbackResponse = await this.handleFailover(prompt, options, error);
  if (fallbackResponse) {
    return fallbackResponse;
  }

  // 第二层：降级处理
  if (options.allowDegradedMode) {
    return this.handleDegradedMode(prompt, options);
  }

  // 第三层：抛出错误
  throw new Error(`所有 AI 提供商都不可用: ${error.message}`);
}
```

#### 🤔 思考题

1. **为什么要使用多个 AI 提供商而不是只用一个？**
   - 提示：想想可靠性、成本、性能的权衡

2. **如何设计一个公平的负载均衡算法？**
   - 提示：考虑提供商的性能差异和成本差异

3. **缓存策略应该考虑哪些因素？**
   - 提示：想想数据时效性、存储成本、命中率

#### 🛠️ 试试看

1. **添加新的路由策略：**
   ```javascript
   // 基于响应时间的路由
   async responseTimeBasedRouting(providers, prompt, options) {
     // 选择平均响应时间最短的提供商
     providers.sort((a, b) => {
       const avgTimeA = this.calculateAverageResponseTime(a.metadata.responseTimeHistory);
       const avgTimeB = this.calculateAverageResponseTime(b.metadata.responseTimeHistory);
       return avgTimeA - avgTimeB;
     });

     return providers[0];
   }
   ```

2. **实现简单的健康检查：**
   ```javascript
   async healthCheck() {
     for (const [name, info] of this.providers) {
       try {
         // 发送简单的测试请求
         await info.instance.request('Hello', { timeout: 5000 });
         info.metadata.healthScore = Math.min(100, info.metadata.healthScore + 5);
       } catch (error) {
         info.metadata.healthScore = Math.max(0, info.metadata.healthScore - 10);
       }
     }
   }
   ```

### 第五个文件：数据库管理器 (src/core/data/DatabaseManager.js)

**为什么学习这个文件？**
- 理解数据持久化的重要性
- 学习数据库连接池管理
- 掌握事务处理和数据一致性
- 了解数据库迁移和版本管理

#### 核心概念：什么是数据库管理器？

**简单解释：** 数据库管理器是应用程序和数据库之间的"桥梁"，它负责：
- 🔗 管理数据库连接
- 📊 执行 SQL 查询
- 🔒 处理事务
- 🔄 管理数据迁移

```
    应用程序
        │
        ▼
┌─────────────────┐
│  数据库管理器    │ ← 桥梁和管理员
│                 │
│ • 连接池管理     │
│ • 查询执行       │
│ • 事务处理       │
│ • 错误重试       │
└─────────────────┘
        │
        ▼
┌─────────────────┐
│   数据库服务     │
│  (PostgreSQL)   │
└─────────────────┘
```

#### 关键代码片段解读

```javascript
/**
 * 🗄️ DatabaseManager - 数据库操作的统一管理器
 *
 * 🎯 主要职责：
 * 1. 管理数据库连接池
 * 2. 提供统一的查询接口
 * 3. 处理事务和错误重试
 * 4. 管理数据库迁移
 */
class DatabaseManager {
  constructor(config) {
    this.config = config;
    this.pools = new Map();        // 连接池映射
    this.migrations = [];          // 迁移记录
    this.isConnected = false;      // 连接状态
    this.retryCount = 0;          // 重试次数
    this.maxRetries = config.maxRetries || 3;
  }

  /**
   * 🚀 初始化数据库连接
   *
   * 💡 为什么需要连接池？
   * - 复用连接，避免频繁创建/销毁
   * - 控制并发连接数
   * - 提高性能和稳定性
   */
  async initialize() {
    try {
      // 1️⃣ 创建主数据库连接池
      this.pools.set('primary', new Pool({
        host: this.config.host,
        port: this.config.port,
        database: this.config.database,
        user: this.config.username,
        password: this.config.password,
        ssl: this.config.ssl,

        // 🏊 连接池配置
        min: this.config.pool?.min || 2,        // 最小连接数
        max: this.config.pool?.max || 20,       // 最大连接数
        acquireTimeoutMillis: 30000,            // 获取连接超时
        createTimeoutMillis: 30000,             // 创建连接超时
        idleTimeoutMillis: 30000,               // 空闲连接超时
        createRetryIntervalMillis: 200          // 重试间隔
      }));

      // 2️⃣ 如果配置了只读副本，创建只读连接池
      if (this.config.replica) {
        this.pools.set('replica', new Pool({
          ...this.config.replica,
          min: 2,
          max: 10
        }));
      }

      // 3️⃣ 测试连接
      await this.testConnection();

      // 4️⃣ 运行数据库迁移
      if (this.config.migrations?.autoRun) {
        await this.runMigrations();
      }

      this.isConnected = true;
      this.logger.info('✅ 数据库连接初始化成功');

    } catch (error) {
      this.logger.error('❌ 数据库连接初始化失败:', error);
      throw error;
    }
  }

  /**
   * 🔍 智能查询路由
   *
   * 💡 读写分离策略：
   * - 读操作 → 只读副本 (如果可用)
   * - 写操作 → 主数据库
   */
  async query(sql, params = [], options = {}) {
    // 🔍 判断是否为只读查询
    const isReadQuery = this.isReadOnlyQuery(sql);

    // 🎯 选择合适的连接池
    const poolName = isReadQuery && this.pools.has('replica') ? 'replica' : 'primary';
    const pool = this.pools.get(poolName);

    // 📊 性能监控
    const startTime = Date.now();
    let client;

    try {
      // 🔗 获取数据库连接
      client = await pool.connect();

      this.logger.debug('🔍 执行查询', {
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        params: params.length,
        pool: poolName
      });

      // 📊 执行查询
      const result = await client.query(sql, params);
      const duration = Date.now() - startTime;

      // ⚠️ 慢查询警告
      if (duration > 1000) {
        this.logger.warn('🐌 检测到慢查询', {
          sql: sql.substring(0, 200),
          duration: `${duration}ms`,
          rowCount: result.rowCount
        });
      }

      // 📈 记录查询统计
      this.recordQueryStats(sql, duration, result.rowCount, poolName);

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;

      // 📊 记录错误统计
      this.recordQueryError(sql, error, duration, poolName);

      // 🔄 处理连接错误和重试
      if (this.shouldRetry(error)) {
        return await this.retryQuery(sql, params, options);
      }

      throw error;

    } finally {
      // 🔓 释放连接
      if (client) {
        client.release();
      }
    }
  }

  /**
   * 🔒 事务处理
   *
   * 💡 什么是事务？
   * - 一组操作要么全部成功，要么全部失败
   * - 保证数据一致性
   * - 支持回滚操作
   */
  async transaction(callback) {
    const pool = this.pools.get('primary'); // 事务必须在主数据库执行
    const client = await pool.connect();

    try {
      // 🚀 开始事务
      await client.query('BEGIN');
      this.logger.debug('🔒 开始事务');

      // 📊 执行事务内的操作
      const result = await callback(client);

      // ✅ 提交事务
      await client.query('COMMIT');
      this.logger.debug('✅ 事务提交成功');

      return result;

    } catch (error) {
      // 🔄 回滚事务
      await client.query('ROLLBACK');
      this.logger.error('🔄 事务回滚', { error: error.message });

      throw error;

    } finally {
      // 🔓 释放连接
      client.release();
    }
  }

  /**
   * 🔄 数据库迁移管理
   *
   * 💡 什么是数据库迁移？
   * - 版本化的数据库结构变更
   * - 支持升级和回滚
   * - 团队协作时保持数据库同步
   */
  async runMigrations() {
    this.logger.info('🔄 开始运行数据库迁移');

    try {
      // 1️⃣ 确保迁移表存在
      await this.ensureMigrationTable();

      // 2️⃣ 获取已执行的迁移
      const executedMigrations = await this.getExecutedMigrations();

      // 3️⃣ 获取待执行的迁移文件
      const migrationFiles = await this.getMigrationFiles();

      // 4️⃣ 过滤出未执行的迁移
      const pendingMigrations = migrationFiles.filter(file =>
        !executedMigrations.includes(file.name)
      );

      if (pendingMigrations.length === 0) {
        this.logger.info('✅ 没有待执行的迁移');
        return;
      }

      // 5️⃣ 依次执行迁移
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration);
      }

      this.logger.info(`✅ 成功执行 ${pendingMigrations.length} 个迁移`);

    } catch (error) {
      this.logger.error('❌ 数据库迁移失败:', error);
      throw error;
    }
  }

  /**
   * 🔍 判断是否为只读查询
   *
   * 💡 通过 SQL 语句的开头判断操作类型
   */
  isReadOnlyQuery(sql) {
    const readOnlyPatterns = [
      /^\s*SELECT/i,           // SELECT 查询
      /^\s*WITH.*SELECT/i,     // CTE 查询
      /^\s*EXPLAIN/i,          // 执行计划
      /^\s*SHOW/i              // 显示信息
    ];

    return readOnlyPatterns.some(pattern => pattern.test(sql));
  }

  /**
   * 🔄 查询重试逻辑
   *
   * 💡 什么情况下需要重试？
   * - 网络临时中断
   * - 数据库临时不可用
   * - 连接池耗尽
   */
  async retryQuery(sql, params, options, attempt = 1) {
    if (attempt > this.maxRetries) {
      throw new Error(`查询重试 ${this.maxRetries} 次后仍然失败`);
    }

    // ⏱️ 指数退避延迟
    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
    await new Promise(resolve => setTimeout(resolve, delay));

    this.logger.warn(`🔄 查询重试 (第 ${attempt} 次)`, {
      sql: sql.substring(0, 100),
      delay: `${delay}ms`
    });

    try {
      return await this.query(sql, params, options);
    } catch (error) {
      if (this.shouldRetry(error)) {
        return await this.retryQuery(sql, params, options, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * 🔍 判断是否应该重试
   */
  shouldRetry(error) {
    const retryableErrors = [
      'ECONNRESET',      // 连接重置
      'ECONNREFUSED',    // 连接拒绝
      'ETIMEDOUT',       // 连接超时
      'ENOTFOUND',       // 主机未找到
      'connection terminated unexpectedly' // 连接意外终止
    ];

    return retryableErrors.some(errorCode =>
      error.code === errorCode || error.message.includes(errorCode)
    );
  }

  /**
   * 📊 记录查询统计
   */
  recordQueryStats(sql, duration, rowCount, poolName) {
    // 简单的统计记录
    if (!this.queryStats) {
      this.queryStats = {
        totalQueries: 0,
        totalDuration: 0,
        slowQueries: 0,
        poolUsage: {}
      };
    }

    this.queryStats.totalQueries++;
    this.queryStats.totalDuration += duration;

    if (duration > 1000) {
      this.queryStats.slowQueries++;
    }

    if (!this.queryStats.poolUsage[poolName]) {
      this.queryStats.poolUsage[poolName] = 0;
    }
    this.queryStats.poolUsage[poolName]++;
  }

  /**
   * 🧹 清理和关闭连接
   */
  async close() {
    this.logger.info('🧹 关闭数据库连接');

    for (const [name, pool] of this.pools) {
      try {
        await pool.end();
        this.logger.debug(`✅ 关闭连接池: ${name}`);
      } catch (error) {
        this.logger.error(`❌ 关闭连接池失败: ${name}`, error);
      }
    }

    this.pools.clear();
    this.isConnected = false;
  }

  /**
   * 📊 获取数据库统计信息
   */
  getStats() {
    const stats = {
      isConnected: this.isConnected,
      pools: {},
      queries: this.queryStats || {}
    };

    // 获取连接池状态
    for (const [name, pool] of this.pools) {
      stats.pools[name] = {
        totalCount: pool.totalCount,
        idleCount: pool.idleCount,
        waitingCount: pool.waitingCount
      };
    }

    return stats;
  }
}
```

#### 🎯 关键学习点

**1. 连接池管理**
```javascript
// 为什么需要连接池？
// ❌ 每次查询都创建新连接
async function badQuery() {
  const client = new Client(config);
  await client.connect();        // 耗时操作
  const result = await client.query('SELECT * FROM tasks');
  await client.end();            // 耗时操作
  return result;
}

// ✅ 使用连接池复用连接
const pool = new Pool(config);
async function goodQuery() {
  const client = await pool.connect(); // 从池中获取连接
  const result = await client.query('SELECT * FROM tasks');
  client.release();                    // 归还连接到池中
  return result;
}
```

**2. 事务处理**
```javascript
// 事务示例：转账操作
async function transferMoney(fromAccount, toAccount, amount) {
  return await this.transaction(async (client) => {
    // 1. 检查余额
    const balance = await client.query(
      'SELECT balance FROM accounts WHERE id = $1',
      [fromAccount]
    );

    if (balance.rows[0].balance < amount) {
      throw new Error('余额不足');
    }

    // 2. 扣除转出账户余额
    await client.query(
      'UPDATE accounts SET balance = balance - $1 WHERE id = $2',
      [amount, fromAccount]
    );

    // 3. 增加转入账户余额
    await client.query(
      'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
      [amount, toAccount]
    );

    // 如果任何一步失败，整个事务会自动回滚
    return { success: true, amount };
  });
}
```

**3. 读写分离**
```javascript
// 智能路由查询
async function smartQuery(sql, params) {
  // 读操作使用只读副本
  if (sql.startsWith('SELECT')) {
    return await this.query(sql, params, { pool: 'replica' });
  }

  // 写操作使用主数据库
  return await this.query(sql, params, { pool: 'primary' });
}
```

#### 🤔 思考题

1. **为什么需要连接池？直接创建连接不行吗？**
   - 提示：想想性能和资源管理

2. **什么情况下需要使用事务？**
   - 提示：考虑数据一致性和原子性

3. **读写分离有什么好处和挑战？**
   - 提示：想想性能提升和数据同步问题

#### 🛠️ 试试看

1. **实现简单的查询缓存：**
   ```javascript
   class DatabaseManager {
     constructor(config) {
       this.queryCache = new Map();
       this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
     }

     async query(sql, params) {
       // 只缓存 SELECT 查询
       if (sql.trim().toUpperCase().startsWith('SELECT')) {
         const cacheKey = `${sql}:${JSON.stringify(params)}`;
         const cached = this.queryCache.get(cacheKey);

         if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
           return cached.result;
         }

         const result = await this.executeQuery(sql, params);
         this.queryCache.set(cacheKey, {
           result,
           timestamp: Date.now()
         });

         return result;
       }

       return await this.executeQuery(sql, params);
     }
   }
   ```

2. **添加查询性能监控：**
   ```javascript
   async query(sql, params) {
     const startTime = process.hrtime.bigint();

     try {
       const result = await this.executeQuery(sql, params);
       const duration = Number(process.hrtime.bigint() - startTime) / 1000000; // 转换为毫秒

       // 记录慢查询
       if (duration > 1000) {
         console.warn(`慢查询警告: ${duration.toFixed(2)}ms`, {
           sql: sql.substring(0, 100),
           params: params.length
         });
       }

       return result;
     } catch (error) {
       const duration = Number(process.hrtime.bigint() - startTime) / 1000000;
       console.error(`查询失败: ${duration.toFixed(2)}ms`, {
         sql: sql.substring(0, 100),
         error: error.message
       });
       throw error;
     }
   }
   ```

---

## 🚀 进阶实战项目

### 项目 1：创建一个完整的任务管理命令 (高级)

**目标：** 综合运用所学知识，创建一个功能完整的任务管理命令。

**功能要求：**
1. 支持 CRUD 操作（创建、读取、更新、删除）
2. 集成 AI 分析功能
3. 支持数据验证和错误处理
4. 实现缓存和性能优化
5. 提供多种输出格式

```javascript
// src/commands/advanced/TaskManagerCommand.js
const BaseCommand = require('../base/BaseCommand');
const { TaskManager } = require('../../core/data/TaskManager');
const { AIProviderManager } = require('../../core/ai/AIProviderManager');
const { DatabaseManager } = require('../../core/data/DatabaseManager');
const chalk = require('chalk');

class TaskManagerCommand extends BaseCommand {
  constructor() {
    super('task-manager', '高级任务管理命令');

    // 依赖注入
    this.taskManager = new TaskManager();
    this.aiManager = new AIProviderManager();
    this.dbManager = new DatabaseManager();

    this.setupSubcommands();
    this.setupValidation();
    this.setupCaching();
  }

  setupSubcommands() {
    // 添加子命令
    this.addArgument('<action>', '操作类型 (create|list|update|delete|analyze)');
    this.addArgument('[target]', '目标对象 (任务ID或查询条件)');

    // 通用选项
    this.addOption('-f, --format <format>', '输出格式', 'table');
    this.addOption('--no-cache', '禁用缓存', false);
    this.addOption('--ai-analysis', '启用AI分析', false);
    this.addOption('--batch-size <size>', '批处理大小', 10);
  }

  async execute(args, options, context) {
    const [action, target] = args;

    // 路由到具体的处理方法
    switch (action.toLowerCase()) {
      case 'create':
        return await this.handleCreate(target, options, context);
      case 'list':
        return await this.handleList(target, options, context);
      case 'update':
        return await this.handleUpdate(target, options, context);
      case 'delete':
        return await this.handleDelete(target, options, context);
      case 'analyze':
        return await this.handleAnalyze(target, options, context);
      default:
        throw new Error(`未知操作: ${action}`);
    }
  }

  /**
   * 🆕 创建任务
   */
  async handleCreate(taskData, options, context) {
    try {
      // 1. 解析任务数据
      const parsedData = this.parseTaskData(taskData);

      // 2. 数据验证
      await this.validateTaskData(parsedData);

      // 3. AI 增强（如果启用）
      if (options.aiAnalysis) {
        parsedData.aiAnalysis = await this.enhanceWithAI(parsedData);
      }

      // 4. 创建任务
      const task = await this.taskManager.createTask(parsedData);

      // 5. 缓存结果
      if (!options.noCache) {
        await this.cacheTask(task);
      }

      return {
        action: 'create',
        task,
        success: true,
        message: '任务创建成功'
      };

    } catch (error) {
      throw new Error(`创建任务失败: ${error.message}`);
    }
  }

  /**
   * 📋 列出任务
   */
  async handleList(query, options, context) {
    try {
      // 1. 构建查询条件
      const queryConditions = this.buildQueryConditions(query, options);

      // 2. 检查缓存
      if (!options.noCache) {
        const cached = await this.getCachedResults(queryConditions);
        if (cached) {
          return { ...cached, fromCache: true };
        }
      }

      // 3. 执行查询
      const tasks = await this.taskManager.getTasks(queryConditions);

      // 4. AI 分析（如果启用）
      if (options.aiAnalysis) {
        for (const task of tasks) {
          task.aiInsights = await this.getAIInsights(task);
        }
      }

      // 5. 缓存结果
      const result = {
        action: 'list',
        tasks,
        total: tasks.length,
        query: queryConditions,
        success: true
      };

      if (!options.noCache) {
        await this.cacheResults(queryConditions, result);
      }

      return result;

    } catch (error) {
      throw new Error(`获取任务列表失败: ${error.message}`);
    }
  }

  /**
   * 🔄 更新任务
   */
  async handleUpdate(taskId, options, context) {
    try {
      // 1. 验证任务存在
      const existingTask = await this.taskManager.getTask(taskId);
      if (!existingTask) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      // 2. 解析更新数据
      const updateData = this.parseUpdateData(options);

      // 3. 数据验证
      await this.validateUpdateData(updateData, existingTask);

      // 4. 执行更新
      const updatedTask = await this.taskManager.updateTask(taskId, updateData);

      // 5. 清除相关缓存
      await this.invalidateCache(taskId);

      // 6. AI 分析变更影响（如果启用）
      if (options.aiAnalysis) {
        updatedTask.changeAnalysis = await this.analyzeChanges(existingTask, updatedTask);
      }

      return {
        action: 'update',
        task: updatedTask,
        changes: updateData,
        success: true,
        message: '任务更新成功'
      };

    } catch (error) {
      throw new Error(`更新任务失败: ${error.message}`);
    }
  }

  /**
   * 🗑️ 删除任务
   */
  async handleDelete(taskId, options, context) {
    try {
      // 1. 验证任务存在
      const task = await this.taskManager.getTask(taskId);
      if (!task) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      // 2. 检查依赖关系
      const dependents = await this.taskManager.getDependents(taskId);
      if (dependents.length > 0 && !options.force) {
        throw new Error(`任务 ${taskId} 被其他任务依赖，无法删除。使用 --force 强制删除。`);
      }

      // 3. 执行删除
      await this.taskManager.deleteTask(taskId);

      // 4. 清除缓存
      await this.invalidateCache(taskId);

      return {
        action: 'delete',
        taskId,
        deletedTask: task,
        success: true,
        message: '任务删除成功'
      };

    } catch (error) {
      throw new Error(`删除任务失败: ${error.message}`);
    }
  }

  /**
   * 🧠 AI 分析任务
   */
  async handleAnalyze(taskId, options, context) {
    try {
      // 1. 获取任务数据
      const task = await this.taskManager.getTask(taskId);
      if (!task) {
        throw new Error(`任务 ${taskId} 不存在`);
      }

      // 2. 获取相关数据
      const subtasks = await this.taskManager.getSubtasks(taskId);
      const dependencies = await this.taskManager.getDependencies(taskId);
      const history = await this.taskManager.getTaskHistory(taskId);

      // 3. AI 综合分析
      const analysis = await this.performComprehensiveAnalysis({
        task,
        subtasks,
        dependencies,
        history
      });

      return {
        action: 'analyze',
        taskId,
        analysis,
        success: true,
        message: 'AI 分析完成'
      };

    } catch (error) {
      throw new Error(`AI 分析失败: ${error.message}`);
    }
  }

  /**
   * 🧠 AI 增强功能
   */
  async enhanceWithAI(taskData) {
    const prompt = `
      请分析以下任务并提供建议：

      标题: ${taskData.title}
      描述: ${taskData.description || '无描述'}
      优先级: ${taskData.priority || '未设置'}

      请提供：
      1. 复杂度评估 (1-10)
      2. 预估工时 (小时)
      3. 潜在风险
      4. 实现建议
      5. 测试要点
    `;

    const response = await this.aiManager.request(prompt, {
      capability: 'analysis',
      maxTokens: 1000
    });

    return this.parseAIResponse(response.content);
  }

  /**
   * 📊 综合分析
   */
  async performComprehensiveAnalysis(data) {
    const { task, subtasks, dependencies, history } = data;

    // 并行执行多个分析
    const [
      complexityAnalysis,
      riskAnalysis,
      timelineAnalysis,
      qualityAnalysis
    ] = await Promise.all([
      this.analyzeComplexity(task, subtasks),
      this.analyzeRisks(task, dependencies),
      this.analyzeTimeline(task, history),
      this.analyzeQuality(task, subtasks)
    ]);

    return {
      complexity: complexityAnalysis,
      risks: riskAnalysis,
      timeline: timelineAnalysis,
      quality: qualityAnalysis,
      recommendations: this.generateRecommendations({
        complexityAnalysis,
        riskAnalysis,
        timelineAnalysis,
        qualityAnalysis
      })
    };
  }

  /**
   * 🎨 格式化显示结果
   */
  displayFormattedResult(result, options) {
    switch (result.action) {
      case 'create':
        this.displayCreateResult(result, options);
        break;
      case 'list':
        this.displayListResult(result, options);
        break;
      case 'update':
        this.displayUpdateResult(result, options);
        break;
      case 'delete':
        this.displayDeleteResult(result, options);
        break;
      case 'analyze':
        this.displayAnalysisResult(result, options);
        break;
    }
  }

  displayCreateResult(result, options) {
    console.log(chalk.green(`✅ ${result.message}`));
    console.log(`📋 任务ID: ${result.task.id}`);
    console.log(`📝 标题: ${result.task.title}`);

    if (result.task.aiAnalysis) {
      console.log(chalk.blue('\n🧠 AI 分析结果:'));
      console.log(`复杂度: ${result.task.aiAnalysis.complexity}/10`);
      console.log(`预估工时: ${result.task.aiAnalysis.estimatedHours}小时`);
    }
  }

  displayAnalysisResult(result, options) {
    const { analysis } = result;

    console.log(chalk.bold('\n🧠 AI 综合分析报告'));
    console.log('='.repeat(50));

    // 复杂度分析
    console.log(chalk.blue('\n📊 复杂度分析:'));
    console.log(`总体复杂度: ${analysis.complexity.overall}/10`);
    console.log(`技术复杂度: ${analysis.complexity.technical}/10`);
    console.log(`业务复杂度: ${analysis.complexity.business}/10`);

    // 风险分析
    console.log(chalk.yellow('\n⚠️ 风险分析:'));
    analysis.risks.forEach((risk, index) => {
      console.log(`${index + 1}. ${risk.description} (${risk.level})`);
    });

    // 时间线分析
    console.log(chalk.green('\n📅 时间线分析:'));
    console.log(`预计完成时间: ${analysis.timeline.estimatedCompletion}`);
    console.log(`关键路径: ${analysis.timeline.criticalPath.join(' → ')}`);

    // 建议
    console.log(chalk.magenta('\n💡 改进建议:'));
    analysis.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
}

module.exports = TaskManagerCommand;
```

### 项目 2：创建一个插件系统 (专家级)

**目标：** 实现一个完整的插件系统，支持动态加载和热重载。

```javascript
// src/plugins/core/PluginManager.js
const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class PluginManager extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = config;
    this.plugins = new Map();           // 已加载的插件
    this.pluginConfigs = new Map();     // 插件配置
    this.hooks = new Map();             // 钩子注册
    this.watchers = new Map();          // 文件监视器
    this.pluginDirectory = config.directory || './plugins';
  }

  /**
   * 🚀 初始化插件系统
   */
  async initialize() {
    try {
      // 1. 确保插件目录存在
      await this.ensurePluginDirectory();

      // 2. 扫描并加载插件
      await this.scanAndLoadPlugins();

      // 3. 启用热重载（开发模式）
      if (this.config.hotReload) {
        await this.enableHotReload();
      }

      this.emit('initialized', {
        pluginCount: this.plugins.size,
        directory: this.pluginDirectory
      });

    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 🔍 扫描并加载所有插件
   */
  async scanAndLoadPlugins() {
    const pluginDirs = await fs.readdir(this.pluginDirectory);

    for (const dir of pluginDirs) {
      const pluginPath = path.join(this.pluginDirectory, dir);
      const stat = await fs.stat(pluginPath);

      if (stat.isDirectory()) {
        await this.loadPlugin(pluginPath);
      }
    }
  }

  /**
   * 📦 加载单个插件
   */
  async loadPlugin(pluginPath) {
    try {
      // 1. 读取插件配置
      const configPath = path.join(pluginPath, 'plugin.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      // 2. 验证插件配置
      this.validatePluginConfig(config);

      // 3. 检查依赖
      await this.checkPluginDependencies(config);

      // 4. 加载插件主文件
      const mainPath = path.join(pluginPath, config.main || 'index.js');

      // 清除 require 缓存以支持热重载
      delete require.cache[require.resolve(mainPath)];

      const PluginClass = require(mainPath);
      const plugin = new PluginClass();

      // 5. 初始化插件
      await plugin.initialize({
        config: config,
        pluginManager: this,
        logger: this.logger
      });

      // 6. 注册插件
      this.plugins.set(config.name, {
        instance: plugin,
        config: config,
        path: pluginPath,
        loadedAt: new Date()
      });

      this.pluginConfigs.set(config.name, config);

      // 7. 注册插件的钩子
      this.registerPluginHooks(config.name, plugin);

      this.emit('pluginLoaded', {
        name: config.name,
        version: config.version,
        path: pluginPath
      });

      console.log(`✅ 插件加载成功: ${config.name} v${config.version}`);

    } catch (error) {
      console.error(`❌ 插件加载失败: ${pluginPath}`, error);
      this.emit('pluginLoadError', { path: pluginPath, error });
    }
  }

  /**
   * 🔄 热重载插件
   */
  async reloadPlugin(pluginName) {
    const pluginInfo = this.plugins.get(pluginName);
    if (!pluginInfo) {
      throw new Error(`插件 ${pluginName} 未找到`);
    }

    try {
      // 1. 卸载插件
      await this.unloadPlugin(pluginName);

      // 2. 重新加载插件
      await this.loadPlugin(pluginInfo.path);

      this.emit('pluginReloaded', { name: pluginName });

    } catch (error) {
      this.emit('pluginReloadError', { name: pluginName, error });
      throw error;
    }
  }

  /**
   * 🗑️ 卸载插件
   */
  async unloadPlugin(pluginName) {
    const pluginInfo = this.plugins.get(pluginName);
    if (!pluginInfo) {
      return;
    }

    try {
      // 1. 调用插件清理方法
      if (pluginInfo.instance.cleanup) {
        await pluginInfo.instance.cleanup();
      }

      // 2. 注销钩子
      this.unregisterPluginHooks(pluginName);

      // 3. 移除插件
      this.plugins.delete(pluginName);
      this.pluginConfigs.delete(pluginName);

      this.emit('pluginUnloaded', { name: pluginName });

    } catch (error) {
      this.emit('pluginUnloadError', { name: pluginName, error });
      throw error;
    }
  }

  /**
   * 🎣 执行钩子
   */
  async executeHook(hookName, context) {
    const handlers = this.hooks.get(hookName) || [];
    const results = [];

    for (const handler of handlers) {
      try {
        const result = await handler.callback(context);
        results.push({
          plugin: handler.plugin,
          result: result
        });
      } catch (error) {
        console.error(`钩子执行失败: ${hookName} (${handler.plugin})`, error);
        results.push({
          plugin: handler.plugin,
          error: error.message
        });
      }
    }

    return results;
  }
}

// 插件基类
class BasePlugin {
  constructor(name, version, metadata = {}) {
    this.name = name;
    this.version = version;
    this.metadata = metadata;
    this.hooks = [];
    this.commands = [];
    this.apiRoutes = [];
  }

  /**
   * 注册钩子
   */
  registerHook(hookName, callback) {
    this.hooks.push({ name: hookName, callback });
  }

  /**
   * 注册命令
   */
  registerCommand(commandName, commandClass) {
    this.commands.push({ name: commandName, class: commandClass });
  }

  /**
   * 注册 API 路由
   */
  registerAPIRoute(method, path, handler) {
    this.apiRoutes.push({ method, path, handler });
  }

  /**
   * 初始化插件 - 子类重写
   */
  async initialize(context) {
    // 子类实现
  }

  /**
   * 清理插件 - 子类重写
   */
  async cleanup() {
    // 子类实现
  }
}

module.exports = { PluginManager, BasePlugin };
```

---

## 🎓 学习成果检验

### 自测问题

**基础概念 (必答)**
1. 解释什么是命令模式，并说明它在 Task Master 中的应用
2. 什么是依赖注入？为什么要使用它？
3. 事件驱动架构的优势是什么？

**代码理解 (选答 3 题)**
1. 分析 `BaseCommand.executeCommand()` 方法的执行流程
2. 解释 `AIProviderManager` 如何实现智能路由
3. 描述数据库连接池的工作原理
4. 说明插件系统的加载和卸载过程

**实践应用 (选答 2 题)**
1. 设计一个新的命令，说明需要考虑哪些方面
2. 如何为现有系统添加缓存功能？
3. 描述如何实现一个简单的权限控制系统
4. 设计一个监控和告警系统的架构

### 实践项目建议

**初级项目**
- 创建一个简单的计算器命令
- 实现一个文件操作命令集
- 开发一个简单的配置管理工具

**中级项目**
- 构建一个完整的任务管理系统
- 实现一个简单的 Web API 服务
- 开发一个数据导入导出工具

**高级项目**
- 设计一个微服务架构的应用
- 实现一个完整的插件系统
- 构建一个实时数据处理系统

---

## 📖 总结与展望

通过这个详细的代码导读，你应该已经掌握了：

### 🎯 核心技能
- **面向对象编程**：类、继承、封装、多态
- **异步编程**：Promise、async/await、事件处理
- **设计模式**：命令模式、策略模式、观察者模式
- **系统架构**：分层架构、模块化设计、插件系统

### 🛠️ 实践能力
- **命令行工具开发**：参数解析、错误处理、用户交互
- **数据库操作**：连接管理、事务处理、查询优化
- **API 集成**：外部服务调用、错误处理、缓存策略
- **系统监控**：日志记录、性能监控、错误追踪

### 🚀 进阶方向
- **微服务架构**：服务拆分、服务发现、负载均衡
- **容器化部署**：Docker、Kubernetes、CI/CD
- **性能优化**：缓存策略、数据库优化、并发处理
- **安全加固**：认证授权、数据加密、安全审计

### 💡 持续学习建议

1. **深入理解业务逻辑**：不只是学习技术，更要理解为什么这样设计
2. **多写代码多实践**：理论知识需要通过实践来巩固
3. **阅读优秀开源项目**：学习业界最佳实践
4. **参与社区讨论**：与其他开发者交流经验
5. **关注技术发展**：持续学习新技术和新模式

记住：**优秀的程序员不是知道所有答案的人，而是知道如何找到答案的人！**

继续保持好奇心，不断学习和实践，你一定能成为一名出色的开发者！🌟
```
