import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Divider,
  message
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  WechatOutlined,
  QqOutlined,
  PhoneOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { LoginForm } from '../../types';
import { useAuthStore } from '../../stores/authStore';

const { Title, Text } = Typography;

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 32px;
  
  h1 {
    color: #1890ff;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    margin: 0;
  }
`;

const ThirdPartyLogin = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
`;

const ThirdPartyButton = styled(Button)`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.wechat {
    background: #07c160;
    border-color: #07c160;
    color: white;
    
    &:hover {
      background: #06ad56;
      border-color: #06ad56;
    }
  }
  
  &.qq {
    background: #1296db;
    border-color: #1296db;
    color: white;
    
    &:hover {
      background: #0e7bb8;
      border-color: #0e7bb8;
    }
  }
`;

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isLoading } = useAuthStore();
  const [form] = Form.useForm();

  const from = (location.state as any)?.from?.pathname || '/';

  const handleSubmit = async (values: LoginForm) => {
    const success = await login(values);
    if (success) {
      navigate(from, { replace: true });
    }
  };

  const handleThirdPartyLogin = (type: 'wechat' | 'qq') => {
    message.info(`${type === 'wechat' ? '微信' : 'QQ'}登录功能暂未开放`);
  };

  return (
    <LoginContainer>
      <LoginCard>
        <Logo>
          <Title level={1}>收书卖书</Title>
          <Text type="secondary">欢迎回来</Text>
        </Logo>

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="phone"
            rules={[
              { required: true, message: '请输入手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
            ]}
          >
            <Input
              prefix={<PhoneOutlined />}
              placeholder="手机号"
              maxLength={11}
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <Space split={<Divider type="vertical" />}>
            <Link to="/forgot-password">忘记密码</Link>
            <Link to="/register">立即注册</Link>
          </Space>
        </div>

        <Divider>
          <Text type="secondary">其他登录方式</Text>
        </Divider>

        <ThirdPartyLogin>
          <ThirdPartyButton
            className="wechat"
            icon={<WechatOutlined />}
            onClick={() => handleThirdPartyLogin('wechat')}
          />
          <ThirdPartyButton
            className="qq"
            icon={<QqOutlined />}
            onClick={() => handleThirdPartyLogin('qq')}
          />
        </ThirdPartyLogin>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
