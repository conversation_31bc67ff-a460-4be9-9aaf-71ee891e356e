# 通用忽略文件
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
*.log
logs
*.pid
*.seed
*.pid.lock
.nyc_output
coverage
.grunt
bower_components
.lock-wscript
build/Release
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.env

# Git
.git
.gitignore
README.md

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# IDE
.vscode
.idea
*.swp
*.swo
*~

# 测试
test
tests
__tests__
*.test.js
*.spec.js

# 文档
docs
*.md
