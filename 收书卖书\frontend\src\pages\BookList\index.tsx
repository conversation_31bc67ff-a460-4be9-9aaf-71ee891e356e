import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Input,
  Select,
  Slider,
  Button,
  Space,
  Pagination,
  Spin,
  Empty,
  Typography,
  Divider,
  Tag
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  AppstoreOutlined,
  BarsOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { Book, Category, BookSearchParams } from '../../types';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';
import BookCard from '../../components/business/BookCard/BookCard';

const { Title } = Typography;
const { Option } = Select;

const FilterSidebar = styled(Card)`
  height: fit-content;
  position: sticky;
  top: 24px;
  
  .ant-card-body {
    padding: 16px;
  }
`;

const FilterSection = styled.div`
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .filter-title {
    font-weight: 600;
    margin-bottom: 12px;
    color: #262626;
  }
`;

const SearchHeader = styled.div`
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const ResultsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
`;

const ViewToggle = styled.div`
  display: flex;
  gap: 8px;
`;

const BookList: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [books, setBooks] = useState<Book[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 搜索和筛选状态
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState<BookSearchParams>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'DESC'
  });

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    // 从URL参数初始化搜索条件
    const params: BookSearchParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      search: searchParams.get('search') || undefined,
      category_id: searchParams.get('category_id') || undefined,
      min_price: searchParams.get('min_price') ? parseFloat(searchParams.get('min_price')!) : undefined,
      max_price: searchParams.get('max_price') ? parseFloat(searchParams.get('max_price')!) : undefined,
      condition: searchParams.get('condition') || undefined,
      sort_by: searchParams.get('sort_by') || 'created_at',
      sort_order: (searchParams.get('sort_order') as 'ASC' | 'DESC') || 'DESC'
    };

    setFilters(params);
    setSearchValue(params.search || '');
    loadBooks(params);
  }, [searchParams]);

  const loadCategories = async () => {
    try {
      const response = await categoriesService.getFlatCategories();
      if (response.success) {
        setCategories(response.data || []);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const loadBooks = async (params: BookSearchParams) => {
    try {
      setLoading(true);
      const response = await booksService.getBooks(params);
      if (response.success) {
        setBooks(response.data.books || []);
        setPagination({
          current: response.data.pagination.current_page,
          pageSize: response.data.pagination.items_per_page,
          total: response.data.pagination.total_items
        });
      }
    } catch (error) {
      console.error('加载图书失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSearchParams = (newParams: Partial<BookSearchParams>) => {
    const updatedParams = { ...filters, ...newParams, page: 1 };
    setFilters(updatedParams);

    // 更新URL参数
    const urlParams = new URLSearchParams();
    Object.entries(updatedParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        urlParams.set(key, value.toString());
      }
    });
    setSearchParams(urlParams);
  };

  const handleSearch = (value: string) => {
    updateSearchParams({ search: value || undefined });
  };

  const handleFilterChange = (key: keyof BookSearchParams, value: any) => {
    updateSearchParams({ [key]: value });
  };

  const handlePaginationChange = (page: number, pageSize?: number) => {
    const newParams = { ...filters, page, limit: pageSize || filters.limit };
    setFilters(newParams);

    const urlParams = new URLSearchParams(searchParams);
    urlParams.set('page', page.toString());
    if (pageSize) {
      urlParams.set('limit', pageSize.toString());
    }
    setSearchParams(urlParams);
  };

  const clearFilters = () => {
    setSearchValue('');
    setSearchParams({});
  };

  const priceRange = [filters.min_price || 0, filters.max_price || 500];

  return (
    <div>
      {/* 搜索头部 */}
      <SearchHeader>
        <Row gutter={[16, 16]} align="middle">
          <Col flex="auto">
            <Input.Search
              placeholder="搜索图书、作者、ISBN..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
            />
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="排序方式"
                style={{ width: 120 }}
                value={`${filters.sort_by}_${filters.sort_order}`}
                onChange={(value) => {
                  const [sort_by, sort_order] = value.split('_');
                  handleFilterChange('sort_by', sort_by);
                  handleFilterChange('sort_order', sort_order);
                }}
              >
                <Option value="created_at_DESC">最新上架</Option>
                <Option value="price_ASC">价格从低到高</Option>
                <Option value="price_DESC">价格从高到低</Option>
                <Option value="sales_count_DESC">销量最高</Option>
                <Option value="views_DESC">浏览最多</Option>
              </Select>
              <Button
                icon={<ClearOutlined />}
                onClick={clearFilters}
              >
                清除筛选
              </Button>
            </Space>
          </Col>
        </Row>
      </SearchHeader>

      <Row gutter={[24, 24]}>
        {/* 筛选侧边栏 */}
        <Col xs={24} md={6}>
          <FilterSidebar title="筛选条件">
            {/* 分类筛选 */}
            <FilterSection>
              <div className="filter-title">图书分类</div>
              <Select
                placeholder="选择分类"
                style={{ width: '100%' }}
                allowClear
                value={filters.category_id}
                onChange={(value) => handleFilterChange('category_id', value)}
              >
                {categories.map(category => (
                  <Option key={category.id} value={category.id}>
                    {category.parent ? `${category.parent.name} - ${category.name}` : category.name}
                  </Option>
                ))}
              </Select>
            </FilterSection>

            {/* 价格筛选 */}
            <FilterSection>
              <div className="filter-title">价格范围</div>
              <Slider
                range
                min={0}
                max={500}
                value={priceRange}
                onChange={(value) => {
                  handleFilterChange('min_price', value[0] || undefined);
                  handleFilterChange('max_price', value[1] || undefined);
                }}
                tooltip={{
                  formatter: (value) => `¥${value}`
                }}
              />
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
                <span>¥{priceRange[0]}</span>
                <span>¥{priceRange[1]}</span>
              </div>
            </FilterSection>

            {/* 状况筛选 */}
            <FilterSection>
              <div className="filter-title">图书状况</div>
              <Select
                placeholder="选择状况"
                style={{ width: '100%' }}
                allowClear
                value={filters.condition}
                onChange={(value) => handleFilterChange('condition', value)}
              >
                <Option value="全新">全新</Option>
                <Option value="九成新">九成新</Option>
                <Option value="八成新">八成新</Option>
                <Option value="七成新">七成新</Option>
                <Option value="六成新">六成新</Option>
              </Select>
            </FilterSection>
          </FilterSidebar>
        </Col>

        {/* 图书列表 */}
        <Col xs={24} md={18}>
          <ResultsHeader>
            <div>
              <Title level={4} style={{ margin: 0 }}>
                找到 {pagination.total} 本图书
              </Title>
              {filters.search && (
                <Tag color="blue" style={{ marginTop: 8 }}>
                  搜索: {filters.search}
                </Tag>
              )}
            </div>
            <ViewToggle>
              <Button
                type={viewMode === 'grid' ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => setViewMode('grid')}
              />
              <Button
                type={viewMode === 'list' ? 'primary' : 'default'}
                icon={<BarsOutlined />}
                onClick={() => setViewMode('list')}
              />
            </ViewToggle>
          </ResultsHeader>

          <Spin spinning={loading}>
            {books.length > 0 ? (
              <>
                <Row gutter={[16, 16]}>
                  {books.map(book => (
                    <Col
                      key={book.id}
                      xs={24}
                      sm={viewMode === 'grid' ? 12 : 24}
                      md={viewMode === 'grid' ? 8 : 24}
                      lg={viewMode === 'grid' ? 6 : 24}
                    >
                      <BookCard book={book} />
                    </Col>
                  ))}
                </Row>

                <div style={{ textAlign: 'center', marginTop: 32 }}>
                  <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                    onChange={handlePaginationChange}
                  />
                </div>
              </>
            ) : (
              <Empty
                description="暂无图书"
                style={{ margin: '64px 0' }}
              />
            )}
          </Spin>
        </Col>
      </Row>
    </div>
  );
};

export default BookList;
