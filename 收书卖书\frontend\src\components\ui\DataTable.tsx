import React from 'react';
import { Table, Card, Space, Button, Input, Select, DatePicker } from 'antd';
import { SearchOutlined, ReloadOutlined, ExportOutlined } from '@ant-design/icons';
import type { ColumnsType, TableProps } from 'antd/es/table';
import styled from 'styled-components';
import { PAGINATION_CONFIG } from '../../utils/constants';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const TableContainer = styled(Card)`
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 16px;
    
    .table-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
    }
    
    .table-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
  
  .table-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
    align-items: center;
  }
`;

interface FilterConfig {
  type: 'search' | 'select' | 'dateRange';
  key: string;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  width?: number;
  allowClear?: boolean;
}

interface DataTableProps<T = any> extends Omit<TableProps<T>, 'pagination'> {
  title?: string;
  data: T[];
  columns: ColumnsType<T>;
  loading?: boolean;
  filters?: FilterConfig[];
  actions?: React.ReactNode[];
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize?: number) => void;
  };
  onRefresh?: () => void;
  onExport?: () => void;
  onFilterChange?: (key: string, value: any) => void;
  showHeader?: boolean;
  showFilters?: boolean;
}

const DataTable = <T extends Record<string, any>>({
  title,
  data,
  columns,
  loading = false,
  filters = [],
  actions = [],
  pagination,
  onRefresh,
  onExport,
  onFilterChange,
  showHeader = true,
  showFilters = true,
  ...tableProps
}: DataTableProps<T>) => {
  const handleFilterChange = (key: string, value: any) => {
    onFilterChange?.(key, value);
  };

  const renderFilter = (filter: FilterConfig) => {
    switch (filter.type) {
      case 'search':
        return (
          <Search
            key={filter.key}
            placeholder={filter.placeholder}
            allowClear={filter.allowClear}
            style={{ width: filter.width || 200 }}
            onSearch={(value) => handleFilterChange(filter.key, value)}
            onChange={(e) => {
              if (!e.target.value) {
                handleFilterChange(filter.key, '');
              }
            }}
          />
        );
      
      case 'select':
        return (
          <Select
            key={filter.key}
            placeholder={filter.placeholder}
            allowClear={filter.allowClear}
            style={{ width: filter.width || 120 }}
            onChange={(value) => handleFilterChange(filter.key, value)}
          >
            {filter.options?.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      case 'dateRange':
        return (
          <RangePicker
            key={filter.key}
            placeholder={['开始日期', '结束日期']}
            style={{ width: filter.width || 240 }}
            onChange={(dates) => handleFilterChange(filter.key, dates)}
          />
        );
      
      default:
        return null;
    }
  };

  const defaultActions = [
    onRefresh && (
      <Button
        key="refresh"
        icon={<ReloadOutlined />}
        onClick={onRefresh}
      >
        刷新
      </Button>
    ),
    onExport && (
      <Button
        key="export"
        icon={<ExportOutlined />}
        onClick={onExport}
      >
        导出
      </Button>
    )
  ].filter(Boolean);

  const allActions = [...defaultActions, ...actions];

  return (
    <TableContainer>
      {showHeader && (title || allActions.length > 0) && (
        <div className="table-header">
          {title && <h3 className="table-title">{title}</h3>}
          {allActions.length > 0 && (
            <div className="table-actions">
              <Space>{allActions}</Space>
            </div>
          )}
        </div>
      )}
      
      {showFilters && filters.length > 0 && (
        <div className="table-filters">
          {filters.map(renderFilter)}
        </div>
      )}
      
      <Table
        {...tableProps}
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={
          pagination
            ? {
                ...PAGINATION_CONFIG,
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                onChange: pagination.onChange
              }
            : false
        }
        scroll={{ x: 'max-content' }}
      />
    </TableContainer>
  );
};

export default DataTable;
