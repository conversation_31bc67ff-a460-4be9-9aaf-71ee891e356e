import React, { useState } from 'react';
import { Upload, message, Modal } from 'antd';
import { PlusOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import styled from 'styled-components';
import { uploadService } from '../../services/upload';
import { UPLOAD_LIMITS } from '../../utils/constants';

const UploadContainer = styled.div`
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }
  
  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }
  
  .upload-item {
    position: relative;
    
    .upload-actions {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      &:hover {
        opacity: 1;
      }
      
      .action-btn {
        color: white;
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.3s ease;
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
`;

interface ImageUploadProps {
  value?: UploadFile[];
  onChange?: (fileList: UploadFile[]) => void;
  maxCount?: number;
  type?: 'avatar' | 'book';
  listType?: 'picture-card' | 'picture';
  disabled?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value = [],
  onChange,
  maxCount = 5,
  type = 'book',
  listType = 'picture-card',
  disabled = false
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const uploadConfig = UPLOAD_LIMITS[type === 'avatar' ? 'avatar' : 'bookImages'];

  const handleCustomRequest: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError, onProgress } = options;

    try {
      onProgress?.({ percent: 10 });

      let response;
      if (type === 'avatar') {
        response = await uploadService.uploadAvatar(file as File);
      } else {
        response = await uploadService.uploadBookImages([file as File]);
      }

      onProgress?.({ percent: 100 });

      if (response.success) {
        const fileData = type === 'avatar' 
          ? response.data 
          : response.data?.files?.[0];
          
        if (fileData) {
          onSuccess?.(fileData);
        } else {
          throw new Error('上传响应数据格式错误');
        }
      } else {
        throw new Error(response.message || '上传失败');
      }
    } catch (error: any) {
      console.error('文件上传失败:', error);
      message.error(error.message || '文件上传失败');
      onError?.(error);
    }
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件');
      return false;
    }

    const isLtMaxSize = file.size <= uploadConfig.maxSize;
    if (!isLtMaxSize) {
      message.error(`图片大小不能超过 ${uploadConfig.maxSize / 1024 / 1024}MB`);
      return false;
    }

    return true;
  };

  const handleChange: UploadProps['onChange'] = ({ fileList }) => {
    // 过滤掉上传失败的文件
    const validFileList = fileList.filter(file => 
      file.status !== 'error'
    ).map(file => {
      // 确保文件有正确的URL
      if (file.status === 'done' && file.response) {
        return {
          ...file,
          url: file.response.url || file.url
        };
      }
      return file;
    });

    onChange?.(validFileList);
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      // 如果没有预览图，生成一个
      if (file.originFileObj) {
        file.preview = await getBase64(file.originFileObj);
      }
    }

    setPreviewImage(file.url || file.preview || '');
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const handleRemove = (file: UploadFile) => {
    const newFileList = value.filter(item => item.uid !== file.uid);
    onChange?.(newFileList);
  };

  const getBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>
        {type === 'avatar' ? '上传头像' : '上传图片'}
      </div>
    </div>
  );

  return (
    <UploadContainer>
      <Upload
        listType={listType}
        fileList={value}
        customRequest={handleCustomRequest}
        beforeUpload={beforeUpload}
        onChange={handleChange}
        onPreview={handlePreview}
        onRemove={handleRemove}
        disabled={disabled}
        maxCount={maxCount}
        multiple={type !== 'avatar'}
        accept={uploadConfig.accept}
        itemRender={(originNode, file) => (
          <div className="upload-item">
            {originNode}
            {file.status === 'done' && (
              <div className="upload-actions">
                <EyeOutlined
                  className="action-btn"
                  onClick={() => handlePreview(file)}
                />
                {!disabled && (
                  <DeleteOutlined
                    className="action-btn"
                    onClick={() => handleRemove(file)}
                  />
                )}
              </div>
            )}
          </div>
        )}
      >
        {value.length >= maxCount ? null : uploadButton}
      </Upload>

      <Modal
        open={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </UploadContainer>
  );
};

export default ImageUpload;
