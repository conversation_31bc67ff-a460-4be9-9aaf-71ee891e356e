import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Tabs,
  Empty,
  Button,
  Spin,
  Statistic,
  Row,
  Col,
  Typography
} from 'antd';
import {
  ShoppingOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  TruckOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { Order } from '../../types';
import { ordersService } from '../../services/orders';
// import OrderList from '../Order/OrderList';

const { Title } = Typography;
const { TabPane } = Tabs;

const StatsCard = styled(Card)`
  .ant-statistic {
    text-align: center;
  }
  
  .ant-statistic-content {
    color: #1890ff;
  }
`;

const ProfileOrders: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    paid: 0,
    delivering: 0,
    delivered: 0,
    cancelled: 0
  });

  useEffect(() => {
    loadOrderStats();
  }, []);

  const loadOrderStats = async () => {
    try {
      setLoading(true);
      // 这里应该调用获取订单统计的API
      // const response = await ordersService.getOrderStats();
      // if (response.success) {
      //   setStats(response.data);
      // }
      
      // 模拟数据
      setTimeout(() => {
        setStats({
          total: 15,
          pending: 2,
          paid: 3,
          delivering: 1,
          delivered: 8,
          cancelled: 1
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('加载订单统计失败:', error);
      setLoading(false);
    }
  };

  return (
    <div>
      {/* 订单统计 */}
      <Card style={{ marginBottom: 24 }}>
        <Title level={4}>订单统计</Title>
        <Spin spinning={loading}>
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={8} md={4}>
              <StatsCard size="small">
                <Statistic
                  title="全部订单"
                  value={stats.total}
                  prefix={<ShoppingOutlined />}
                />
              </StatsCard>
            </Col>
            <Col xs={12} sm={8} md={4}>
              <StatsCard size="small">
                <Statistic
                  title="待支付"
                  value={stats.pending}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </StatsCard>
            </Col>
            <Col xs={12} sm={8} md={4}>
              <StatsCard size="small">
                <Statistic
                  title="已支付"
                  value={stats.paid}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </StatsCard>
            </Col>
            <Col xs={12} sm={8} md={4}>
              <StatsCard size="small">
                <Statistic
                  title="配送中"
                  value={stats.delivering}
                  prefix={<TruckOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </StatsCard>
            </Col>
            <Col xs={12} sm={8} md={4}>
              <StatsCard size="small">
                <Statistic
                  title="已完成"
                  value={stats.delivered}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </StatsCard>
            </Col>
            <Col xs={12} sm={8} md={4}>
              <StatsCard size="small">
                <Statistic
                  title="已取消"
                  value={stats.cancelled}
                  prefix={<CloseCircleOutlined />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </StatsCard>
            </Col>
          </Row>
        </Spin>
      </Card>

      {/* 订单列表 */}
      <Card>
        <Title level={4}>订单列表</Title>
        <div style={{ textAlign: 'center', padding: '64px 0' }}>
          <Empty
            description="请前往订单页面查看详细订单信息"
          >
            <Button type="primary" onClick={() => navigate('/orders')}>
              查看订单
            </Button>
          </Empty>
        </div>
      </Card>
    </div>
  );
};

export default ProfileOrders;
