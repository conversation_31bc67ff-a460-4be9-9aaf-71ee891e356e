import React, { useState } from 'react';
import { Image, Space } from 'antd';
import { LeftOutlined, RightOutlined, ZoomInOutlined, ZoomOutOutlined, RotateLeftOutlined, RotateRightOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const PreviewContainer = styled.div`
  .ant-image-preview-operations {
    .ant-image-preview-operations-operation {
      margin: 0 8px;
    }
  }
`;

const ThumbnailContainer = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
  
  .thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
    
    &.active {
      border-color: #1890ff;
    }
    
    &:hover {
      border-color: #40a9ff;
    }
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
`;

const MainImageContainer = styled.div<{ width?: string | number; height?: string | number }>`
  position: relative;
  width: ${props => typeof props.width === 'number' ? `${props.width}px` : props.width || '100%'};
  height: ${props => typeof props.height === 'number' ? `${props.height}px` : props.height || 'auto'};
  
  .main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }
  
  .nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    z-index: 1;
    
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    
    &.prev {
      left: 12px;
    }
    
    &.next {
      right: 12px;
    }
  }
  
  .image-indicator {
    position: absolute;
    bottom: 12px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    z-index: 1;
  }
`;

interface ImagePreviewProps {
  images: string[];
  width?: string | number;
  height?: string | number;
  showThumbnails?: boolean;
  showNavigation?: boolean;
  showIndicator?: boolean;
  fallback?: string;
  className?: string;
  style?: React.CSSProperties;
  onImageChange?: (index: number, image: string) => void;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  images = [],
  width,
  height = 400,
  showThumbnails = true,
  showNavigation = true,
  showIndicator = true,
  fallback = '/images/placeholder.png',
  className,
  style,
  onImageChange
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [previewVisible, setPreviewVisible] = useState(false);

  // 如果没有图片，使用占位图
  const displayImages = images.length > 0 ? images : [fallback];
  const currentImage = displayImages[currentIndex] || fallback;

  // 切换到上一张图片
  const handlePrevious = () => {
    const newIndex = currentIndex > 0 ? currentIndex - 1 : displayImages.length - 1;
    setCurrentIndex(newIndex);
    onImageChange?.(newIndex, displayImages[newIndex]);
  };

  // 切换到下一张图片
  const handleNext = () => {
    const newIndex = currentIndex < displayImages.length - 1 ? currentIndex + 1 : 0;
    setCurrentIndex(newIndex);
    onImageChange?.(newIndex, displayImages[newIndex]);
  };

  // 点击缩略图切换
  const handleThumbnailClick = (index: number) => {
    setCurrentIndex(index);
    onImageChange?.(index, displayImages[index]);
  };

  // 键盘导航
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      handlePrevious();
    } else if (e.key === 'ArrowRight') {
      handleNext();
    }
  };

  return (
    <PreviewContainer className={className} style={style}>
      <MainImageContainer 
        width={width} 
        height={height}
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        <Image
          src={currentImage}
          alt={`图片 ${currentIndex + 1}`}
          className="main-image"
          preview={{
            visible: previewVisible,
            onVisibleChange: setPreviewVisible,
            current: currentIndex,
            onChange: (index) => {
              setCurrentIndex(index);
              onImageChange?.(index, displayImages[index]);
            },
            toolbarRender: (
              originalNode,
              info: {
                transform: { x: number; y: number; rotate: number; scale: number };
                actions: {
                  onFlipY: () => void;
                  onFlipX: () => void;
                  onRotateLeft: () => void;
                  onRotateRight: () => void;
                  onZoomOut: () => void;
                  onZoomIn: () => void;
                  onReset: () => void;
                };
              }
            ) => (
              <Space size={12} className="ant-image-preview-operations">
                <div
                  className="ant-image-preview-operations-operation"
                  onClick={info.actions.onZoomOut}
                >
                  <ZoomOutOutlined />
                </div>
                <div
                  className="ant-image-preview-operations-operation"
                  onClick={info.actions.onZoomIn}
                >
                  <ZoomInOutlined />
                </div>
                <div
                  className="ant-image-preview-operations-operation"
                  onClick={info.actions.onRotateLeft}
                >
                  <RotateLeftOutlined />
                </div>
                <div
                  className="ant-image-preview-operations-operation"
                  onClick={info.actions.onRotateRight}
                >
                  <RotateRightOutlined />
                </div>
                {originalNode}
              </Space>
            )
          }}
          fallback={fallback}
        />

        {/* 导航按钮 */}
        {showNavigation && displayImages.length > 1 && (
          <>
            <button className="nav-button prev" onClick={handlePrevious}>
              <LeftOutlined />
            </button>
            <button className="nav-button next" onClick={handleNext}>
              <RightOutlined />
            </button>
          </>
        )}

        {/* 图片指示器 */}
        {showIndicator && displayImages.length > 1 && (
          <div className="image-indicator">
            {currentIndex + 1} / {displayImages.length}
          </div>
        )}
      </MainImageContainer>

      {/* 缩略图 */}
      {showThumbnails && displayImages.length > 1 && (
        <ThumbnailContainer>
          {displayImages.map((image, index) => (
            <div
              key={index}
              className={`thumbnail ${index === currentIndex ? 'active' : ''}`}
              onClick={() => handleThumbnailClick(index)}
            >
              <img src={image} alt={`缩略图 ${index + 1}`} />
            </div>
          ))}
        </ThumbnailContainer>
      )}
    </PreviewContainer>
  );
};

export default ImagePreview;
