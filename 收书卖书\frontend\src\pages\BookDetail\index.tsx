import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Image,
  Typography,
  Button,
  Space,
  Tag,
  Divider,
  InputNumber,
  Breadcrumb,
  Spin,
  message,
  Descriptions,
  Rate,
  Avatar
} from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  ShareAltOutlined,
  EyeOutlined,
  UserOutlined,
  PhoneOutlined,
  WechatOutlined,
  QqOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { Book } from '../../types';
import { booksService } from '../../services/books';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';

const { Title, Text, Paragraph } = Typography;

const DetailContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const ImageGallery = styled.div`
  .main-image {
    width: 100%;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .thumbnail-list {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    flex-wrap: wrap;

    .thumbnail {
      width: 60px;
      height: 80px;
      border-radius: 4px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;

      &:hover, &.active {
        border-color: #1890ff;
      }
    }
  }
`;

const BookInfo = styled.div`
  .book-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #262626;
  }

  .book-author {
    font-size: 16px;
    color: #8c8c8c;
    margin-bottom: 16px;
  }

  .price-section {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    margin: 16px 0;

    .current-price {
      font-size: 28px;
      color: #f5222d;
      font-weight: bold;
    }

    .original-price {
      font-size: 16px;
      color: #999;
      text-decoration: line-through;
      margin-left: 12px;
    }

    .discount {
      background: #f5222d;
      color: white;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-left: 12px;
    }
  }

  .stock-info {
    margin: 16px 0;

    .stock-count {
      color: #52c41a;
      font-weight: 500;
    }

    &.low-stock .stock-count {
      color: #faad14;
    }

    &.out-of-stock .stock-count {
      color: #f5222d;
    }
  }
`;

const ActionSection = styled.div`
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 24px;

  .quantity-selector {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 16px 0;

    .quantity-label {
      font-weight: 500;
    }
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
  }
`;

const SellerInfo = styled(Card)`
  margin-top: 24px;

  .seller-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .seller-name {
      font-weight: 500;
      font-size: 16px;
    }

    .seller-rating {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .contact-methods {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
`;

const BookDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addItem } = useCartStore();
  const { isAuthenticated } = useAuthStore();

  const [book, setBook] = useState<Book | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    if (id) {
      loadBookDetail(id);
    }
  }, [id]);

  const loadBookDetail = async (bookId: string) => {
    try {
      setLoading(true);
      const response = await booksService.getBook(bookId);
      if (response.success && response.data) {
        setBook(response.data);
      } else {
        message.error('图书不存在');
        navigate('/books');
      }
    } catch (error) {
      console.error('加载图书详情失败:', error);
      message.error('加载图书详情失败');
      navigate('/books');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!book) return;

    addItem(book, quantity);
  };

  const handleBuyNow = () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!book) return;

    // 直接跳转到订单创建页面
    navigate('/orders/create', {
      state: {
        cartItems: [{
          book_id: book.id,
          book,
          quantity
        }]
      }
    });
  };

  const getConditionColor = (condition: string) => {
    const colorMap: Record<string, string> = {
      '全新': 'green',
      '九成新': 'blue',
      '八成新': 'orange',
      '七成新': 'gold',
      '六成新': 'red'
    };
    return colorMap[condition] || 'default';
  };

  const getStockStatus = (stock: number) => {
    if (stock === 0) return 'out-of-stock';
    if (stock <= 5) return 'low-stock';
    return 'normal';
  };

  const getStockText = (stock: number) => {
    if (stock === 0) return '暂无库存';
    if (stock <= 5) return `仅剩 ${stock} 本`;
    return `库存充足 (${stock} 本)`;
  };

  const calculateDiscount = (originalPrice?: number, currentPrice?: number) => {
    if (!originalPrice || !currentPrice || originalPrice <= currentPrice) return null;
    return Math.round((1 - currentPrice / originalPrice) * 100);
  };

  if (loading) {
    return (
      <DetailContainer>
        <div style={{ textAlign: 'center', padding: '64px 0' }}>
          <Spin size="large" />
        </div>
      </DetailContainer>
    );
  }

  if (!book) {
    return (
      <DetailContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: '64px 0' }}>
            <Title level={3}>图书不存在</Title>
            <Button type="primary" onClick={() => navigate('/books')}>
              返回图书列表
            </Button>
          </div>
        </Card>
      </DetailContainer>
    );
  }

  const images = book.images && book.images.length > 0
    ? book.images
    : [book.cover_image || '/images/book-placeholder.png'];

  const discount = calculateDiscount(book.original_price, book.price);

  return (
    <DetailContainer>
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: 24 }}>
        <Breadcrumb.Item>
          <Button
            type="link"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
            style={{ padding: 0 }}
          >
            返回
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Button type="link" onClick={() => navigate('/books')} style={{ padding: 0 }}>
            图书市场
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          {book.category?.name}
        </Breadcrumb.Item>
        <Breadcrumb.Item>{book.title}</Breadcrumb.Item>
      </Breadcrumb>

      <Row gutter={[24, 24]}>
        {/* 左侧：图片展示 */}
        <Col xs={24} md={10}>
          <Card>
            <ImageGallery>
              <Image
                className="main-image"
                src={images[selectedImage]}
                alt={book.title}
                fallback="/images/book-placeholder.png"
              />
              {images.length > 1 && (
                <div className="thumbnail-list">
                  {images.map((image, index) => (
                    <Image
                      key={index}
                      className={`thumbnail ${index === selectedImage ? 'active' : ''}`}
                      src={image}
                      alt={`${book.title} ${index + 1}`}
                      fallback="/images/book-placeholder.png"
                      preview={false}
                      onClick={() => setSelectedImage(index)}
                    />
                  ))}
                </div>
              )}
            </ImageGallery>
          </Card>
        </Col>

        {/* 中间：图书信息 */}
        <Col xs={24} md={8}>
          <BookInfo>
            <div className="book-title">{book.title}</div>
            <div className="book-author">作者：{book.author || '未知'}</div>

            <Space wrap>
              <Tag color={getConditionColor(book.condition)}>{book.condition}</Tag>
              <Tag color={book.status === '上架' ? 'success' : 'default'}>{book.status}</Tag>
              <Space>
                <EyeOutlined />
                <Text type="secondary">{book.views} 次浏览</Text>
              </Space>
            </Space>

            <div className="price-section">
              <Space align="baseline">
                <span className="current-price">¥{book.price}</span>
                {book.original_price && book.original_price > book.price && (
                  <>
                    <span className="original-price">¥{book.original_price}</span>
                    {discount && (
                      <span className="discount">{discount}折</span>
                    )}
                  </>
                )}
              </Space>
            </div>

            <div className={`stock-info ${getStockStatus(book.stock)}`}>
              <Text>库存状态：</Text>
              <span className="stock-count">{getStockText(book.stock)}</span>
            </div>

            <Descriptions column={1} size="small" style={{ marginTop: 16 }}>
              <Descriptions.Item label="ISBN">{book.isbn || '暂无'}</Descriptions.Item>
              <Descriptions.Item label="出版社">{book.publisher || '暂无'}</Descriptions.Item>
              <Descriptions.Item label="出版日期">{book.publication_date || '暂无'}</Descriptions.Item>
              <Descriptions.Item label="分类">{book.category?.name}</Descriptions.Item>
            </Descriptions>

            {book.description && (
              <>
                <Divider />
                <div>
                  <Title level={5}>图书简介</Title>
                  <Paragraph>{book.description}</Paragraph>
                </div>
              </>
            )}
          </BookInfo>
        </Col>

        {/* 右侧：购买操作 */}
        <Col xs={24} md={6}>
          <ActionSection>
            <Title level={4}>购买选项</Title>

            {book.status === '上架' && book.stock > 0 ? (
              <>
                <div className="quantity-selector">
                  <span className="quantity-label">数量：</span>
                  <InputNumber
                    min={1}
                    max={book.stock}
                    value={quantity}
                    onChange={(value) => setQuantity(value || 1)}
                  />
                </div>

                <div className="action-buttons">
                  <Button
                    type="primary"
                    size="large"
                    icon={<ShoppingCartOutlined />}
                    onClick={handleAddToCart}
                    block
                  >
                    加入购物车
                  </Button>
                  <Button
                    size="large"
                    onClick={handleBuyNow}
                    block
                  >
                    立即购买
                  </Button>
                </div>
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '24px 0' }}>
                <Text type="secondary">
                  {book.status === '下架' ? '商品已下架' : '暂无库存'}
                </Text>
              </div>
            )}

            <Divider />

            <Space direction="vertical" style={{ width: '100%' }}>
              <Button icon={<HeartOutlined />} block>
                收藏
              </Button>
              <Button icon={<ShareAltOutlined />} block>
                分享
              </Button>
            </Space>
          </ActionSection>

          {/* 卖家信息 */}
          {book.creator && (
            <SellerInfo title="卖家信息" size="small">
              <div className="seller-header">
                <Avatar
                  src={book.creator.avatar}
                  icon={<UserOutlined />}
                  size="large"
                />
                <div>
                  <div className="seller-name">
                    {book.creator.username || book.creator.phone}
                  </div>
                  <div className="seller-rating">
                    <Rate disabled defaultValue={5} style={{ fontSize: '12px' }} />
                    <Text type="secondary">(100%好评)</Text>
                  </div>
                </div>
              </div>

              <div className="contact-methods">
                {book.creator.contact_wechat && (
                  <Button size="small" icon={<WechatOutlined />}>
                    微信
                  </Button>
                )}
                {book.creator.contact_qq && (
                  <Button size="small" icon={<QqOutlined />}>
                    QQ
                  </Button>
                )}
                {book.creator.contact_phone_public && (
                  <Button size="small" icon={<PhoneOutlined />}>
                    电话
                  </Button>
                )}
              </div>
            </SellerInfo>
          )}
        </Col>
      </Row>
    </DetailContainer>
  );
};

export default BookDetail;
