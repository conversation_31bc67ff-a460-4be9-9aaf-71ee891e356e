const sequelize = require('../config/database');
const User = require('./User');
const Category = require('./Category');
const Book = require('./Book');
const { Order, OrderItem } = require('./Order');
const Message = require('./Message');
const Favorite = require('./Favorite');

// 定义关联关系

// User 关联
User.hasMany(Book, { foreignKey: 'created_by', as: 'createdBooks' });
User.hasMany(Book, { foreignKey: 'updated_by', as: 'updatedBooks' });
User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' });
User.hasMany(Message, { foreignKey: 'sender_id', as: 'sentMessages' });
User.hasMany(Message, { foreignKey: 'receiver_id', as: 'receivedMessages' });
User.hasMany(Favorite, { foreignKey: 'user_id', as: 'favorites' });

// Category 关联
Category.hasMany(Book, { foreignKey: 'category_id', as: 'books' });

// Book 关联
Book.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
Book.belongsTo(User, { foreignKey: 'updated_by', as: 'updater' });
Book.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });
Book.hasMany(OrderItem, { foreignKey: 'book_id', as: 'orderItems' });
Book.hasMany(Message, { foreignKey: 'book_id', as: 'messages' });
Book.hasMany(Favorite, { foreignKey: 'book_id', as: 'favorites' });

// Order 关联
Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });
Order.hasMany(Message, { foreignKey: 'order_id', as: 'messages' });

// OrderItem 关联
OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
OrderItem.belongsTo(Book, { foreignKey: 'book_id', as: 'book' });

// Message 关联
Message.belongsTo(User, { foreignKey: 'sender_id', as: 'sender' });
Message.belongsTo(User, { foreignKey: 'receiver_id', as: 'receiver' });
Message.belongsTo(Book, { foreignKey: 'book_id', as: 'book' });
Message.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

// Favorite 关联
Favorite.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Favorite.belongsTo(Book, { foreignKey: 'book_id', as: 'book' });

module.exports = {
  sequelize,
  User,
  Category,
  Book,
  Order,
  OrderItem,
  Message,
  Favorite
};
