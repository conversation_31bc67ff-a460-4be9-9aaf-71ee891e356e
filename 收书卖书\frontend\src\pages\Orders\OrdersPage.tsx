import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  List,
  Button,
  Space,
  Typography,
  Tag,
  Image,
  Rate,
  Modal,
  Input,
  message,
  Empty,
  Spin,
  Breadcrumb,
  Steps,
  Timeline,
  Divider
} from 'antd';
import {
  HomeOutlined,
  ShoppingOutlined,
  EyeOutlined,
  MessageOutlined,
  StarOutlined,
  TruckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { ordersService } from '../../services/orders';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Step } = Steps;

const OrdersContainer = styled.div`
  min-height: 100vh;
  background: #f5f5f5;
  
  .page-header {
    background: white;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .breadcrumb {
        margin-bottom: 8px;
      }
      
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 12px;
        
        .orders-icon {
          color: #1677ff;
        }
      }
    }
  }
  
  .orders-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    
    .orders-tabs {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .ant-tabs-nav {
        padding: 0 24px;
        margin: 0;
        
        .ant-tabs-tab {
          padding: 16px 24px;
          font-size: 16px;
          font-weight: 600;
        }
      }
      
      .ant-tabs-content {
        padding: 0 24px 24px 24px;
      }
    }
    
    .order-item {
      border: 1px solid #f0f0f0;
      border-radius: 12px;
      margin-bottom: 16px;
      background: white;
      overflow: hidden;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .order-header {
        padding: 16px 20px;
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .order-info {
          display: flex;
          align-items: center;
          gap: 16px;
          
          .order-number {
            font-weight: 600;
            color: #262626;
          }
          
          .order-date {
            color: #8c8c8c;
            font-size: 14px;
          }
        }
        
        .order-status {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .status-tag {
            font-size: 12px;
          }
        }
      }
      
      .order-content {
        padding: 20px;
        
        .order-books {
          margin-bottom: 16px;
          
          .book-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            
            &:last-child {
              border-bottom: none;
            }
            
            .book-image {
              width: 80px;
              height: 100px;
              border-radius: 6px;
              overflow: hidden;
              flex-shrink: 0;
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            
            .book-info {
              flex: 1;
              
              .book-title {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 8px;
                line-height: 1.4;
                cursor: pointer;
                
                &:hover {
                  color: #1677ff;
                }
              }
              
              .book-meta {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 8px;
                
                .meta-item {
                  font-size: 13px;
                  color: #8c8c8c;
                }
              }
              
              .book-price {
                display: flex;
                align-items: center;
                justify-content: space-between;
                
                .price-info {
                  .current-price {
                    font-size: 16px;
                    font-weight: 700;
                    color: #ff4d4f;
                  }
                  
                  .quantity {
                    color: #8c8c8c;
                    font-size: 14px;
                    margin-left: 8px;
                  }
                }
              }
            }
          }
        }
        
        .order-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;
          
          .order-total {
            .total-label {
              color: #8c8c8c;
              margin-right: 8px;
            }
            
            .total-amount {
              font-size: 18px;
              font-weight: 700;
              color: #ff4d4f;
            }
          }
          
          .order-actions {
            display: flex;
            gap: 8px;
            
            .action-btn {
              border-radius: 6px;
              height: 32px;
              padding: 0 16px;
              font-size: 13px;
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      
      .empty-icon {
        font-size: 64px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }
      
      .empty-title {
        font-size: 18px;
        color: #595959;
        margin-bottom: 8px;
      }
      
      .empty-desc {
        color: #8c8c8c;
        margin-bottom: 24px;
      }
    }
  }
  
  .order-detail-modal {
    .order-timeline {
      margin: 24px 0;
    }
    
    .shipping-info {
      background: #f6f9ff;
      padding: 16px;
      border-radius: 8px;
      margin: 16px 0;
      
      .shipping-title {
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .shipping-detail {
        font-size: 14px;
        color: #595959;
        line-height: 1.6;
      }
    }
  }
  
  .review-modal {
    .review-form {
      .book-info {
        display: flex;
        gap: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        margin-bottom: 20px;
        
        .book-image {
          width: 80px;
          height: 100px;
          border-radius: 6px;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .book-details {
          flex: 1;
          
          .book-title {
            font-weight: 600;
            margin-bottom: 8px;
          }
          
          .book-author {
            color: #8c8c8c;
            font-size: 14px;
          }
        }
      }
      
      .rating-section {
        margin-bottom: 20px;
        
        .rating-label {
          margin-bottom: 8px;
          font-weight: 600;
        }
        
        .rating-input {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .rating-text {
            color: #8c8c8c;
            font-size: 14px;
          }
        }
      }
      
      .comment-section {
        .comment-label {
          margin-bottom: 8px;
          font-weight: 600;
        }
      }
    }
  }
`;

interface OrdersPageProps {}

const OrdersPage: React.FC<OrdersPageProps> = () => {
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const [detailVisible, setDetailVisible] = useState(false);
  const [reviewVisible, setReviewVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [selectedBook, setSelectedBook] = useState<any>(null);
  const [reviewForm, setReviewForm] = useState({
    rating: 5,
    comment: ''
  });

  useEffect(() => {
    loadOrders();
  }, [activeTab]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      
      const params: any = {};
      if (activeTab !== 'all') {
        params.status = activeTab;
      }
      
      const response = await ordersService.getOrders(params);
      
      if (response.success) {
        setOrders(response.data.orders);
      }
    } catch (error) {
      message.error('加载订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetail = (order: any) => {
    setSelectedOrder(order);
    setDetailVisible(true);
  };

  const handleCancelOrder = async (orderId: string) => {
    Modal.confirm({
      title: '确认取消订单',
      content: '取消后订单无法恢复，确定要取消吗？',
      onOk: async () => {
        try {
          await ordersService.cancelOrder(orderId);
          message.success('订单已取消');
          loadOrders();
        } catch (error) {
          message.error('取消订单失败');
        }
      }
    });
  };

  const handleConfirmReceived = async (orderId: string) => {
    Modal.confirm({
      title: '确认收货',
      content: '确认已收到商品并验收无误？',
      onOk: async () => {
        try {
          await ordersService.confirmOrder(orderId);
          message.success('确认收货成功');
          loadOrders();
        } catch (error) {
          message.error('确认收货失败');
        }
      }
    });
  };

  const handleReview = (order: any, book: any) => {
    setSelectedOrder(order);
    setSelectedBook(book);
    setReviewVisible(true);
  };

  const handleSubmitReview = async () => {
    try {
      // 提交评价逻辑
      message.success('评价提交成功');
      setReviewVisible(false);
      setReviewForm({ rating: 5, comment: '' });
      loadOrders();
    } catch (error) {
      message.error('评价提交失败');
    }
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: 'orange',
      paid: 'blue',
      delivering: 'cyan',
      delivered: 'green',
      completed: 'green',
      cancelled: 'red'
    };
    return colorMap[status] || 'default';
  };

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: '待支付',
      paid: '已支付',
      delivering: '配送中',
      delivered: '已送达',
      completed: '已完成',
      cancelled: '已取消'
    };
    return textMap[status] || status;
  };

  const getStatusIcon = (status: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      pending: <ClockCircleOutlined />,
      paid: <CheckCircleOutlined />,
      delivering: <TruckOutlined />,
      delivered: <CheckCircleOutlined />,
      completed: <CheckCircleOutlined />,
      cancelled: <ExclamationCircleOutlined />
    };
    return iconMap[status] || <ClockCircleOutlined />;
  };

  const renderOrderActions = (order: any) => {
    const actions = [];
    
    switch (order.status) {
      case 'pending':
        actions.push(
          <Button key="pay" type="primary" size="small" className="action-btn">
            立即支付
          </Button>
        );
        actions.push(
          <Button 
            key="cancel" 
            size="small" 
            className="action-btn"
            onClick={() => handleCancelOrder(order.id)}
          >
            取消订单
          </Button>
        );
        break;
      case 'paid':
        actions.push(
          <Button 
            key="cancel" 
            size="small" 
            className="action-btn"
            onClick={() => handleCancelOrder(order.id)}
          >
            取消订单
          </Button>
        );
        break;
      case 'delivering':
        actions.push(
          <Button 
            key="confirm" 
            type="primary" 
            size="small" 
            className="action-btn"
            onClick={() => handleConfirmReceived(order.id)}
          >
            确认收货
          </Button>
        );
        break;
      case 'delivered':
        actions.push(
          <Button 
            key="confirm" 
            type="primary" 
            size="small" 
            className="action-btn"
            onClick={() => handleConfirmReceived(order.id)}
          >
            确认收货
          </Button>
        );
        break;
      case 'completed':
        order.items?.forEach((item: any) => {
          if (!item.reviewed) {
            actions.push(
              <Button 
                key={`review-${item.id}`}
                size="small" 
                className="action-btn"
                icon={<StarOutlined />}
                onClick={() => handleReview(order, item)}
              >
                评价
              </Button>
            );
          }
        });
        break;
    }
    
    actions.push(
      <Button 
        key="detail" 
        size="small" 
        className="action-btn"
        icon={<EyeOutlined />}
        onClick={() => handleViewDetail(order)}
      >
        查看详情
      </Button>
    );
    
    return actions;
  };

  const tabs = [
    { key: 'all', label: '全部订单' },
    { key: 'pending', label: '待支付' },
    { key: 'paid', label: '已支付' },
    { key: 'delivering', label: '配送中' },
    { key: 'completed', label: '已完成' },
    { key: 'cancelled', label: '已取消' }
  ];

  return (
    <OrdersContainer>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <HomeOutlined />
              <span onClick={() => navigate('/')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                首页
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <ShoppingOutlined />
              我的订单
            </Breadcrumb.Item>
          </Breadcrumb>
          
          <Title level={2} className="page-title">
            <ShoppingOutlined className="orders-icon" />
            我的订单
          </Title>
        </div>
      </div>

      <div className="orders-content">
        <Card className="orders-tabs">
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            {tabs.map(tab => (
              <TabPane tab={tab.label} key={tab.key}>
                <Spin spinning={loading}>
                  {orders.length > 0 ? (
                    <div>
                      {orders.map(order => (
                        <div key={order.id} className="order-item">
                          <div className="order-header">
                            <div className="order-info">
                              <span className="order-number">订单号: {order.order_number}</span>
                              <span className="order-date">{order.created_at}</span>
                            </div>
                            <div className="order-status">
                              {getStatusIcon(order.status)}
                              <Tag color={getStatusColor(order.status)} className="status-tag">
                                {getStatusText(order.status)}
                              </Tag>
                            </div>
                          </div>
                          
                          <div className="order-content">
                            <div className="order-books">
                              {order.items?.map((item: any) => (
                                <div key={item.id} className="book-item">
                                  <div className="book-image">
                                    <img 
                                      src={item.book?.cover_image || '/images/book-placeholder.png'} 
                                      alt={item.book?.title} 
                                    />
                                  </div>
                                  <div className="book-info">
                                    <div 
                                      className="book-title"
                                      onClick={() => navigate(`/books/${item.book?.id}`)}
                                    >
                                      {item.book?.title}
                                    </div>
                                    <div className="book-meta">
                                      <span className="meta-item">作者: {item.book?.author || '未知'}</span>
                                      <Tag size="small" color="green">现货</Tag>
                                    </div>
                                    <div className="book-price">
                                      <div className="price-info">
                                        <span className="current-price">¥{item.price?.toFixed(2)}</span>
                                        <span className="quantity">x{item.quantity}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                            
                            <div className="order-footer">
                              <div className="order-total">
                                <span className="total-label">实付金额:</span>
                                <span className="total-amount">¥{order.total_amount?.toFixed(2)}</span>
                              </div>
                              <div className="order-actions">
                                {renderOrderActions(order)}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="empty-state">
                      <ShoppingOutlined className="empty-icon" />
                      <div className="empty-title">暂无订单</div>
                      <div className="empty-desc">您还没有任何订单，快去挑选心仪的图书吧</div>
                      <Button type="primary" onClick={() => navigate('/books')}>
                        去逛逛
                      </Button>
                    </div>
                  )}
                </Spin>
              </TabPane>
            ))}
          </Tabs>
        </Card>
      </div>

      {/* 订单详情弹窗 */}
      <Modal
        title="订单详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
        className="order-detail-modal"
      >
        {selectedOrder && (
          <div>
            <div style={{ marginBottom: 24 }}>
              <Text strong>订单号: </Text>
              <Text>{selectedOrder.order_number}</Text>
              <Divider type="vertical" />
              <Text strong>订单状态: </Text>
              <Tag color={getStatusColor(selectedOrder.status)}>
                {getStatusText(selectedOrder.status)}
              </Tag>
            </div>

            {/* 物流信息 */}
            {selectedOrder.status === 'delivering' && (
              <div className="shipping-info">
                <div className="shipping-title">物流信息</div>
                <div className="shipping-detail">
                  快递公司: 顺丰速运<br />
                  快递单号: SF1234567890<br />
                  当前状态: 运输中，预计明天送达
                </div>
              </div>
            )}

            {/* 订单时间线 */}
            <div className="order-timeline">
              <Timeline>
                <Timeline.Item color="green">
                  <Text strong>订单创建</Text>
                  <br />
                  <Text type="secondary">{selectedOrder.created_at}</Text>
                </Timeline.Item>
                {selectedOrder.status !== 'pending' && (
                  <Timeline.Item color="blue">
                    <Text strong>支付完成</Text>
                    <br />
                    <Text type="secondary">{selectedOrder.paid_at || selectedOrder.created_at}</Text>
                  </Timeline.Item>
                )}
                {['delivering', 'delivered', 'completed'].includes(selectedOrder.status) && (
                  <Timeline.Item color="orange">
                    <Text strong>商品发货</Text>
                    <br />
                    <Text type="secondary">{selectedOrder.shipped_at || selectedOrder.created_at}</Text>
                  </Timeline.Item>
                )}
                {['delivered', 'completed'].includes(selectedOrder.status) && (
                  <Timeline.Item color="green">
                    <Text strong>确认收货</Text>
                    <br />
                    <Text type="secondary">{selectedOrder.delivered_at || selectedOrder.created_at}</Text>
                  </Timeline.Item>
                )}
              </Timeline>
            </div>

            {/* 商品列表 */}
            <List
              dataSource={selectedOrder.items}
              renderItem={(item: any) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Image
                        width={60}
                        height={75}
                        src={item.book?.cover_image || '/images/book-placeholder.png'}
                        style={{ borderRadius: 4 }}
                      />
                    }
                    title={item.book?.title}
                    description={`作者: ${item.book?.author || '未知'} | 数量: ${item.quantity}`}
                  />
                  <div>¥{item.price?.toFixed(2)}</div>
                </List.Item>
              )}
            />
          </div>
        )}
      </Modal>

      {/* 评价弹窗 */}
      <Modal
        title="评价图书"
        open={reviewVisible}
        onCancel={() => setReviewVisible(false)}
        onOk={handleSubmitReview}
        className="review-modal"
      >
        {selectedBook && (
          <div className="review-form">
            <div className="book-info">
              <div className="book-image">
                <img 
                  src={selectedBook.book?.cover_image || '/images/book-placeholder.png'} 
                  alt={selectedBook.book?.title} 
                />
              </div>
              <div className="book-details">
                <div className="book-title">{selectedBook.book?.title}</div>
                <div className="book-author">作者: {selectedBook.book?.author || '未知'}</div>
              </div>
            </div>
            
            <div className="rating-section">
              <div className="rating-label">评分:</div>
              <div className="rating-input">
                <Rate 
                  value={reviewForm.rating} 
                  onChange={(value) => setReviewForm(prev => ({ ...prev, rating: value }))}
                />
                <span className="rating-text">
                  {reviewForm.rating === 5 ? '非常满意' : 
                   reviewForm.rating === 4 ? '满意' :
                   reviewForm.rating === 3 ? '一般' :
                   reviewForm.rating === 2 ? '不满意' : '非常不满意'}
                </span>
              </div>
            </div>
            
            <div className="comment-section">
              <div className="comment-label">评价内容:</div>
              <TextArea
                rows={4}
                placeholder="分享您的购买体验，帮助其他用户更好地了解这本书..."
                value={reviewForm.comment}
                onChange={(e) => setReviewForm(prev => ({ ...prev, comment: e.target.value }))}
              />
            </div>
          </div>
        )}
      </Modal>
    </OrdersContainer>
  );
};

export default OrdersPage;
