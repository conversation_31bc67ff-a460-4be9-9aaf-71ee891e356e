import React, { useEffect, Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { useAuthStore } from './stores/authStore';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/ui/ProtectedRoute';
import ErrorBoundary from './components/ui/ErrorBoundary';

// 懒加载页面组件
const Home = lazy(() => import('./pages/Home/index'));
const BookList = lazy(() => import('./pages/BookList/index'));
const BookDetail = lazy(() => import('./pages/BookDetail/index'));
const Cart = lazy(() => import('./pages/Cart/index'));
const Order = lazy(() => import('./pages/Order/index'));
const Profile = lazy(() => import('./pages/Profile/index'));
const Login = lazy(() => import('./pages/Auth/Login'));
const Register = lazy(() => import('./pages/Auth/Register'));
const AdminDashboard = lazy(() => import('./pages/Admin/Dashboard'));

// 设置dayjs中文
dayjs.locale('zh-cn');

// 加载组件
const LoadingComponent: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px'
  }}>
    <Spin size="large" />
  </div>
);

function App() {
  const { checkAuth } = useAuthStore();

  useEffect(() => {
    // 应用启动时检查认证状态
    checkAuth();
  }, [checkAuth]);

  return (
    <ErrorBoundary>
      <ConfigProvider locale={zhCN}>
        <AntdApp>
          <Router>
            <Suspense fallback={<LoadingComponent />}>
              <Routes>
              {/* 公开路由 */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* 主应用布局 */}
              <Route path="/" element={<Layout />}>
                <Route index element={<Home />} />
                <Route path="books" element={<BookList />} />
                <Route path="books/:id" element={<BookDetail />} />

                {/* 需要登录的路由 */}
                <Route path="cart" element={
                  <ProtectedRoute>
                    <Cart />
                  </ProtectedRoute>
                } />
                <Route path="orders/*" element={
                  <ProtectedRoute>
                    <Order />
                  </ProtectedRoute>
                } />
                <Route path="profile/*" element={
                  <ProtectedRoute>
                    <Profile />
                  </ProtectedRoute>
                } />

                {/* 管理员路由 */}
                <Route path="admin/*" element={
                  <ProtectedRoute requireAdmin>
                    <AdminDashboard />
                  </ProtectedRoute>
                } />
              </Route>

              {/* 404重定向 */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Router>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
}

export default App;
