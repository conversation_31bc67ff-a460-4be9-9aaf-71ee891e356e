import React, { useEffect, Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { useAuthStore } from './stores/authStore';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/ui/ProtectedRoute';
import ErrorBoundary from './components/ui/ErrorBoundary';

// 懒加载页面组件
const HomePage = lazy(() => import('./pages/Home/HomePage'));
const BooksListPage = lazy(() => import('./pages/Books/BooksListPage'));
const BookDetailPage = lazy(() => import('./pages/Books/BookDetailPage'));
const CartPage = lazy(() => import('./pages/Cart/CartPage'));
const CheckoutPage = lazy(() => import('./pages/Checkout/CheckoutPage'));
const OrdersPage = lazy(() => import('./pages/Orders/OrdersPage'));
const AuthPage = lazy(() => import('./pages/Auth/AuthPage'));
const UserProfile = lazy(() => import('./components/business/UserProfile'));
const AdminDashboard = lazy(() => import('./pages/Admin/Dashboard'));

// 设置dayjs中文
dayjs.locale('zh-cn');

// 加载组件
const LoadingComponent: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px'
  }}>
    <Spin size="large" />
  </div>
);

function App() {
  const { checkAuth } = useAuthStore();

  useEffect(() => {
    // 应用启动时检查认证状态
    checkAuth();
  }, [checkAuth]);

  return (
    <ErrorBoundary>
      <ConfigProvider locale={zhCN}>
        <AntdApp>
          <Router>
            <Suspense fallback={<LoadingComponent />}>
              <Routes>
              {/* 公开路由 */}
              <Route path="/auth" element={<AuthPage />} />
              <Route path="/login" element={<AuthPage />} />
              <Route path="/register" element={<AuthPage />} />

              {/* 主应用布局 */}
              <Route path="/" element={<Layout />}>
                <Route index element={<HomePage />} />
                <Route path="books" element={<BooksListPage />} />
                <Route path="books/:id" element={<BookDetailPage />} />

                {/* 需要登录的路由 */}
                <Route path="cart" element={
                  <ProtectedRoute>
                    <CartPage />
                  </ProtectedRoute>
                } />
                <Route path="checkout" element={
                  <ProtectedRoute>
                    <CheckoutPage />
                  </ProtectedRoute>
                } />
                <Route path="orders" element={
                  <ProtectedRoute>
                    <OrdersPage />
                  </ProtectedRoute>
                } />
                <Route path="orders/:id" element={
                  <ProtectedRoute>
                    <OrdersPage />
                  </ProtectedRoute>
                } />
                <Route path="profile" element={
                  <ProtectedRoute>
                    <UserProfile />
                  </ProtectedRoute>
                } />

                {/* 管理员路由 */}
                <Route path="admin/*" element={
                  <ProtectedRoute requireAdmin>
                    <AdminDashboard />
                  </ProtectedRoute>
                } />
              </Route>

              {/* 404重定向 */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            </Suspense>
          </Router>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
}

export default App;
