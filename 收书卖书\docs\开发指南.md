# 收书卖书平台 - 开发指南

## 项目概述

收书卖书平台是一个基于现代Web技术栈构建的二手图书交易平台，提供完整的图书买卖、用户管理、订单处理等功能。

### 技术栈

**前端:**
- React 18 + TypeScript
- Ant Design 5.x
- React Router 6
- Zustand (状态管理)
- Styled Components
- Axios

**后端:**
- Node.js + Express
- PostgreSQL
- Sequelize ORM
- JWT认证
- Redis (缓存)
- Multer (文件上传)

**部署:**
- Docker + Docker Compose
- Nginx (反向代理)
- PM2 (进程管理)

## 开发环境搭建

### 系统要求

- Node.js >= 16.0.0
- PostgreSQL >= 13.0
- Redis >= 6.0
- Docker >= 20.0 (可选)

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd 收书卖书
```

2. **安装依赖**
```bash
# 后端依赖
cd backend
npm install

# 前端依赖
cd ../frontend
npm install
```

3. **环境配置**
```bash
# 后端环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件

# 前端环境变量
cp frontend/.env.example frontend/.env
# 编辑 frontend/.env 文件
```

4. **数据库初始化**
```bash
cd backend
npm run db:init
npm run db:seed  # 可选：导入测试数据
```

5. **启动开发服务器**
```bash
# 启动后端 (终端1)
cd backend
npm run dev

# 启动前端 (终端2)
cd frontend
npm start
```

6. **访问应用**
- 前端: http://localhost:3000
- 后端: http://localhost:3001
- API文档: http://localhost:3001/api-docs

## 项目结构

### 后端结构
```
backend/
├── src/
│   ├── config/          # 配置文件
│   │   ├── database.js  # 数据库配置
│   │   ├── redis.js     # Redis配置
│   │   └── upload.js    # 文件上传配置
│   ├── models/          # 数据模型
│   │   ├── User.js      # 用户模型
│   │   ├── Book.js      # 图书模型
│   │   ├── Order.js     # 订单模型
│   │   └── index.js     # 模型关联
│   ├── routes/          # 路由控制器
│   │   ├── auth.js      # 认证路由
│   │   ├── books.js     # 图书路由
│   │   ├── orders.js    # 订单路由
│   │   └── ...
│   ├── middleware/      # 中间件
│   │   ├── auth.js      # 认证中间件
│   │   ├── validation.js # 数据验证
│   │   └── errorHandler.js # 错误处理
│   ├── services/        # 业务逻辑
│   │   ├── emailService.js
│   │   ├── paymentService.js
│   │   └── recommendationService.js
│   ├── utils/           # 工具函数
│   │   ├── logger.js    # 日志工具
│   │   ├── helpers.js   # 辅助函数
│   │   └── constants.js # 常量定义
│   ├── database/        # 数据库相关
│   │   ├── migrations/  # 数据库迁移
│   │   ├── seeders/     # 种子数据
│   │   └── init.sql     # 初始化脚本
│   └── app.js           # 应用入口
├── uploads/             # 上传文件目录
├── logs/                # 日志文件
├── tests/               # 测试文件
├── package.json
└── Dockerfile
```

### 前端结构
```
frontend/
├── public/              # 静态资源
├── src/
│   ├── components/      # 组件
│   │   ├── ui/          # 通用UI组件
│   │   ├── business/    # 业务组件
│   │   └── layout/      # 布局组件
│   ├── pages/           # 页面组件
│   │   ├── Home/        # 首页
│   │   ├── Books/       # 图书页面
│   │   ├── Cart/        # 购物车
│   │   └── Profile/     # 个人中心
│   ├── stores/          # 状态管理
│   │   ├── authStore.ts # 认证状态
│   │   ├── cartStore.ts # 购物车状态
│   │   └── ...
│   ├── services/        # API服务
│   │   ├── api.ts       # API基础配置
│   │   ├── auth.ts      # 认证服务
│   │   ├── books.ts     # 图书服务
│   │   └── ...
│   ├── types/           # TypeScript类型定义
│   ├── utils/           # 工具函数
│   ├── styles/          # 样式文件
│   │   ├── theme.ts     # 主题配置
│   │   ├── global.css   # 全局样式
│   │   └── mobile.css   # 移动端样式
│   ├── hooks/           # 自定义Hooks
│   ├── constants/       # 常量定义
│   └── App.tsx          # 应用入口
├── package.json
└── Dockerfile
```

## 开发规范

### 代码规范

1. **命名规范**
   - 文件名: PascalCase (组件) / camelCase (工具)
   - 变量名: camelCase
   - 常量名: UPPER_SNAKE_CASE
   - 类名: PascalCase
   - 函数名: camelCase

2. **TypeScript规范**
   - 所有组件必须有类型定义
   - 使用接口定义数据结构
   - 避免使用 `any` 类型
   - 优先使用类型推断

3. **React规范**
   - 使用函数组件 + Hooks
   - 组件props必须有TypeScript接口
   - 使用memo优化性能
   - 合理使用useCallback和useMemo

4. **样式规范**
   - 使用Styled Components
   - 遵循BEM命名规范
   - 响应式设计优先
   - 使用设计系统变量

### Git规范

1. **分支命名**
   - `feature/功能名称`: 新功能开发
   - `bugfix/问题描述`: Bug修复
   - `hotfix/紧急修复`: 紧急修复
   - `refactor/重构内容`: 代码重构

2. **提交信息**
   ```
   type(scope): description
   
   [optional body]
   
   [optional footer]
   ```
   
   类型说明:
   - `feat`: 新功能
   - `fix`: Bug修复
   - `docs`: 文档更新
   - `style`: 代码格式调整
   - `refactor`: 代码重构
   - `test`: 测试相关
   - `chore`: 构建过程或辅助工具的变动

### API设计规范

1. **RESTful设计**
   - 使用标准HTTP方法
   - 资源导向的URL设计
   - 统一的响应格式
   - 合理的状态码使用

2. **数据验证**
   - 输入数据验证
   - 输出数据格式化
   - 错误信息标准化

3. **安全规范**
   - JWT Token认证
   - 密码加密存储
   - SQL注入防护
   - XSS攻击防护

## 数据库设计

### 核心表结构

1. **用户表 (users)**
   - 用户基本信息
   - 认证信息
   - 联系方式

2. **图书表 (books)**
   - 图书基本信息
   - 价格和库存
   - 状态管理

3. **订单表 (orders)**
   - 订单基本信息
   - 配送信息
   - 状态跟踪

4. **订单项表 (order_items)**
   - 订单商品详情
   - 数量和价格

### 数据库操作

1. **迁移管理**
```bash
# 创建迁移
npm run migration:create -- --name create_table_name

# 运行迁移
npm run migration:up

# 回滚迁移
npm run migration:down
```

2. **种子数据**
```bash
# 创建种子文件
npm run seed:create -- --name seed_name

# 运行种子
npm run seed:up

# 回滚种子
npm run seed:down
```

## 测试指南

### 单元测试

1. **后端测试**
```bash
cd backend
npm test                # 运行所有测试
npm run test:watch     # 监听模式
npm run test:coverage  # 覆盖率报告
```

2. **前端测试**
```bash
cd frontend
npm test               # 运行测试
npm run test:coverage  # 覆盖率报告
```

### 集成测试

1. **API测试**
   - 使用Jest + Supertest
   - 测试所有API端点
   - 验证数据格式和状态码

2. **E2E测试**
   - 使用Cypress
   - 测试关键用户流程
   - 跨浏览器兼容性测试

## 部署指南

### Docker部署

1. **构建镜像**
```bash
# 构建所有服务
docker-compose build

# 构建特定服务
docker-compose build backend
```

2. **启动服务**
```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

### 生产部署

1. **环境准备**
   - 配置生产环境变量
   - 设置SSL证书
   - 配置域名解析

2. **部署步骤**
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装依赖
npm install --production

# 3. 构建前端
cd frontend && npm run build

# 4. 重启服务
pm2 restart all
```

## 性能优化

### 前端优化

1. **代码分割**
   - 路由级别分割
   - 组件懒加载
   - 第三方库分离

2. **资源优化**
   - 图片压缩和懒加载
   - 静态资源CDN
   - 缓存策略

3. **渲染优化**
   - 虚拟滚动
   - 防抖和节流
   - memo和useMemo

### 后端优化

1. **数据库优化**
   - 索引优化
   - 查询优化
   - 连接池配置

2. **缓存策略**
   - Redis缓存
   - 查询结果缓存
   - 静态资源缓存

3. **并发处理**
   - 异步处理
   - 队列系统
   - 负载均衡

## 监控和日志

### 日志管理

1. **日志级别**
   - ERROR: 错误信息
   - WARN: 警告信息
   - INFO: 一般信息
   - DEBUG: 调试信息

2. **日志格式**
```json
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "INFO",
  "message": "用户登录成功",
  "userId": "uuid",
  "ip": "127.0.0.1",
  "userAgent": "Mozilla/5.0..."
}
```

### 性能监控

1. **关键指标**
   - 响应时间
   - 错误率
   - 并发用户数
   - 数据库性能

2. **监控工具**
   - 应用性能监控 (APM)
   - 数据库监控
   - 服务器监控

## 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置
   - 查看网络连接

2. **认证失败**
   - 检查JWT配置
   - 验证Token有效性
   - 查看用户权限

3. **文件上传失败**
   - 检查文件大小限制
   - 验证文件类型
   - 查看存储空间

### 调试技巧

1. **后端调试**
   - 使用console.log和debugger
   - 查看日志文件
   - 使用Postman测试API

2. **前端调试**
   - 使用浏览器开发者工具
   - React Developer Tools
   - 网络请求监控

## 贡献指南

### 开发流程

1. Fork项目到个人仓库
2. 创建功能分支
3. 开发和测试
4. 提交Pull Request
5. 代码审查
6. 合并到主分支

### 代码审查

1. **审查要点**
   - 代码质量和规范
   - 功能完整性
   - 测试覆盖率
   - 性能影响

2. **审查流程**
   - 自动化检查
   - 人工审查
   - 测试验证
   - 文档更新

## 联系方式

- 项目维护者: [维护者信息]
- 技术支持: [支持邮箱]
- 问题反馈: [GitHub Issues]
