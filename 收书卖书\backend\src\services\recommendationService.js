const { Op } = require('sequelize');
const { Book, User, Order, OrderItem, Favorite, Review, Category } = require('../models');
const logger = require('../utils/logger');

class RecommendationService {
  // 基于用户行为的协同过滤推荐
  async getCollaborativeRecommendations(userId, limit = 10) {
    try {
      // 1. 获取用户的购买历史和收藏
      const userInteractions = await this.getUserInteractions(userId);
      
      if (userInteractions.length === 0) {
        // 如果用户没有交互历史，返回热门图书
        return this.getPopularBooks(limit);
      }

      // 2. 找到相似用户
      const similarUsers = await this.findSimilarUsers(userId, userInteractions);
      
      // 3. 基于相似用户的偏好推荐图书
      const recommendations = await this.getRecommendationsFromSimilarUsers(
        userId, 
        similarUsers, 
        limit
      );

      return recommendations;
    } catch (error) {
      logger.error('协同过滤推荐失败:', error);
      return this.getPopularBooks(limit);
    }
  }

  // 基于内容的推荐
  async getContentBasedRecommendations(userId, limit = 10) {
    try {
      // 1. 获取用户偏好的分类和作者
      const userPreferences = await this.getUserPreferences(userId);
      
      if (!userPreferences.categories.length && !userPreferences.authors.length) {
        return this.getPopularBooks(limit);
      }

      // 2. 基于偏好推荐相似图书
      const recommendations = await Book.findAll({
        where: {
          [Op.or]: [
            {
              category_id: {
                [Op.in]: userPreferences.categories
              }
            },
            {
              author: {
                [Op.in]: userPreferences.authors
              }
            }
          ],
          status: '上架',
          stock: {
            [Op.gt]: 0
          }
        },
        include: [
          {
            model: Category,
            as: 'category'
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ],
        order: [
          ['views', 'DESC'],
          ['sales_count', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit
      });

      return recommendations;
    } catch (error) {
      logger.error('基于内容推荐失败:', error);
      return this.getPopularBooks(limit);
    }
  }

  // 混合推荐算法
  async getHybridRecommendations(userId, limit = 10) {
    try {
      const [collaborative, contentBased, popular] = await Promise.all([
        this.getCollaborativeRecommendations(userId, Math.ceil(limit * 0.4)),
        this.getContentBasedRecommendations(userId, Math.ceil(limit * 0.4)),
        this.getPopularBooks(Math.ceil(limit * 0.2))
      ]);

      // 合并并去重
      const allRecommendations = [...collaborative, ...contentBased, ...popular];
      const uniqueRecommendations = this.removeDuplicates(allRecommendations);
      
      // 按评分和热度排序
      const sortedRecommendations = uniqueRecommendations
        .sort((a, b) => {
          const scoreA = this.calculateBookScore(a);
          const scoreB = this.calculateBookScore(b);
          return scoreB - scoreA;
        })
        .slice(0, limit);

      return sortedRecommendations;
    } catch (error) {
      logger.error('混合推荐失败:', error);
      return this.getPopularBooks(limit);
    }
  }

  // 获取热门图书
  async getPopularBooks(limit = 10) {
    try {
      const books = await Book.findAll({
        where: {
          status: '上架',
          stock: {
            [Op.gt]: 0
          }
        },
        include: [
          {
            model: Category,
            as: 'category'
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ],
        order: [
          ['sales_count', 'DESC'],
          ['views', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit
      });

      return books;
    } catch (error) {
      logger.error('获取热门图书失败:', error);
      return [];
    }
  }

  // 获取新书推荐
  async getNewBooks(limit = 10) {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const books = await Book.findAll({
        where: {
          status: '上架',
          stock: {
            [Op.gt]: 0
          },
          created_at: {
            [Op.gte]: thirtyDaysAgo
          }
        },
        include: [
          {
            model: Category,
            as: 'category'
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ],
        order: [['created_at', 'DESC']],
        limit
      });

      return books;
    } catch (error) {
      logger.error('获取新书推荐失败:', error);
      return [];
    }
  }

  // 获取相似图书
  async getSimilarBooks(bookId, limit = 6) {
    try {
      const book = await Book.findByPk(bookId, {
        include: [{ model: Category, as: 'category' }]
      });

      if (!book) {
        return [];
      }

      const similarBooks = await Book.findAll({
        where: {
          id: {
            [Op.ne]: bookId
          },
          [Op.or]: [
            {
              category_id: book.category_id
            },
            {
              author: book.author
            }
          ],
          status: '上架',
          stock: {
            [Op.gt]: 0
          }
        },
        include: [
          {
            model: Category,
            as: 'category'
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ],
        order: [
          ['sales_count', 'DESC'],
          ['views', 'DESC']
        ],
        limit
      });

      return similarBooks;
    } catch (error) {
      logger.error('获取相似图书失败:', error);
      return [];
    }
  }

  // 获取用户交互数据
  async getUserInteractions(userId) {
    try {
      const [orders, favorites, reviews] = await Promise.all([
        // 购买记录
        OrderItem.findAll({
          include: [
            {
              model: Order,
              as: 'order',
              where: { user_id: userId },
              attributes: []
            }
          ],
          attributes: ['book_id']
        }),
        // 收藏记录
        Favorite.findAll({
          where: { user_id: userId },
          attributes: ['book_id']
        }),
        // 评论记录
        Review.findAll({
          where: { user_id: userId },
          attributes: ['book_id', 'rating']
        })
      ]);

      const interactions = new Set();
      
      // 购买权重最高
      orders.forEach(order => interactions.add(order.book_id));
      
      // 收藏权重中等
      favorites.forEach(fav => interactions.add(fav.book_id));
      
      // 评论权重根据评分
      reviews.forEach(review => {
        if (review.rating >= 4) {
          interactions.add(review.book_id);
        }
      });

      return Array.from(interactions);
    } catch (error) {
      logger.error('获取用户交互数据失败:', error);
      return [];
    }
  }

  // 找到相似用户
  async findSimilarUsers(userId, userInteractions) {
    try {
      // 简化的相似度计算：找到有共同购买/收藏的用户
      const similarUserIds = new Set();

      // 基于购买记录找相似用户
      const orders = await OrderItem.findAll({
        where: {
          book_id: {
            [Op.in]: userInteractions
          }
        },
        include: [
          {
            model: Order,
            as: 'order',
            where: {
              user_id: {
                [Op.ne]: userId
              }
            },
            attributes: ['user_id']
          }
        ]
      });

      orders.forEach(order => {
        similarUserIds.add(order.order.user_id);
      });

      // 基于收藏记录找相似用户
      const favorites = await Favorite.findAll({
        where: {
          book_id: {
            [Op.in]: userInteractions
          },
          user_id: {
            [Op.ne]: userId
          }
        },
        attributes: ['user_id']
      });

      favorites.forEach(fav => {
        similarUserIds.add(fav.user_id);
      });

      return Array.from(similarUserIds).slice(0, 50); // 限制相似用户数量
    } catch (error) {
      logger.error('查找相似用户失败:', error);
      return [];
    }
  }

  // 基于相似用户获取推荐
  async getRecommendationsFromSimilarUsers(userId, similarUsers, limit) {
    try {
      if (similarUsers.length === 0) {
        return [];
      }

      // 获取相似用户的交互记录
      const recommendations = await Book.findAll({
        include: [
          {
            model: OrderItem,
            as: 'orderItems',
            include: [
              {
                model: Order,
                as: 'order',
                where: {
                  user_id: {
                    [Op.in]: similarUsers
                  }
                }
              }
            ]
          },
          {
            model: Category,
            as: 'category'
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'username']
          }
        ],
        where: {
          status: '上架',
          stock: {
            [Op.gt]: 0
          }
        },
        order: [
          ['sales_count', 'DESC'],
          ['views', 'DESC']
        ],
        limit: limit * 2 // 获取更多候选，后续过滤
      });

      // 过滤掉用户已经交互过的图书
      const userInteractions = await this.getUserInteractions(userId);
      const filtered = recommendations.filter(book => 
        !userInteractions.includes(book.id)
      );

      return filtered.slice(0, limit);
    } catch (error) {
      logger.error('基于相似用户推荐失败:', error);
      return [];
    }
  }

  // 获取用户偏好
  async getUserPreferences(userId) {
    try {
      const [orderBooks, favoriteBooks, reviewBooks] = await Promise.all([
        // 购买的图书
        Book.findAll({
          include: [
            {
              model: OrderItem,
              as: 'orderItems',
              include: [
                {
                  model: Order,
                  as: 'order',
                  where: { user_id: userId }
                }
              ]
            }
          ]
        }),
        // 收藏的图书
        Book.findAll({
          include: [
            {
              model: Favorite,
              as: 'favorites',
              where: { user_id: userId }
            }
          ]
        }),
        // 高评分的图书
        Book.findAll({
          include: [
            {
              model: Review,
              as: 'reviews',
              where: { 
                user_id: userId,
                rating: {
                  [Op.gte]: 4
                }
              }
            }
          ]
        })
      ]);

      const allBooks = [...orderBooks, ...favoriteBooks, ...reviewBooks];
      const categories = [...new Set(allBooks.map(book => book.category_id).filter(Boolean))];
      const authors = [...new Set(allBooks.map(book => book.author).filter(Boolean))];

      return { categories, authors };
    } catch (error) {
      logger.error('获取用户偏好失败:', error);
      return { categories: [], authors: [] };
    }
  }

  // 计算图书评分
  calculateBookScore(book) {
    const viewsScore = Math.log(book.views + 1) * 0.3;
    const salesScore = Math.log(book.sales_count + 1) * 0.5;
    const timeScore = this.getTimeScore(book.created_at) * 0.2;
    
    return viewsScore + salesScore + timeScore;
  }

  // 计算时间评分（新书加分）
  getTimeScore(createdAt) {
    const now = new Date();
    const created = new Date(createdAt);
    const daysDiff = (now - created) / (1000 * 60 * 60 * 24);
    
    if (daysDiff <= 7) return 10;
    if (daysDiff <= 30) return 5;
    if (daysDiff <= 90) return 2;
    return 1;
  }

  // 去重
  removeDuplicates(books) {
    const seen = new Set();
    return books.filter(book => {
      if (seen.has(book.id)) {
        return false;
      }
      seen.add(book.id);
      return true;
    });
  }
}

module.exports = new RecommendationService();
