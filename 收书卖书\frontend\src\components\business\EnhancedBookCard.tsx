import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Tag, Button, Space, Typography, message, Tooltip } from 'antd';
import { 
  ShoppingCartOutlined, 
  EyeOutlined, 
  FireOutlined,
  CrownOutlined,
  ThunderboltOutlined,
  StarOutlined,
  StarFilled
} from '@ant-design/icons';
import styled from 'styled-components';
import { Book } from '../../types';
import { useCartStore } from '../../stores/cartStore';
import { useAuthStore } from '../../stores/authStore';
import FavoriteButton from './FavoriteButton';
import LazyImage from '../ui/LazyImage';

const { Text } = Typography;

const StyledCard = styled(Card)`
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f0f0f0;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  &:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: #91caff;
  }
  
  .ant-card-body {
    padding: 20px;
  }
  
  .ant-card-cover {
    position: relative;
    overflow: hidden;
  }
`;

const ImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 280px;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  
  .book-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    filter: brightness(1) saturate(1);
  }
  
  &:hover .book-image {
    transform: scale(1.08);
    filter: brightness(1.1) saturate(1.2);
  }
  
  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(22, 119, 255, 0.8) 0%,
      rgba(64, 150, 255, 0.6) 100%
    );
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
    
    .overlay-actions {
      display: flex;
      gap: 16px;
      transform: translateY(20px);
      transition: transform 0.3s ease;
    }
  }
  
  &:hover .image-overlay {
    opacity: 1;
    
    .overlay-actions {
      transform: translateY(0);
    }
  }
`;

const BookInfo = styled.div`
  .book-title {
    font-size: 18px;
    font-weight: 700;
    color: #262626;
    margin-bottom: 10px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 50px;
    cursor: pointer;
    transition: color 0.3s ease;
    
    &:hover {
      color: #1677ff;
    }
  }
  
  .book-author {
    font-size: 14px;
    color: #595959;
    margin-bottom: 16px;
    font-weight: 500;
  }
  
  .book-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .condition-tag {
      font-weight: 600;
      border-radius: 8px;
      padding: 4px 12px;
      font-size: 12px;
    }
    
    .stats {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 13px;
      color: #8c8c8c;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
      }
    }
  }
  
  .price-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .price-info {
      display: flex;
      align-items: baseline;
      gap: 10px;
      
      .current-price {
        font-size: 24px;
        font-weight: 800;
        color: #ff4d4f;
        text-shadow: 0 1px 2px rgba(255, 77, 79, 0.2);
      }
      
      .original-price {
        font-size: 16px;
        color: #8c8c8c;
        text-decoration: line-through;
        font-weight: 500;
      }
      
      .discount {
        background: linear-gradient(135deg, #ff4d4f, #ff7875);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 700;
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
      }
    }
    
    .rating {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 13px;
      color: #faad14;
    }
  }
  
  .actions {
    display: flex;
    gap: 12px;
    
    .add-cart-btn {
      flex: 1;
      height: 44px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 15px;
      background: linear-gradient(135deg, #1677ff, #4096ff);
      border: none;
      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
      
      &:hover {
        background: linear-gradient(135deg, #0958d9, #1677ff);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(22, 119, 255, 0.4);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
    
    .favorite-btn {
      width: 44px;
      height: 44px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #f0f0f0;
      background: white;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #ff4d4f;
        background: #fff2f0;
        transform: scale(1.1);
      }
    }
  }
`;

const StatusBadge = styled.div<{ type: 'hot' | 'new' | 'sale' | 'vip' }>`
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 700;
  color: white;
  backdrop-filter: blur(10px);
  
  ${props => {
    switch (props.type) {
      case 'hot':
        return `
          background: linear-gradient(135deg, #ff4d4f, #ff7875);
          box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);
        `;
      case 'new':
        return `
          background: linear-gradient(135deg, #52c41a, #73d13d);
          box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
        `;
      case 'sale':
        return `
          background: linear-gradient(135deg, #faad14, #ffc069);
          box-shadow: 0 4px 12px rgba(250, 173, 20, 0.4);
        `;
      case 'vip':
        return `
          background: linear-gradient(135deg, #722ed1, #9254de);
          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.4);
        `;
      default:
        return '';
    }
  }}
`;

const StockIndicator = styled.div<{ stock: number }>`
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 2;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  
  ${props => {
    if (props.stock === 0) {
      return `
        background: rgba(255, 77, 79, 0.9);
        color: white;
      `;
    } else if (props.stock <= 5) {
      return `
        background: rgba(250, 173, 20, 0.9);
        color: white;
      `;
    } else {
      return `
        background: rgba(82, 196, 26, 0.9);
        color: white;
      `;
    }
  }}
`;

interface EnhancedBookCardProps {
  book: Book;
  showActions?: boolean;
  size?: 'small' | 'default' | 'large';
  className?: string;
  style?: React.CSSProperties;
}

const EnhancedBookCard: React.FC<EnhancedBookCardProps> = ({
  book,
  showActions = true,
  size = 'default',
  className,
  style
}) => {
  const navigate = useNavigate();
  const { addItem } = useCartStore();
  const { isAuthenticated } = useAuthStore();

  // 计算折扣
  const discount = book.original_price && book.original_price > book.price
    ? Math.round((1 - book.price / book.original_price) * 100)
    : null;

  // 判断图书状态
  const isHot = book.sales_count > 50 || book.views > 1000;
  const isNew = new Date(book.created_at).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000;
  const isOnSale = discount && discount > 20;
  const isVip = book.price > 100;

  // 模拟评分（实际项目中应该从数据库获取）
  const rating = 4.2 + Math.random() * 0.8;

  // 获取状况标签颜色
  const getConditionColor = (condition: string) => {
    const colorMap: Record<string, string> = {
      '全新': '#52c41a',
      '九成新': '#1677ff',
      '八成新': '#faad14',
      '七成新': '#ff7875',
      '六成新': '#ff4d4f'
    };
    return colorMap[condition] || '#8c8c8c';
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isAuthenticated) {
      message.warning('请先登录');
      return;
    }
    
    if (book.stock === 0) {
      message.error('商品已售罄');
      return;
    }
    
    addItem({
      id: book.id,
      title: book.title,
      price: book.price,
      cover_image: book.cover_image,
      author: book.author,
      stock: book.stock
    });
    message.success('已添加到购物车');
  };

  const handleViewDetail = () => {
    navigate(`/books/${book.id}`);
  };

  return (
    <StyledCard
      hoverable
      className={`enhanced-book-card ${className || ''}`}
      style={style}
      cover={
        <ImageContainer onClick={handleViewDetail}>
          <LazyImage
            src={book.cover_image || '/images/book-placeholder.png'}
            alt={book.title}
            className="book-image"
          />
          
          {/* 状态徽章 */}
          {isHot && (
            <StatusBadge type="hot">
              <FireOutlined />
              热销
            </StatusBadge>
          )}
          {isNew && !isHot && (
            <StatusBadge type="new">
              <ThunderboltOutlined />
              新品
            </StatusBadge>
          )}
          {isOnSale && !isHot && !isNew && (
            <StatusBadge type="sale">
              特惠 {discount}折
            </StatusBadge>
          )}
          {isVip && !isHot && !isNew && !isOnSale && (
            <StatusBadge type="vip">
              <CrownOutlined />
              精品
            </StatusBadge>
          )}
          
          {/* 库存指示器 */}
          <StockIndicator stock={book.stock}>
            {book.stock === 0 ? '售罄' : book.stock <= 5 ? `仅剩${book.stock}本` : '现货'}
          </StockIndicator>
          
          {/* 悬浮操作 */}
          <div className="image-overlay">
            <div className="overlay-actions">
              <Tooltip title="查看详情">
                <Button
                  type="primary"
                  shape="circle"
                  icon={<EyeOutlined />}
                  size="large"
                  onClick={handleViewDetail}
                />
              </Tooltip>
              {showActions && (
                <FavoriteButton
                  bookId={book.id}
                  size="large"
                  type="default"
                />
              )}
            </div>
          </div>
        </ImageContainer>
      }
    >
      <BookInfo>
        <div className="book-title" onClick={handleViewDetail}>
          {book.title}
        </div>
        
        <div className="book-author">
          <Text type="secondary">{book.author || '未知作者'}</Text>
        </div>
        
        <div className="book-meta">
          <Tag 
            className="condition-tag"
            color={getConditionColor(book.condition)}
          >
            {book.condition}
          </Tag>
          
          <div className="stats">
            <div className="stat-item">
              <EyeOutlined />
              {book.views}
            </div>
            <div className="stat-item">
              已售 {book.sales_count}
            </div>
          </div>
        </div>
        
        <div className="price-section">
          <div className="price-info">
            <span className="current-price">¥{book.price}</span>
            {book.original_price && book.original_price > book.price && (
              <>
                <span className="original-price">¥{book.original_price}</span>
                {discount && (
                  <span className="discount">{discount}折</span>
                )}
              </>
            )}
          </div>
          
          <div className="rating">
            <StarFilled />
            {rating.toFixed(1)}
          </div>
        </div>
        
        {showActions && (
          <div className="actions">
            <Button
              type="primary"
              className="add-cart-btn"
              icon={<ShoppingCartOutlined />}
              onClick={handleAddToCart}
              disabled={book.stock === 0}
            >
              {book.stock === 0 ? '已售罄' : '加入购物车'}
            </Button>
            
            <FavoriteButton
              bookId={book.id}
              className="favorite-btn"
            />
          </div>
        )}
      </BookInfo>
    </StyledCard>
  );
};

export default EnhancedBookCard;
