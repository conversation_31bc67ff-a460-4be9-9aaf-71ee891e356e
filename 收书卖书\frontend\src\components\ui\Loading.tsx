import React from 'react';
import { Spin, SpinProps } from 'antd';
import styled, { keyframes, css } from 'styled-components';
import { theme } from '../../styles/theme';

export type LoadingVariant = 'spinner' | 'dots' | 'pulse' | 'wave' | 'skeleton' | 'book';
export type LoadingSize = 'small' | 'medium' | 'large';

interface LoadingProps extends Omit<SpinProps, 'size'> {
  variant?: LoadingVariant;
  size?: LoadingSize;
  text?: string;
  fullScreen?: boolean;
  overlay?: boolean;
}

// 动画定义
const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

const wave = keyframes`
  0%, 60%, 100% { transform: initial; }
  30% { transform: translateY(-15px); }
`;

const bookFlip = keyframes`
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(-90deg); }
  100% { transform: rotateY(0deg); }
`;

const dots = keyframes`
  0%, 20% { color: transparent; text-shadow: .25em 0 0 transparent, .5em 0 0 transparent; }
  40% { color: ${theme.colors.primary[500]}; text-shadow: .25em 0 0 transparent, .5em 0 0 transparent; }
  60% { text-shadow: .25em 0 0 ${theme.colors.primary[500]}, .5em 0 0 transparent; }
  80%, 100% { text-shadow: .25em 0 0 ${theme.colors.primary[500]}, .5em 0 0 ${theme.colors.primary[500]}; }
`;

const LoadingContainer = styled.div<{ fullScreen?: boolean; overlay?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  
  ${({ fullScreen }) => fullScreen && css`
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: ${theme.zIndex.overlay};
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
  `}
  
  ${({ overlay }) => overlay && css`
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: ${theme.zIndex.overlay};
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
  `}
`;

const SpinnerLoader = styled.div<{ size: LoadingSize }>`
  border: 3px solid ${theme.colors.gray[200]};
  border-top: 3px solid ${theme.colors.primary[500]};
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          width: 20px;
          height: 20px;
          border-width: 2px;
        `;
      case 'large':
        return css`
          width: 48px;
          height: 48px;
          border-width: 4px;
        `;
      default:
        return css`
          width: 32px;
          height: 32px;
          border-width: 3px;
        `;
    }
  }}
`;

const DotsLoader = styled.div<{ size: LoadingSize }>`
  &::before {
    content: '●';
    animation: ${dots} 1.4s infinite;
    color: ${theme.colors.primary[500]};
    
    ${({ size }) => {
      switch (size) {
        case 'small':
          return css`
            font-size: 12px;
          `;
        case 'large':
          return css`
            font-size: 24px;
          `;
        default:
          return css`
            font-size: 16px;
          `;
      }
    }}
  }
`;

const PulseLoader = styled.div<{ size: LoadingSize }>`
  background: ${theme.colors.primary[500]};
  border-radius: 50%;
  animation: ${pulse} 1.5s ease-in-out infinite;
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          width: 20px;
          height: 20px;
        `;
      case 'large':
        return css`
          width: 48px;
          height: 48px;
        `;
      default:
        return css`
          width: 32px;
          height: 32px;
        `;
    }
  }}
`;

const WaveLoader = styled.div<{ size: LoadingSize }>`
  display: flex;
  gap: 4px;
  
  ${({ size }) => {
    const barSize = size === 'small' ? '3px' : size === 'large' ? '6px' : '4px';
    const barHeight = size === 'small' ? '20px' : size === 'large' ? '40px' : '30px';
    
    return css`
      .wave-bar {
        width: ${barSize};
        height: ${barHeight};
        background: ${theme.colors.primary[500]};
        border-radius: 2px;
        animation: ${wave} 1.2s infinite ease-in-out;
        
        &:nth-child(1) { animation-delay: -1.1s; }
        &:nth-child(2) { animation-delay: -1.0s; }
        &:nth-child(3) { animation-delay: -0.9s; }
        &:nth-child(4) { animation-delay: -0.8s; }
        &:nth-child(5) { animation-delay: -0.7s; }
      }
    `;
  }}
`;

const BookLoader = styled.div<{ size: LoadingSize }>`
  position: relative;
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          width: 24px;
          height: 18px;
          font-size: 20px;
        `;
      case 'large':
        return css`
          width: 48px;
          height: 36px;
          font-size: 40px;
        `;
      default:
        return css`
          width: 32px;
          height: 24px;
          font-size: 28px;
        `;
    }
  }}
  
  &::before {
    content: '📚';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ${bookFlip} 2s infinite;
  }
`;

const SkeletonLoader = styled.div<{ size: LoadingSize }>`
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  ${({ size }) => {
    const width = size === 'small' ? '200px' : size === 'large' ? '400px' : '300px';
    
    return css`
      width: ${width};
      
      .skeleton-line {
        height: 12px;
        background: linear-gradient(
          90deg,
          ${theme.colors.gray[200]} 25%,
          ${theme.colors.gray[100]} 50%,
          ${theme.colors.gray[200]} 75%
        );
        background-size: 200% 100%;
        border-radius: 6px;
        animation: ${pulse} 1.5s infinite;
        
        &:nth-child(1) { width: 100%; }
        &:nth-child(2) { width: 80%; }
        &:nth-child(3) { width: 60%; }
      }
    `;
  }}
`;

const LoadingText = styled.div<{ size: LoadingSize }>`
  color: ${theme.colors.textSecondary};
  text-align: center;
  
  ${({ size }) => {
    switch (size) {
      case 'small':
        return css`
          font-size: ${theme.typography.fontSize.sm};
        `;
      case 'large':
        return css`
          font-size: ${theme.typography.fontSize.lg};
        `;
      default:
        return css`
          font-size: ${theme.typography.fontSize.base};
        `;
    }
  }}
`;

const Loading: React.FC<LoadingProps> = ({
  variant = 'spinner',
  size = 'medium',
  text,
  fullScreen = false,
  overlay = false,
  ...props
}) => {
  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return <DotsLoader size={size} />;
      case 'pulse':
        return <PulseLoader size={size} />;
      case 'wave':
        return (
          <WaveLoader size={size}>
            <div className="wave-bar" />
            <div className="wave-bar" />
            <div className="wave-bar" />
            <div className="wave-bar" />
            <div className="wave-bar" />
          </WaveLoader>
        );
      case 'book':
        return <BookLoader size={size} />;
      case 'skeleton':
        return (
          <SkeletonLoader size={size}>
            <div className="skeleton-line" />
            <div className="skeleton-line" />
            <div className="skeleton-line" />
          </SkeletonLoader>
        );
      default:
        return <SpinnerLoader size={size} />;
    }
  };

  if (variant === 'skeleton') {
    return (
      <LoadingContainer fullScreen={fullScreen} overlay={overlay}>
        {renderLoader()}
        {text && <LoadingText size={size}>{text}</LoadingText>}
      </LoadingContainer>
    );
  }

  return (
    <LoadingContainer fullScreen={fullScreen} overlay={overlay}>
      <Spin 
        spinning={true} 
        indicator={renderLoader()} 
        size={size === 'small' ? 'small' : size === 'large' ? 'large' : 'default'}
        {...props}
      >
        <div />
      </Spin>
      {text && <LoadingText size={size}>{text}</LoadingText>}
    </LoadingContainer>
  );
};

export default Loading;
