import React, { useState, useEffect, useRef } from 'react';
import { AutoComplete, Input, Spin, Typography } from 'antd';
import { SearchOutlined, BookOutlined, UserOutlined, TagOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { debounce } from '../../utils/helpers';
import { booksService } from '../../services/books';

const { Text } = Typography;

const SuggestionItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  
  .suggestion-icon {
    margin-right: 8px;
    color: #999;
  }
  
  .suggestion-content {
    flex: 1;
    
    .suggestion-title {
      font-size: 14px;
      color: #262626;
      margin-bottom: 2px;
    }
    
    .suggestion-meta {
      font-size: 12px;
      color: #999;
    }
  }
  
  .suggestion-type {
    font-size: 12px;
    color: #1890ff;
    background: #f0f8ff;
    padding: 2px 6px;
    border-radius: 2px;
  }
`;

interface SearchSuggestionProps {
  placeholder?: string;
  onSearch?: (value: string) => void;
  onSelect?: (value: string, option: any) => void;
  style?: React.CSSProperties;
  className?: string;
  size?: 'small' | 'middle' | 'large';
  allowClear?: boolean;
  autoFocus?: boolean;
}

interface SuggestionOption {
  value: string;
  label: React.ReactNode;
  type: 'book' | 'author' | 'category';
  data?: any;
}

const SearchSuggestion: React.FC<SearchSuggestionProps> = ({
  placeholder = '搜索图书、作者、分类...',
  onSearch,
  onSelect,
  style,
  className,
  size = 'middle',
  allowClear = true,
  autoFocus = false
}) => {
  const [options, setOptions] = useState<SuggestionOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const searchRef = useRef<any>(null);

  // 防抖搜索函数
  const debouncedSearch = useRef(
    debounce(async (value: string) => {
      if (!value.trim()) {
        setOptions([]);
        return;
      }

      try {
        setLoading(true);
        
        // 搜索图书
        const booksResponse = await booksService.searchBooks({
          keyword: value,
          limit: 5
        });

        const suggestions: SuggestionOption[] = [];

        if (booksResponse.success && booksResponse.data.books) {
          // 添加图书建议
          booksResponse.data.books.forEach(book => {
            suggestions.push({
              value: book.title,
              type: 'book',
              data: book,
              label: (
                <SuggestionItem>
                  <BookOutlined className="suggestion-icon" />
                  <div className="suggestion-content">
                    <div className="suggestion-title">{book.title}</div>
                    <div className="suggestion-meta">
                      {book.author} · {book.category?.name} · ¥{book.price}
                    </div>
                  </div>
                  <div className="suggestion-type">图书</div>
                </SuggestionItem>
              )
            });

            // 添加作者建议（去重）
            if (book.author && !suggestions.some(s => s.value === book.author && s.type === 'author')) {
              suggestions.push({
                value: book.author,
                type: 'author',
                data: { author: book.author },
                label: (
                  <SuggestionItem>
                    <UserOutlined className="suggestion-icon" />
                    <div className="suggestion-content">
                      <div className="suggestion-title">{book.author}</div>
                      <div className="suggestion-meta">作者</div>
                    </div>
                    <div className="suggestion-type">作者</div>
                  </SuggestionItem>
                )
              });
            }

            // 添加分类建议（去重）
            if (book.category && !suggestions.some(s => s.value === book.category!.name && s.type === 'category')) {
              suggestions.push({
                value: book.category.name,
                type: 'category',
                data: book.category,
                label: (
                  <SuggestionItem>
                    <TagOutlined className="suggestion-icon" />
                    <div className="suggestion-content">
                      <div className="suggestion-title">{book.category.name}</div>
                      <div className="suggestion-meta">分类</div>
                    </div>
                    <div className="suggestion-type">分类</div>
                  </SuggestionItem>
                )
              });
            }
          });
        }

        // 限制建议数量
        setOptions(suggestions.slice(0, 10));
      } catch (error) {
        console.error('搜索建议失败:', error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    }, 300)
  ).current;

  // 处理搜索值变化
  const handleSearch = (value: string) => {
    setSearchValue(value);
    debouncedSearch(value);
  };

  // 处理选择建议
  const handleSelect = (value: string, option: SuggestionOption) => {
    setSearchValue(value);
    onSelect?.(value, option);
    
    // 如果选择的是图书，可以直接跳转到详情页
    if (option.type === 'book' && option.data) {
      window.location.href = `/books/${option.data.id}`;
    } else {
      // 其他类型执行搜索
      onSearch?.(value);
    }
  };

  // 处理回车搜索
  const handlePressEnter = () => {
    onSearch?.(searchValue);
  };

  // 清除搜索
  const handleClear = () => {
    setSearchValue('');
    setOptions([]);
  };

  return (
    <AutoComplete
      ref={searchRef}
      value={searchValue}
      options={options}
      onSearch={handleSearch}
      onSelect={handleSelect}
      onClear={handleClear}
      style={style}
      className={className}
      size={size}
      allowClear={allowClear}
      autoFocus={autoFocus}
      notFoundContent={loading ? <Spin size="small" /> : null}
      filterOption={false} // 禁用默认过滤，使用自定义搜索
    >
      <Input
        placeholder={placeholder}
        prefix={<SearchOutlined />}
        onPressEnter={handlePressEnter}
        suffix={loading ? <Spin size="small" /> : null}
      />
    </AutoComplete>
  );
};

export default SearchSuggestion;
