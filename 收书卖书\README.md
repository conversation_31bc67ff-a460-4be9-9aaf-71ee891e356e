# 收书卖书平台

大学生二手书交易平台，专注于为大学生提供便捷、安全的二手书买卖服务。

## 项目概述

### 业务模式
- **B2C模式**: 平台收购后销售，确保图书质量和交易安全
- **目标用户**: 大学生群体
- **核心价值**: 让知识传递更有价值，降低学习成本

### 主要功能
- 📚 图书浏览与搜索
- 🛒 购物车与订单管理
- 💬 实时消息系统
- 👥 用户管理与认证
- 📊 管理员后台
- 📱 移动端适配

## 技术架构

### 前端技术栈
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design 5.x
- **状态管理**: Zustand
- **样式**: Styled Components
- **路由**: React Router v6
- **HTTP客户端**: Axios
- **构建工具**: Create React App

### 后端技术栈
- **运行时**: Node.js 18+
- **框架**: Express.js
- **数据库**: PostgreSQL + Sequelize ORM
- **认证**: JWT + bcryptjs
- **实时通信**: Socket.IO
- **文件上传**: Multer
- **数据验证**: Joi
- **安全**: Helmet, CORS, Rate Limiting

## 快速开始

### 环境要求
- Node.js 18.0+
- PostgreSQL 12+
- npm 或 yarn

### 安装与运行

1. **克隆项目**
```bash
git clone <repository-url>
cd 收书卖书
```

2. **安装依赖**
```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

3. **环境配置**
```bash
# 后端环境配置
cd backend
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **数据库初始化**
```bash
# 创建数据库
createdb book_trading

# 运行种子数据
cd backend
npm run seed
```

5. **启动服务**
```bash
# 启动后端服务 (端口: 3001)
cd backend
npm run dev

# 启动前端服务 (端口: 3000)
cd frontend
npm start
```

6. **访问应用**
- 前端应用: http://localhost:3000
- 后端API: http://localhost:3001

### 默认账户
- **超级管理员**: 13800000000 / admin123456
- **普通管理员**: 13800000001 / admin123456
- **测试用户**: 13800000002-13800000006 / test123456

## 项目结构

```
收书卖书/
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── app.js          # 应用入口
│   │   ├── config/         # 配置文件
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由处理
│   │   ├── middleware/     # 中间件
│   │   ├── utils/          # 工具函数
│   │   └── database/       # 数据库脚本
│   ├── package.json
│   └── README.md
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── pages/          # 页面
│   │   ├── services/       # API服务
│   │   ├── stores/         # 状态管理
│   │   ├── types/          # 类型定义
│   │   └── utils/          # 工具函数
│   ├── public/
│   └── package.json
└── README.md
```

## 核心功能

### 用户功能
- ✅ 用户注册/登录
- ✅ 图书浏览与搜索
- ✅ 图书详情页面
- ✅ 购物车管理
- ✅ 订单管理（创建、查看、取消）
- ✅ 个人中心（信息管理、订单查看、安全设置）
- 🚧 消息系统
- 🚧 支付集成

### 管理功能
- ✅ 图书管理 (CRUD)
- ✅ 用户管理
- ✅ 订单管理
- ✅ 分类管理
- ✅ 系统统计
- ✅ 管理员后台界面
- 🚧 数据分析
- 🚧 报表导出

### 系统功能
- ✅ JWT认证
- ✅ 权限控制
- ✅ 文件上传
- ✅ 数据验证
- ✅ 错误处理
- ✅ 日志记录
- ✅ 响应式设计

## API文档

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/refresh` - 刷新令牌

### 图书接口
- `GET /api/books` - 获取图书列表
- `GET /api/books/:id` - 获取图书详情
- `POST /api/books` - 创建图书 (管理员)
- `PUT /api/books/:id` - 更新图书 (管理员)
- `DELETE /api/books/:id` - 删除图书 (管理员)

### 订单接口
- `POST /api/orders` - 创建订单
- `GET /api/orders` - 获取订单列表
- `GET /api/orders/:id` - 获取订单详情
- `PATCH /api/orders/:id/cancel` - 取消订单

更多API文档请参考 `backend/README.md`

## 开发指南

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码
- 组件使用函数式组件 + Hooks

### 提交规范
```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 部署

### Docker 部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境
1. 配置环境变量
2. 构建前端应用
3. 配置反向代理 (Nginx)
4. 设置 SSL 证书
5. 配置监控和日志

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>

---

**注意**: 这是一个学习项目，仅供教育和学习目的使用。
