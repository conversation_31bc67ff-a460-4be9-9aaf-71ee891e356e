import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CartItem, Book } from '../types';
import { message } from 'antd';

interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
}

interface CartActions {
  addItem: (book: Book, quantity?: number) => void;
  removeItem: (bookId: string) => void;
  updateQuantity: (bookId: string, quantity: number) => void;
  clearCart: () => void;
  getItem: (bookId: string) => CartItem | undefined;
  calculateTotals: () => void;
}

export const useCartStore = create<CartState & CartActions>()(
  persist(
    (set, get) => ({
      // 状态
      items: [],
      totalItems: 0,
      totalAmount: 0,

      // 添加商品到购物车
      addItem: (book: Book, quantity = 1) => {
        const { items } = get();
        const existingItem = items.find(item => item.book_id === book.id);

        if (existingItem) {
          // 如果商品已存在，增加数量
          const newQuantity = existingItem.quantity + quantity;
          if (newQuantity > book.stock) {
            message.warning(`库存不足，最多只能添加 ${book.stock} 本`);
            return;
          }
          get().updateQuantity(book.id, newQuantity);
        } else {
          // 如果是新商品，添加到购物车
          if (quantity > book.stock) {
            message.warning(`库存不足，最多只能添加 ${book.stock} 本`);
            return;
          }
          const newItem: CartItem = {
            book_id: book.id,
            book,
            quantity
          };
          set(state => ({
            items: [...state.items, newItem]
          }));
        }
        
        get().calculateTotals();
        message.success('已添加到购物车');
      },

      // 从购物车移除商品
      removeItem: (bookId: string) => {
        set(state => ({
          items: state.items.filter(item => item.book_id !== bookId)
        }));
        get().calculateTotals();
        message.success('已从购物车移除');
      },

      // 更新商品数量
      updateQuantity: (bookId: string, quantity: number) => {
        if (quantity <= 0) {
          get().removeItem(bookId);
          return;
        }

        const { items } = get();
        const item = items.find(item => item.book_id === bookId);
        
        if (item && quantity > item.book.stock) {
          message.warning(`库存不足，最多只能购买 ${item.book.stock} 本`);
          return;
        }

        set(state => ({
          items: state.items.map(item =>
            item.book_id === bookId
              ? { ...item, quantity }
              : item
          )
        }));
        get().calculateTotals();
      },

      // 清空购物车
      clearCart: () => {
        set({
          items: [],
          totalItems: 0,
          totalAmount: 0
        });
      },

      // 获取指定商品
      getItem: (bookId: string) => {
        const { items } = get();
        return items.find(item => item.book_id === bookId);
      },

      // 计算总计
      calculateTotals: () => {
        const { items } = get();
        const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
        const totalAmount = items.reduce((sum, item) => sum + (item.book.price * item.quantity), 0);
        
        set({
          totalItems,
          totalAmount
        });
      }
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({
        items: state.items
      }),
      onRehydrateStorage: () => (state) => {
        // 重新计算总计
        if (state) {
          state.calculateTotals();
        }
      }
    }
  )
);
