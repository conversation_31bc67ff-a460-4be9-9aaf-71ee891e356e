import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  Avatar,
  Space,
  Typography,
  message,
  Row,
  Col,
  Divider
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  WechatOutlined,
  QqOutlined,
  CameraOutlined,
  SaveOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';
import { uploadService } from '../../services/upload';

const { Title, Text } = Typography;

const AvatarUpload = styled.div`
  text-align: center;
  margin-bottom: 24px;
  
  .avatar-wrapper {
    position: relative;
    display: inline-block;
    
    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      cursor: pointer;
      
      &:hover {
        opacity: 1;
      }
      
      .upload-icon {
        color: white;
        font-size: 24px;
      }
    }
  }
`;

const InfoSection = styled.div`
  margin-bottom: 32px;
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #262626;
  }
`;

const ProfileInfo: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      // 这里应该调用更新用户信息的API
      // const response = await userService.updateProfile(values);
      // if (response.success) {
      //   updateUser(response.data);
      //   message.success('个人信息更新成功');
      // }
      
      // 模拟API调用
      setTimeout(() => {
        message.success('个人信息更新成功');
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('更新个人信息失败:', error);
      message.error('更新个人信息失败');
      setLoading(false);
    }
  };

  const handleAvatarUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;
    
    try {
      setAvatarLoading(true);
      const response = await uploadService.uploadAvatar(file as File);
      
      if (response.success && response.data) {
        // 更新用户头像
        if (user) {
          const updatedUser = { ...user, avatar: response.data.url };
          updateUser(updatedUser);
        }
        message.success('头像上传成功');
        onSuccess?.(response.data);
      } else {
        throw new Error(response.message || '上传失败');
      }
    } catch (error: any) {
      console.error('头像上传失败:', error);
      message.error(error.message || '头像上传失败');
      onError?.(error);
    } finally {
      setAvatarLoading(false);
    }
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件');
      return false;
    }
    
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过5MB');
      return false;
    }
    
    return true;
  };

  return (
    <Card>
      <Title level={3}>个人信息</Title>
      
      {/* 头像上传 */}
      <AvatarUpload>
        <div className="avatar-wrapper">
          <Avatar
            size={100}
            src={user?.avatar}
            icon={<UserOutlined />}
          />
          <Upload
            showUploadList={false}
            customRequest={handleAvatarUpload}
            beforeUpload={beforeUpload}
            disabled={avatarLoading}
          >
            <div className="upload-overlay">
              <CameraOutlined className="upload-icon" />
            </div>
          </Upload>
        </div>
        <div style={{ marginTop: 8 }}>
          <Text type="secondary">点击头像更换</Text>
        </div>
      </AvatarUpload>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          username: user?.username,
          email: user?.email,
          contact_wechat: user?.contact_wechat,
          contact_qq: user?.contact_qq,
          contact_phone_public: user?.contact_phone_public
        }}
      >
        <InfoSection>
          <div className="section-title">基本信息</div>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { min: 3, message: '用户名长度至少3位' },
                  { max: 30, message: '用户名长度不能超过30位' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱地址"
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item label="手机号">
            <Input
              prefix={<PhoneOutlined />}
              value={user?.phone}
              disabled
              addonAfter={
                <Button type="link" size="small">
                  更换手机号
                </Button>
              }
            />
          </Form.Item>
        </InfoSection>

        <Divider />

        <InfoSection>
          <div className="section-title">联系方式</div>
          <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
            设置联系方式后，其他用户可以通过这些方式联系您
          </Text>
          
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="contact_wechat"
                label="微信号"
              >
                <Input
                  prefix={<WechatOutlined />}
                  placeholder="请输入微信号"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="contact_qq"
                label="QQ号"
              >
                <Input
                  prefix={<QqOutlined />}
                  placeholder="请输入QQ号"
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="contact_phone_public"
            label="公开电话"
            extra="如果设置，其他用户可以看到这个电话号码"
          >
            <Input
              prefix={<PhoneOutlined />}
              placeholder="请输入公开电话（可选）"
            />
          </Form.Item>
        </InfoSection>

        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              保存修改
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default ProfileInfo;
