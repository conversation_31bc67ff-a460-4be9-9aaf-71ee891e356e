const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ReviewHelpful = sequelize.define('ReviewHelpful', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  review_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'reviews',
      key: 'id'
    }
  },
  is_helpful: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'review_helpful',
  timestamps: false,
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'review_id']
    },
    {
      fields: ['review_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['is_helpful']
    }
  ]
});

module.exports = ReviewHelpful;
