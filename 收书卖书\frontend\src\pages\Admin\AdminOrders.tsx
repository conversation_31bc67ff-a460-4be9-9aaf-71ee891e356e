import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { ShoppingOutlined, ExportOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const AdminOrders: React.FC = () => {
  return (
    <div>
      <Title level={2}>订单管理</Title>
      
      <Card>
        <div style={{ textAlign: 'center', padding: '64px 0' }}>
          <ShoppingOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={3}>订单管理功能</Title>
          <Paragraph type="secondary">
            这里可以查看和管理所有订单，包括订单状态更新、配送管理、退货处理等功能。
          </Paragraph>
          <Space>
            <Button type="primary">
              查看所有订单
            </Button>
            <Button icon={<ExportOutlined />}>
              导出订单数据
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default AdminOrders;
