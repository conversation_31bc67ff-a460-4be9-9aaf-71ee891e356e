# Task Master AI 代码结构详细解读

## 目录
1. [完整目录结构分析](#完整目录结构分析)
2. [核心文件详细解读](#核心文件详细解读)
3. [文件间依赖关系分析](#文件间依赖关系分析)
4. [使用场景和实际应用](#使用场景和实际应用)
5. [配置和扩展指南](#配置和扩展指南)

---

## 完整目录结构分析

### 项目根目录结构

```
task-master/
├── 📁 src/                           # 源代码根目录
│   ├── 📁 commands/                  # 命令实现模块
│   │   ├── 📁 base/                 # 基础命令类和注册器
│   │   ├── 📁 task/                 # 任务管理相关命令
│   │   ├── 📁 analysis/             # 分析功能命令
│   │   ├── 📁 workflow/             # 工作流自动化命令
│   │   ├── 📁 integration/          # 外部集成命令
│   │   └── 📁 utils/                # 工具类命令
│   ├── 📁 core/                      # 核心业务逻辑
│   │   ├── 📁 ai/                   # AI 服务集成层
│   │   ├── 📁 data/                 # 数据访问和管理层
│   │   ├── 📁 analysis/             # 分析引擎核心
│   │   ├── 📁 workflow/             # 工作流引擎
│   │   ├── 📁 security/             # 安全和权限管理
│   │   └── 📁 cache/                # 缓存管理系统
│   ├── 📁 integrations/              # 外部系统集成
│   │   ├── 📁 jira/                 # Jira 集成模块
│   │   ├── 📁 slack/                # Slack 集成模块
│   │   ├── 📁 github/               # GitHub 集成模块
│   │   ├── 📁 database/             # 数据库集成
│   │   └── 📁 base/                 # 集成基础类
│   ├── 📁 plugins/                   # 插件系统
│   │   ├── 📁 core/                 # 插件核心框架
│   │   ├── 📁 builtin/              # 内置插件
│   │   └── 📁 custom/               # 自定义插件目录
│   ├── 📁 utils/                     # 通用工具函数
│   │   ├── 📁 formatters/           # 格式化工具
│   │   ├── 📁 validators/           # 验证工具
│   │   ├── 📁 helpers/              # 辅助函数
│   │   └── 📁 constants/            # 常量定义
│   ├── 📁 types/                     # TypeScript 类型定义
│   │   ├── 📁 core/                 # 核心类型
│   │   ├── 📁 api/                  # API 相关类型
│   │   └── 📁 integrations/         # 集成相关类型
│   ├── 📄 cli.js                     # CLI 应用入口点
│   ├── 📄 app.js                     # Web API 服务入口
│   └── 📄 index.js                   # 模块导出入口
├── 📁 tests/                         # 测试文件目录
│   ├── 📁 unit/                     # 单元测试
│   │   ├── 📁 commands/             # 命令测试
│   │   ├── 📁 core/                 # 核心逻辑测试
│   │   └── 📁 utils/                # 工具函数测试
│   ├── 📁 integration/              # 集成测试
│   │   ├── 📁 api/                  # API 集成测试
│   │   ├── 📁 database/             # 数据库集成测试
│   │   └── 📁 external/             # 外部服务集成测试
│   ├── 📁 e2e/                      # 端到端测试
│   │   ├── 📁 scenarios/            # 测试场景
│   │   └── 📁 fixtures/             # 测试数据
│   ├── 📁 performance/              # 性能测试
│   └── 📁 security/                 # 安全测试
├── 📁 docs/                          # 项目文档
│   ├── 📁 api/                      # API 文档
│   ├── 📁 guides/                   # 使用指南
│   ├── 📁 architecture/             # 架构文档
│   └── 📁 examples/                 # 示例代码
├── 📁 config/                        # 配置文件
│   ├── 📁 environments/             # 环境配置
│   ├── 📁 database/                 # 数据库配置
│   ├── 📁 integrations/             # 集成配置
│   └── 📁 security/                 # 安全配置
├── 📁 migrations/                    # 数据库迁移文件
│   ├── 📁 sql/                      # SQL 迁移脚本
│   └── 📁 data/                     # 数据迁移脚本
├── 📁 scripts/                       # 构建和部署脚本
│   ├── 📁 build/                    # 构建脚本
│   ├── 📁 deploy/                   # 部署脚本
│   └── 📁 maintenance/              # 维护脚本
├── 📁 docker/                        # Docker 相关文件
│   ├── 📄 Dockerfile                # 主应用容器
│   ├── 📄 Dockerfile.worker         # 后台任务容器
│   └── 📁 compose/                  # Docker Compose 配置
├── 📁 k8s/                          # Kubernetes 部署配置
│   ├── 📁 base/                     # 基础配置
│   ├── 📁 overlays/                 # 环境特定配置
│   └── 📁 monitoring/               # 监控配置
├── 📁 .github/                       # GitHub 配置
│   ├── 📁 workflows/                # GitHub Actions
│   └── 📁 ISSUE_TEMPLATE/           # Issue 模板
├── 📄 package.json                   # Node.js 项目配置
├── 📄 package-lock.json             # 依赖锁定文件
├── 📄 tsconfig.json                 # TypeScript 配置
├── 📄 jest.config.js                # Jest 测试配置
├── 📄 eslint.config.js              # ESLint 代码规范配置
├── 📄 prettier.config.js            # Prettier 格式化配置
├── 📄 docker-compose.yml            # 开发环境 Docker Compose
├── 📄 docker-compose.prod.yml       # 生产环境 Docker Compose
├── 📄 .env.example                  # 环境变量示例
├── 📄 .gitignore                    # Git 忽略文件配置
├── 📄 README.md                     # 项目说明文档
├── 📄 CHANGELOG.md                  # 版本更新日志
├── 📄 CONTRIBUTING.md               # 贡献指南
└── 📄 LICENSE                       # 开源许可证
```

### 目录组织设计原理

#### 1. 分层架构原则 (Layered Architecture)
项目采用经典的分层架构模式，从上到下分为：
- **表示层** (`src/commands/`, `src/cli.js`): 用户交互和命令处理
- **业务逻辑层** (`src/core/`): 核心业务逻辑和算法
- **数据访问层** (`src/core/data/`, `src/integrations/database/`): 数据存储和访问
- **基础设施层** (`src/utils/`, `src/integrations/`): 通用工具和外部集成

#### 2. 模块化设计原则 (Modular Design)
每个功能模块都有独立的目录结构，包含：
- **核心逻辑**: 主要业务实现
- **接口定义**: 对外暴露的 API
- **类型定义**: TypeScript 类型声明
- **测试文件**: 对应的测试用例

#### 3. 关注点分离原则 (Separation of Concerns)
- **命令层**: 只负责参数解析和结果展示
- **业务层**: 专注于业务逻辑实现
- **数据层**: 专注于数据存储和检索
- **集成层**: 专注于外部系统交互

---

## 核心文件详细解读

### 应用入口文件

#### `src/cli.js` - CLI 应用主入口
```javascript
#!/usr/bin/env node

/**
 * Task Master AI CLI 应用主入口文件
 * 
 * 功能职责：
 * 1. 初始化 CLI 应用环境
 * 2. 注册所有可用命令
 * 3. 处理全局选项和配置
 * 4. 启动命令解析和执行流程
 * 
 * 设计模式：命令模式 (Command Pattern)
 * 依赖注入：通过 CommandRegistry 管理命令生命周期
 */

const { Command } = require('commander');
const chalk = require('chalk');
const figlet = require('figlet');
const { CommandRegistry } = require('./commands/base/CommandRegistry');
const { ConfigManager } = require('./core/data/ConfigManager');
const { Logger } = require('./utils/Logger');
const { ErrorHandler } = require('./utils/ErrorHandler');
const { PerformanceMonitor } = require('./utils/PerformanceMonitor');

class TaskMasterCLI {
  constructor() {
    this.program = new Command();
    this.registry = new CommandRegistry();
    this.config = new ConfigManager();
    this.logger = new Logger();
    this.errorHandler = new ErrorHandler();
    this.performanceMonitor = new PerformanceMonitor();
  }

  /**
   * 初始化 CLI 应用
   * 设置全局配置、注册命令、配置错误处理
   */
  async initialize() {
    // 显示启动横幅
    this.displayBanner();
    
    // 加载配置
    await this.loadConfiguration();
    
    // 注册所有命令
    await this.registerCommands();
    
    // 设置全局选项
    this.setupGlobalOptions();
    
    // 配置错误处理
    this.setupErrorHandling();
    
    // 启用性能监控
    this.setupPerformanceMonitoring();
  }

  /**
   * 显示应用启动横幅
   */
  displayBanner() {
    console.log(chalk.cyan(figlet.textSync('Task Master AI', {
      font: 'Standard',
      horizontalLayout: 'default',
      verticalLayout: 'default'
    })));
    console.log(chalk.gray('智能项目管理系统 v2.0.0\n'));
  }

  /**
   * 加载应用配置
   * 支持环境变量、配置文件、命令行参数的优先级覆盖
   */
  async loadConfiguration() {
    try {
      await this.config.load();
      this.logger.info('配置加载完成');
    } catch (error) {
      this.logger.warn('配置加载失败，使用默认配置', error);
      await this.config.loadDefaults();
    }
  }

  /**
   * 注册所有可用命令
   * 使用动态加载机制，支持插件命令扩展
   */
  async registerCommands() {
    const commandModules = [
      // 任务管理命令
      require('./commands/task/AddTaskCommand'),
      require('./commands/task/ListCommand'),
      require('./commands/task/ShowCommand'),
      require('./commands/task/UpdateCommand'),
      require('./commands/task/DeleteCommand'),
      
      // 分析功能命令
      require('./commands/analysis/AnalyzeComplexityCommand'),
      require('./commands/analysis/ValidateDependenciesCommand'),
      require('./commands/analysis/GenerateReportCommand'),
      require('./commands/analysis/PerformanceAnalysisCommand'),
      
      // 工作流命令
      require('./commands/workflow/SmartWorkflowCommand'),
      require('./commands/workflow/AutoImplementCommand'),
      require('./commands/workflow/WorkflowTemplateCommand'),
      
      // 集成命令
      require('./commands/integration/JiraSyncCommand'),
      require('./commands/integration/SlackNotifyCommand'),
      require('./commands/integration/GitHubIntegrationCommand'),
      
      // 工具命令
      require('./commands/utils/ConfigCommand'),
      require('./commands/utils/DiagnosticsCommand'),
      require('./commands/utils/BackupCommand'),
      require('./commands/utils/MigrationCommand')
    ];

    // 注册核心命令
    for (const CommandClass of commandModules) {
      const command = new CommandClass();
      this.registry.register(command);
      this.program.addCommand(command.getCommand());
    }

    // 加载插件命令
    await this.loadPluginCommands();
    
    this.logger.info(`已注册 ${this.registry.getCommandCount()} 个命令`);
  }

  /**
   * 加载插件命令
   * 支持动态发现和加载第三方插件
   */
  async loadPluginCommands() {
    const pluginManager = require('./plugins/core/PluginManager');
    const plugins = await pluginManager.loadAllPlugins();
    
    for (const plugin of plugins) {
      const commands = plugin.getCommands();
      for (const command of commands) {
        this.registry.register(command);
        this.program.addCommand(command.getCommand());
      }
    }
  }

  /**
   * 设置全局选项
   * 包括详细输出、配置文件路径、日志级别等
   */
  setupGlobalOptions() {
    this.program
      .name('task-master')
      .description('Task Master AI - 智能项目管理系统')
      .version(require('../package.json').version)
      .option('-v, --verbose', '详细输出模式')
      .option('-q, --quiet', '静默模式')
      .option('-c, --config <path>', '指定配置文件路径')
      .option('--log-level <level>', '设置日志级别', 'info')
      .option('--no-color', '禁用彩色输出')
      .option('--profile', '启用性能分析')
      .hook('preAction', this.preActionHook.bind(this))
      .hook('postAction', this.postActionHook.bind(this));
  }

  /**
   * 命令执行前钩子
   * 处理全局选项，设置执行上下文
   */
  async preActionHook(thisCommand, actionCommand) {
    const options = thisCommand.opts();
    
    // 设置日志级别
    this.logger.setLevel(options.logLevel);
    
    // 设置详细模式
    if (options.verbose) {
      this.logger.setVerbose(true);
    }
    
    // 设置静默模式
    if (options.quiet) {
      this.logger.setQuiet(true);
    }
    
    // 启用性能分析
    if (options.profile) {
      this.performanceMonitor.start(actionCommand.name());
    }
    
    // 记录命令执行
    this.logger.debug(`执行命令: ${actionCommand.name()}`, {
      args: actionCommand.args,
      options: actionCommand.opts()
    });
  }

  /**
   * 命令执行后钩子
   * 清理资源，记录性能数据
   */
  async postActionHook(thisCommand, actionCommand) {
    const options = thisCommand.opts();
    
    // 停止性能监控
    if (options.profile) {
      const metrics = this.performanceMonitor.stop(actionCommand.name());
      this.logger.info('性能指标:', metrics);
    }
    
    // 清理临时资源
    await this.cleanup();
  }

  /**
   * 设置错误处理
   * 统一处理未捕获的异常和 Promise 拒绝
   */
  setupErrorHandling() {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      this.errorHandler.handleUncaughtException(error);
      process.exit(1);
    });

    // 处理未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
      this.errorHandler.handleUnhandledRejection(reason, promise);
      process.exit(1);
    });

    // 处理程序退出
    process.on('SIGINT', async () => {
      this.logger.info('接收到中断信号，正在清理...');
      await this.cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      this.logger.info('接收到终止信号，正在清理...');
      await this.cleanup();
      process.exit(0);
    });
  }

  /**
   * 设置性能监控
   * 监控命令执行时间、内存使用等指标
   */
  setupPerformanceMonitoring() {
    if (this.config.get('monitoring.enabled', false)) {
      this.performanceMonitor.enable();
      this.logger.info('性能监控已启用');
    }
  }

  /**
   * 清理资源
   * 关闭数据库连接、清理临时文件等
   */
  async cleanup() {
    try {
      // 关闭数据库连接
      const dbManager = require('./core/data/DatabaseManager');
      await dbManager.close();
      
      // 清理缓存
      const cacheManager = require('./core/cache/CacheManager');
      await cacheManager.cleanup();
      
      // 保存配置更改
      await this.config.save();
      
      this.logger.info('资源清理完成');
    } catch (error) {
      this.logger.error('资源清理失败:', error);
    }
  }

  /**
   * 启动 CLI 应用
   * 解析命令行参数并执行相应命令
   */
  async run() {
    try {
      await this.initialize();
      await this.program.parseAsync(process.argv);
    } catch (error) {
      this.errorHandler.handleError(error);
      process.exit(1);
    }
  }
}

// 创建并启动 CLI 应用实例
const cli = new TaskMasterCLI();
cli.run().catch(error => {
  console.error(chalk.red('应用启动失败:'), error.message);
  process.exit(1);
});

module.exports = TaskMasterCLI;
```

**文件重要性分析**：
- **系统入口**: 作为整个 CLI 应用的启动点，负责初始化所有核心组件
- **命令注册中心**: 统一管理所有命令的注册和生命周期
- **全局配置管理**: 处理应用级别的配置和选项
- **错误处理中心**: 提供统一的错误处理和资源清理机制
- **性能监控**: 集成性能监控，支持命令执行分析

**性能考虑**：
- 使用懒加载机制，只在需要时加载命令模块
- 实现了资源清理机制，避免内存泄漏
- 集成性能监控，便于识别性能瓶颈

**安全注意事项**：
- 对用户输入进行验证和清理
- 实现了安全的配置文件加载机制
- 提供了错误信息的安全输出，避免敏感信息泄露

#### `src/app.js` - Web API 服务入口
```javascript
/**
 * Task Master AI Web API 服务入口文件
 *
 * 功能职责：
 * 1. 初始化 Express 应用服务器
 * 2. 配置中间件栈和安全策略
 * 3. 注册 API 路由和错误处理
 * 4. 启动 HTTP/HTTPS 服务器
 *
 * 架构模式：分层架构 + 中间件模式
 * 安全特性：HTTPS、CORS、Rate Limiting、Authentication
 */

const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const morgan = require('morgan');
const { createServer } = require('https');
const { readFileSync } = require('fs');

const { APIRouter } = require('./api/routes');
const { AuthenticationMiddleware } = require('./core/security/AuthenticationMiddleware');
const { AuthorizationMiddleware } = require('./core/security/AuthorizationMiddleware');
const { ValidationMiddleware } = require('./utils/ValidationMiddleware');
const { ErrorHandlingMiddleware } = require('./utils/ErrorHandlingMiddleware');
const { LoggingMiddleware } = require('./utils/LoggingMiddleware');
const { ConfigManager } = require('./core/data/ConfigManager');
const { DatabaseManager } = require('./core/data/DatabaseManager');
const { CacheManager } = require('./core/cache/CacheManager');
const { MetricsCollector } = require('./utils/MetricsCollector');

class TaskMasterAPIServer {
  constructor() {
    this.app = express();
    this.config = new ConfigManager();
    this.dbManager = new DatabaseManager();
    this.cacheManager = new CacheManager();
    this.metricsCollector = new MetricsCollector();
    this.server = null;
  }

  /**
   * 初始化 API 服务器
   */
  async initialize() {
    // 加载配置
    await this.loadConfiguration();

    // 初始化数据库连接
    await this.initializeDatabase();

    // 初始化缓存系统
    await this.initializeCache();

    // 配置中间件
    this.setupMiddleware();

    // 注册路由
    this.setupRoutes();

    // 配置错误处理
    this.setupErrorHandling();

    // 启用监控
    this.setupMonitoring();
  }

  /**
   * 配置中间件栈
   * 按照执行顺序配置各种中间件
   */
  setupMiddleware() {
    // 1. 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"]
        }
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    }));

    // 2. CORS 配置
    this.app.use(cors({
      origin: this.config.get('api.cors.origins', ['http://localhost:3000']),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // 3. 压缩中间件
    this.app.use(compression({
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      },
      threshold: 1024
    }));

    // 4. 请求解析中间件
    this.app.use(express.json({
      limit: '10mb',
      verify: (req, res, buf) => {
        req.rawBody = buf;
      }
    }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 5. 日志中间件
    this.app.use(morgan('combined', {
      stream: {
        write: (message) => {
          this.logger.info(message.trim());
        }
      }
    }));

    // 6. 速率限制
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 分钟
      max: this.config.get('api.rateLimit.max', 100),
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // 7. 自定义中间件
    this.app.use(LoggingMiddleware);
    this.app.use('/api/', AuthenticationMiddleware);
    this.app.use('/api/', AuthorizationMiddleware);
    this.app.use('/api/', ValidationMiddleware);
  }

  /**
   * 注册 API 路由
   */
  setupRoutes() {
    // 健康检查端点
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: require('../package.json').version,
        uptime: process.uptime()
      });
    });

    // 就绪检查端点
    this.app.get('/ready', async (req, res) => {
      try {
        // 检查数据库连接
        await this.dbManager.ping();

        // 检查缓存连接
        await this.cacheManager.ping();

        res.json({
          status: 'ready',
          checks: {
            database: 'healthy',
            cache: 'healthy'
          }
        });
      } catch (error) {
        res.status(503).json({
          status: 'not ready',
          error: error.message
        });
      }
    });

    // 指标端点
    this.app.get('/metrics', async (req, res) => {
      const metrics = await this.metricsCollector.collect();
      res.set('Content-Type', 'text/plain');
      res.send(metrics);
    });

    // API 路由
    this.app.use('/api/v1', APIRouter);

    // 静态文件服务（如果需要）
    if (this.config.get('api.serveStatic', false)) {
      this.app.use('/static', express.static('public'));
    }

    // 404 处理
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.originalUrl} not found`,
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * 启动服务器
   */
  async start() {
    const port = this.config.get('api.port', 3000);
    const host = this.config.get('api.host', '0.0.0.0');
    const useHttps = this.config.get('api.https.enabled', false);

    if (useHttps) {
      const httpsOptions = {
        key: readFileSync(this.config.get('api.https.keyPath')),
        cert: readFileSync(this.config.get('api.https.certPath'))
      };

      this.server = createServer(httpsOptions, this.app);
    } else {
      this.server = this.app;
    }

    return new Promise((resolve, reject) => {
      this.server.listen(port, host, (error) => {
        if (error) {
          reject(error);
        } else {
          console.log(`🚀 Task Master API Server started on ${useHttps ? 'https' : 'http'}://${host}:${port}`);
          resolve();
        }
      });
    });
  }

  /**
   * 优雅关闭服务器
   */
  async shutdown() {
    console.log('🔄 Shutting down server gracefully...');

    return new Promise((resolve) => {
      this.server.close(async () => {
        // 关闭数据库连接
        await this.dbManager.close();

        // 关闭缓存连接
        await this.cacheManager.close();

        console.log('✅ Server shutdown complete');
        resolve();
      });
    });
  }
}

// 启动服务器
async function startServer() {
  const server = new TaskMasterAPIServer();

  try {
    await server.initialize();
    await server.start();

    // 优雅关闭处理
    process.on('SIGTERM', () => server.shutdown());
    process.on('SIGINT', () => server.shutdown());

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  startServer();
}

module.exports = TaskMasterAPIServer;
```

**文件重要性分析**：
- **API 服务入口**: 提供 RESTful API 接口，支持 Web 客户端和第三方集成
- **安全防护**: 实现了完整的安全中间件栈，包括 HTTPS、CORS、速率限制
- **监控集成**: 提供健康检查、就绪检查和指标收集端点
- **优雅关闭**: 实现了优雅的服务器关闭机制，确保数据完整性

### 命令系统核心文件

#### `src/commands/base/BaseCommand.js` - 命令基类
```javascript
/**
 * 命令基类 - 所有命令的抽象基类
 *
 * 功能职责：
 * 1. 定义命令的标准接口和生命周期
 * 2. 提供通用的参数解析和验证机制
 * 3. 实现命令执行的模板方法模式
 * 4. 提供错误处理和日志记录功能
 *
 * 设计模式：模板方法模式 + 策略模式
 * 扩展性：支持钩子函数和中间件机制
 */

const { Command } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const { Logger } = require('../../utils/Logger');
const { Validator } = require('../../utils/Validator');
const { ErrorHandler } = require('../../utils/ErrorHandler');
const { PerformanceMonitor } = require('../../utils/PerformanceMonitor');

class BaseCommand {
  constructor(name, description) {
    this.name = name;
    this.description = description;
    this.command = new Command(name);
    this.logger = new Logger();
    this.validator = new Validator();
    this.errorHandler = new ErrorHandler();
    this.performanceMonitor = new PerformanceMonitor();

    // 命令选项和参数
    this.options = new Map();
    this.arguments = [];

    // 钩子函数
    this.hooks = {
      beforeValidation: [],
      afterValidation: [],
      beforeExecution: [],
      afterExecution: [],
      onError: []
    };

    // 中间件
    this.middleware = [];

    this.setupCommand();
  }

  /**
   * 设置命令基本信息
   */
  setupCommand() {
    this.command
      .description(this.description)
      .action(this.executeCommand.bind(this));
  }

  /**
   * 添加命令选项
   */
  addOption(flags, description, defaultValue, validator) {
    this.command.option(flags, description, defaultValue);

    // 存储选项元数据
    const optionName = this.extractOptionName(flags);
    this.options.set(optionName, {
      flags,
      description,
      defaultValue,
      validator
    });

    return this;
  }

  /**
   * 添加命令参数
   */
  addArgument(name, description, validator) {
    this.command.argument(name, description);
    this.arguments.push({
      name,
      description,
      validator
    });

    return this;
  }

  /**
   * 添加钩子函数
   */
  addHook(hookName, handler) {
    if (this.hooks[hookName]) {
      this.hooks[hookName].push(handler);
    }
    return this;
  }

  /**
   * 添加中间件
   */
  use(middleware) {
    this.middleware.push(middleware);
    return this;
  }

  /**
   * 执行命令的主流程（模板方法）
   */
  async executeCommand(...args) {
    const spinner = ora('正在执行命令...').start();
    const startTime = Date.now();

    try {
      // 解析参数和选项
      const { parsedArgs, options } = this.parseArguments(args);

      // 执行前置钩子
      await this.executeHooks('beforeValidation', { args: parsedArgs, options });

      // 验证参数
      await this.validateArguments(parsedArgs, options);

      // 执行后置验证钩子
      await this.executeHooks('afterValidation', { args: parsedArgs, options });

      // 执行中间件
      const context = { args: parsedArgs, options, command: this };
      await this.executeMiddleware(context);

      // 执行前置执行钩子
      await this.executeHooks('beforeExecution', context);

      // 执行主要逻辑
      const result = await this.execute(parsedArgs, options, context);

      // 执行后置执行钩子
      await this.executeHooks('afterExecution', { ...context, result });

      // 显示结果
      await this.displayResult(result, options);

      spinner.succeed(chalk.green('命令执行成功'));

      // 记录性能指标
      const duration = Date.now() - startTime;
      this.performanceMonitor.record(this.name, duration);

      return result;

    } catch (error) {
      spinner.fail(chalk.red('命令执行失败'));

      // 执行错误钩子
      await this.executeHooks('onError', { error, args });

      // 处理错误
      await this.handleError(error);

      throw error;
    }
  }

  /**
   * 解析命令参数和选项
   */
  parseArguments(args) {
    const command = args[args.length - 1]; // Commander.js 传递的命令对象
    const options = command.opts();
    const parsedArgs = args.slice(0, -1);

    return { parsedArgs, options };
  }

  /**
   * 验证参数和选项
   */
  async validateArguments(args, options) {
    // 验证参数
    for (let i = 0; i < this.arguments.length; i++) {
      const argDef = this.arguments[i];
      const argValue = args[i];

      if (argDef.validator) {
        const isValid = await argDef.validator(argValue);
        if (!isValid) {
          throw new Error(`Invalid argument ${argDef.name}: ${argValue}`);
        }
      }
    }

    // 验证选项
    for (const [optionName, optionDef] of this.options) {
      const optionValue = options[optionName];

      if (optionDef.validator && optionValue !== undefined) {
        const isValid = await optionDef.validator(optionValue);
        if (!isValid) {
          throw new Error(`Invalid option ${optionName}: ${optionValue}`);
        }
      }
    }
  }

  /**
   * 执行钩子函数
   */
  async executeHooks(hookName, context) {
    const hooks = this.hooks[hookName] || [];
    for (const hook of hooks) {
      await hook(context);
    }
  }

  /**
   * 执行中间件
   */
  async executeMiddleware(context) {
    for (const middleware of this.middleware) {
      await middleware(context);
    }
  }

  /**
   * 显示执行结果
   * 子类可以重写此方法来自定义输出格式
   */
  async displayResult(result, options) {
    if (options.quiet) {
      return;
    }

    if (options.format === 'json') {
      console.log(JSON.stringify(result, null, 2));
    } else {
      this.displayFormattedResult(result, options);
    }
  }

  /**
   * 格式化显示结果
   * 子类应该重写此方法
   */
  displayFormattedResult(result, options) {
    console.log(result);
  }

  /**
   * 处理错误
   */
  async handleError(error) {
    this.errorHandler.handle(error, {
      command: this.name,
      context: 'command_execution'
    });
  }

  /**
   * 获取 Commander.js 命令对象
   */
  getCommand() {
    return this.command;
  }

  /**
   * 提取选项名称
   */
  extractOptionName(flags) {
    const match = flags.match(/--([a-zA-Z0-9-]+)/);
    return match ? match[1].replace(/-/g, '') : null;
  }

  /**
   * 抽象方法：子类必须实现的主要执行逻辑
   */
  async execute(args, options, context) {
    throw new Error('子类必须实现 execute 方法');
  }
}

module.exports = BaseCommand;
```

**文件重要性分析**：
- **命令架构基础**: 为所有命令提供统一的接口和执行模式
- **模板方法实现**: 定义了命令执行的标准流程，确保一致性
- **扩展性支持**: 通过钩子和中间件机制支持功能扩展
- **错误处理**: 提供统一的错误处理和日志记录机制

#### `src/commands/base/CommandRegistry.js` - 命令注册器
```javascript
/**
 * 命令注册器 - 管理所有命令的注册和生命周期
 *
 * 功能职责：
 * 1. 命令的注册、注销和查找
 * 2. 命令别名管理
 * 3. 命令分组和分类
 * 4. 命令权限验证
 * 5. 插件命令的动态加载
 *
 * 设计模式：注册器模式 + 工厂模式
 * 特性：支持命令热加载、权限控制、性能监控
 */

const { EventEmitter } = require('events');
const { Logger } = require('../../utils/Logger');
const { PermissionManager } = require('../../core/security/PermissionManager');

class CommandRegistry extends EventEmitter {
  constructor() {
    super();
    this.commands = new Map();           // 命令实例映射
    this.aliases = new Map();            // 命令别名映射
    this.categories = new Map();         // 命令分类映射
    this.permissions = new Map();        // 命令权限映射
    this.middleware = [];                // 全局中间件
    this.hooks = new Map();              // 全局钩子
    this.logger = new Logger();
    this.permissionManager = new PermissionManager();

    this.setupEventHandlers();
  }

  /**
   * 注册命令
   */
  register(command, options = {}) {
    const {
      category = 'general',
      aliases = [],
      permissions = [],
      enabled = true,
      priority = 0
    } = options;

    // 验证命令
    this.validateCommand(command);

    // 检查命令是否已存在
    if (this.commands.has(command.name)) {
      throw new Error(`Command '${command.name}' is already registered`);
    }

    // 注册命令
    this.commands.set(command.name, {
      instance: command,
      category,
      permissions,
      enabled,
      priority,
      registeredAt: new Date(),
      usageCount: 0,
      lastUsed: null
    });

    // 注册别名
    for (const alias of aliases) {
      if (this.aliases.has(alias)) {
        throw new Error(`Alias '${alias}' is already registered`);
      }
      this.aliases.set(alias, command.name);
    }

    // 添加到分类
    if (!this.categories.has(category)) {
      this.categories.set(category, new Set());
    }
    this.categories.get(category).add(command.name);

    // 存储权限信息
    if (permissions.length > 0) {
      this.permissions.set(command.name, permissions);
    }

    // 触发注册事件
    this.emit('commandRegistered', {
      name: command.name,
      category,
      aliases,
      permissions
    });

    this.logger.debug(`Command '${command.name}' registered successfully`, {
      category,
      aliases,
      permissions
    });

    return this;
  }

  /**
   * 注销命令
   */
  unregister(commandName) {
    const commandInfo = this.commands.get(commandName);
    if (!commandInfo) {
      throw new Error(`Command '${commandName}' is not registered`);
    }

    // 移除命令
    this.commands.delete(commandName);

    // 移除别名
    for (const [alias, name] of this.aliases) {
      if (name === commandName) {
        this.aliases.delete(alias);
      }
    }

    // 从分类中移除
    for (const [category, commands] of this.categories) {
      commands.delete(commandName);
      if (commands.size === 0) {
        this.categories.delete(category);
      }
    }

    // 移除权限信息
    this.permissions.delete(commandName);

    // 触发注销事件
    this.emit('commandUnregistered', { name: commandName });

    this.logger.debug(`Command '${commandName}' unregistered successfully`);

    return this;
  }

  /**
   * 获取命令
   */
  get(commandName) {
    // 检查直接命令名
    let commandInfo = this.commands.get(commandName);

    // 检查别名
    if (!commandInfo) {
      const actualName = this.aliases.get(commandName);
      if (actualName) {
        commandInfo = this.commands.get(actualName);
      }
    }

    return commandInfo ? commandInfo.instance : null;
  }

  /**
   * 检查命令是否存在
   */
  has(commandName) {
    return this.commands.has(commandName) || this.aliases.has(commandName);
  }

  /**
   * 获取所有命令
   */
  getAll() {
    return Array.from(this.commands.values()).map(info => info.instance);
  }

  /**
   * 按分类获取命令
   */
  getByCategory(category) {
    const commandNames = this.categories.get(category);
    if (!commandNames) {
      return [];
    }

    return Array.from(commandNames)
      .map(name => this.commands.get(name).instance)
      .filter(Boolean);
  }

  /**
   * 获取命令统计信息
   */
  getStats() {
    const stats = {
      totalCommands: this.commands.size,
      totalAliases: this.aliases.size,
      categories: {},
      usage: {},
      permissions: {}
    };

    // 分类统计
    for (const [category, commands] of this.categories) {
      stats.categories[category] = commands.size;
    }

    // 使用统计
    for (const [name, info] of this.commands) {
      stats.usage[name] = {
        count: info.usageCount,
        lastUsed: info.lastUsed
      };
    }

    // 权限统计
    for (const [name, perms] of this.permissions) {
      stats.permissions[name] = perms.length;
    }

    return stats;
  }

  /**
   * 执行命令
   */
  async execute(commandName, args, context = {}) {
    const command = this.get(commandName);
    if (!command) {
      throw new Error(`Command '${commandName}' not found`);
    }

    // 获取实际命令名（处理别名）
    const actualName = this.getActualCommandName(commandName);
    const commandInfo = this.commands.get(actualName);

    // 检查命令是否启用
    if (!commandInfo.enabled) {
      throw new Error(`Command '${actualName}' is disabled`);
    }

    // 权限检查
    await this.checkPermissions(actualName, context.user);

    // 执行全局中间件
    await this.executeGlobalMiddleware(actualName, args, context);

    // 执行全局前置钩子
    await this.executeGlobalHooks('beforeExecution', {
      commandName: actualName,
      args,
      context
    });

    try {
      // 记录使用统计
      commandInfo.usageCount++;
      commandInfo.lastUsed = new Date();

      // 执行命令
      const result = await command.execute(args, context);

      // 执行全局后置钩子
      await this.executeGlobalHooks('afterExecution', {
        commandName: actualName,
        args,
        context,
        result
      });

      // 触发执行事件
      this.emit('commandExecuted', {
        name: actualName,
        args,
        result,
        duration: Date.now() - context.startTime
      });

      return result;

    } catch (error) {
      // 执行全局错误钩子
      await this.executeGlobalHooks('onError', {
        commandName: actualName,
        args,
        context,
        error
      });

      // 触发错误事件
      this.emit('commandError', {
        name: actualName,
        args,
        error
      });

      throw error;
    }
  }

  /**
   * 添加全局中间件
   */
  addGlobalMiddleware(middleware) {
    this.middleware.push(middleware);
    return this;
  }

  /**
   * 添加全局钩子
   */
  addGlobalHook(hookName, handler) {
    if (!this.hooks.has(hookName)) {
      this.hooks.set(hookName, []);
    }
    this.hooks.get(hookName).push(handler);
    return this;
  }

  /**
   * 验证命令
   */
  validateCommand(command) {
    if (!command || typeof command !== 'object') {
      throw new Error('Command must be an object');
    }

    if (!command.name || typeof command.name !== 'string') {
      throw new Error('Command must have a valid name');
    }

    if (!command.execute || typeof command.execute !== 'function') {
      throw new Error('Command must have an execute method');
    }
  }

  /**
   * 检查权限
   */
  async checkPermissions(commandName, user) {
    const requiredPermissions = this.permissions.get(commandName);
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    if (!user) {
      throw new Error('Authentication required');
    }

    const hasPermission = await this.permissionManager.checkPermissions(
      user,
      requiredPermissions
    );

    if (!hasPermission) {
      throw new Error(`Insufficient permissions for command '${commandName}'`);
    }

    return true;
  }

  /**
   * 执行全局中间件
   */
  async executeGlobalMiddleware(commandName, args, context) {
    for (const middleware of this.middleware) {
      await middleware(commandName, args, context);
    }
  }

  /**
   * 执行全局钩子
   */
  async executeGlobalHooks(hookName, data) {
    const hooks = this.hooks.get(hookName) || [];
    for (const hook of hooks) {
      await hook(data);
    }
  }

  /**
   * 获取实际命令名（处理别名）
   */
  getActualCommandName(commandName) {
    return this.aliases.get(commandName) || commandName;
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    this.on('commandRegistered', (data) => {
      this.logger.info(`Command registered: ${data.name}`);
    });

    this.on('commandUnregistered', (data) => {
      this.logger.info(`Command unregistered: ${data.name}`);
    });

    this.on('commandExecuted', (data) => {
      this.logger.debug(`Command executed: ${data.name}`, {
        duration: data.duration,
        args: data.args
      });
    });

    this.on('commandError', (data) => {
      this.logger.error(`Command error: ${data.name}`, {
        error: data.error.message,
        args: data.args
      });
    });
  }

  /**
   * 热重载命令
   */
  async reloadCommand(commandName) {
    const commandInfo = this.commands.get(commandName);
    if (!commandInfo) {
      throw new Error(`Command '${commandName}' not found`);
    }

    // 注销旧命令
    this.unregister(commandName);

    // 重新加载命令模块
    const commandPath = commandInfo.instance.constructor.filePath;
    if (commandPath) {
      delete require.cache[require.resolve(commandPath)];
      const CommandClass = require(commandPath);
      const newCommand = new CommandClass();

      // 重新注册
      this.register(newCommand, {
        category: commandInfo.category,
        permissions: commandInfo.permissions
      });
    }

    this.emit('commandReloaded', { name: commandName });
  }

  /**
   * 获取命令数量
   */
  getCommandCount() {
    return this.commands.size;
  }

  /**
   * 清空所有命令
   */
  clear() {
    this.commands.clear();
    this.aliases.clear();
    this.categories.clear();
    this.permissions.clear();

    this.emit('registryCleared');
  }
}

module.exports = CommandRegistry;
```

**文件重要性分析**：
- **命令管理中心**: 统一管理所有命令的注册、查找和执行
- **权限控制**: 集成权限管理，确保命令执行的安全性
- **性能监控**: 提供命令使用统计和性能监控功能
- **扩展性**: 支持插件命令的动态加载和热重载

### 核心业务逻辑文件

#### `src/core/ai/AIProviderManager.js` - AI 提供商管理器
```javascript
/**
 * AI 提供商管理器 - 统一管理多个 AI 服务提供商
 *
 * 功能职责：
 * 1. 管理多个 AI 提供商的配置和连接
 * 2. 实现智能路由和负载均衡
 * 3. 提供故障转移和重试机制
 * 4. 监控 API 使用量和成本
 * 5. 缓存 AI 响应以提高性能
 *
 * 设计模式：策略模式 + 工厂模式 + 代理模式
 * 特性：智能路由、成本优化、性能监控
 */

const { EventEmitter } = require('events');
const { Logger } = require('../../utils/Logger');
const { CacheManager } = require('../cache/CacheManager');
const { MetricsCollector } = require('../../utils/MetricsCollector');
const { RateLimiter } = require('../../utils/RateLimiter');

// AI 提供商实现
const ClaudeProvider = require('./providers/ClaudeProvider');
const OpenAIProvider = require('./providers/OpenAIProvider');
const PerplexityProvider = require('./providers/PerplexityProvider');

class AIProviderManager extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = config;
    this.providers = new Map();
    this.activeProvider = null;
    this.fallbackProviders = [];
    this.logger = new Logger();
    this.cache = new CacheManager();
    this.metrics = new MetricsCollector();
    this.rateLimiter = new RateLimiter();

    // 路由策略
    this.routingStrategies = {
      cost: this.costBasedRouting.bind(this),
      performance: this.performanceBasedRouting.bind(this),
      capability: this.capabilityBasedRouting.bind(this),
      roundRobin: this.roundRobinRouting.bind(this),
      weighted: this.weightedRouting.bind(this)
    };

    // 当前路由策略
    this.currentStrategy = config.routingStrategy || 'capability';

    // 使用统计
    this.usageStats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalCost: 0,
      averageResponseTime: 0,
      providerUsage: new Map()
    };

    this.initialize();
  }

  /**
   * 初始化 AI 提供商管理器
   */
  async initialize() {
    // 初始化提供商
    await this.initializeProviders();

    // 设置健康检查
    this.setupHealthChecks();

    // 启动监控
    this.startMonitoring();

    this.logger.info('AI Provider Manager initialized', {
      providers: Array.from(this.providers.keys()),
      strategy: this.currentStrategy
    });
  }

  /**
   * 初始化所有配置的提供商
   */
  async initializeProviders() {
    const providerConfigs = this.config.providers || {};

    // Claude 提供商
    if (providerConfigs.claude && providerConfigs.claude.enabled) {
      const claude = new ClaudeProvider(providerConfigs.claude);
      await this.registerProvider('claude', claude, {
        priority: providerConfigs.claude.priority || 1,
        capabilities: ['analysis', 'generation', 'reasoning'],
        costPerToken: 0.003,
        maxTokens: 200000
      });
    }

    // OpenAI 提供商
    if (providerConfigs.openai && providerConfigs.openai.enabled) {
      const openai = new OpenAIProvider(providerConfigs.openai);
      await this.registerProvider('openai', openai, {
        priority: providerConfigs.openai.priority || 2,
        capabilities: ['generation', 'analysis', 'chat'],
        costPerToken: 0.03,
        maxTokens: 128000
      });
    }

    // Perplexity 提供商
    if (providerConfigs.perplexity && providerConfigs.perplexity.enabled) {
      const perplexity = new PerplexityProvider(providerConfigs.perplexity);
      await this.registerProvider('perplexity', perplexity, {
        priority: providerConfigs.perplexity.priority || 3,
        capabilities: ['research', 'analysis'],
        costPerToken: 0.001,
        maxTokens: 32000
      });
    }

    // 设置主要提供商和备用提供商
    this.setupProviderHierarchy();
  }

  /**
   * 注册 AI 提供商
   */
  async registerProvider(name, provider, metadata = {}) {
    try {
      // 验证提供商
      await provider.validate();

      // 注册提供商
      this.providers.set(name, {
        instance: provider,
        metadata: {
          ...metadata,
          registeredAt: new Date(),
          status: 'active',
          healthScore: 100,
          lastHealthCheck: null,
          responseTimeHistory: [],
          errorCount: 0,
          successCount: 0
        }
      });

      // 初始化使用统计
      this.usageStats.providerUsage.set(name, {
        requests: 0,
        successes: 0,
        failures: 0,
        totalCost: 0,
        averageResponseTime: 0
      });

      this.emit('providerRegistered', { name, metadata });
      this.logger.info(`AI Provider '${name}' registered successfully`);

    } catch (error) {
      this.logger.error(`Failed to register AI Provider '${name}':`, error);
      throw error;
    }
  }

  /**
   * 智能请求路由
   */
  async request(prompt, options = {}) {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(prompt, options);
      const cachedResponse = await this.cache.get(cacheKey);

      if (cachedResponse && !options.skipCache) {
        this.logger.debug('Cache hit for AI request', { requestId });
        return cachedResponse;
      }

      // 选择最佳提供商
      const provider = await this.selectProvider(prompt, options);

      if (!provider) {
        throw new Error('No available AI provider');
      }

      // 检查速率限制
      await this.checkRateLimit(provider.name);

      // 发送请求
      const response = await this.executeRequest(provider, prompt, options, requestId);

      // 缓存响应
      if (options.cache !== false) {
        await this.cache.set(cacheKey, response, {
          ttl: options.cacheTTL || 3600 // 1小时
        });
      }

      // 更新统计
      this.updateStats(provider.name, true, Date.now() - startTime, response.cost);

      this.emit('requestCompleted', {
        requestId,
        provider: provider.name,
        duration: Date.now() - startTime,
        cached: false
      });

      return response;

    } catch (error) {
      // 尝试故障转移
      if (options.enableFailover !== false) {
        const fallbackResponse = await this.handleFailover(prompt, options, error, requestId);
        if (fallbackResponse) {
          return fallbackResponse;
        }
      }

      // 更新失败统计
      this.updateStats(null, false, Date.now() - startTime, 0);

      this.emit('requestFailed', {
        requestId,
        error: error.message,
        duration: Date.now() - startTime
      });

      throw error;
    }
  }

  /**
   * 选择最佳 AI 提供商
   */
  async selectProvider(prompt, options) {
    const strategy = options.routingStrategy || this.currentStrategy;
    const routingFunction = this.routingStrategies[strategy];

    if (!routingFunction) {
      throw new Error(`Unknown routing strategy: ${strategy}`);
    }

    const availableProviders = this.getAvailableProviders(options);

    if (availableProviders.length === 0) {
      return null;
    }

    return await routingFunction(availableProviders, prompt, options);
  }

  /**
   * 基于能力的路由策略
   */
  async capabilityBasedRouting(providers, prompt, options) {
    const requiredCapability = options.capability || this.inferCapability(prompt);

    // 过滤具有所需能力的提供商
    const capableProviders = providers.filter(provider =>
      provider.metadata.capabilities.includes(requiredCapability)
    );

    if (capableProviders.length === 0) {
      return providers[0]; // 回退到第一个可用提供商
    }

    // 按优先级和健康分数排序
    capableProviders.sort((a, b) => {
      const scoreA = a.metadata.priority * a.metadata.healthScore;
      const scoreB = b.metadata.priority * b.metadata.healthScore;
      return scoreB - scoreA;
    });

    return capableProviders[0];
  }

  /**
   * 基于成本的路由策略
   */
  async costBasedRouting(providers, prompt, options) {
    const estimatedTokens = this.estimateTokens(prompt);

    // 计算每个提供商的预估成本
    const providersWithCost = providers.map(provider => ({
      ...provider,
      estimatedCost: estimatedTokens * provider.metadata.costPerToken
    }));

    // 按成本排序（最低成本优先）
    providersWithCost.sort((a, b) => a.estimatedCost - b.estimatedCost);

    return providersWithCost[0];
  }

  /**
   * 基于性能的路由策略
   */
  async performanceBasedRouting(providers, prompt, options) {
    // 按平均响应时间排序
    providers.sort((a, b) => {
      const avgTimeA = this.calculateAverageResponseTime(a.metadata.responseTimeHistory);
      const avgTimeB = this.calculateAverageResponseTime(b.metadata.responseTimeHistory);
      return avgTimeA - avgTimeB;
    });

    return providers[0];
  }

  /**
   * 轮询路由策略
   */
  async roundRobinRouting(providers, prompt, options) {
    if (!this.roundRobinIndex) {
      this.roundRobinIndex = 0;
    }

    const provider = providers[this.roundRobinIndex % providers.length];
    this.roundRobinIndex++;

    return provider;
  }

  /**
   * 加权路由策略
   */
  async weightedRouting(providers, prompt, options) {
    const totalWeight = providers.reduce((sum, p) => sum + (p.metadata.weight || 1), 0);
    const random = Math.random() * totalWeight;

    let currentWeight = 0;
    for (const provider of providers) {
      currentWeight += provider.metadata.weight || 1;
      if (random <= currentWeight) {
        return provider;
      }
    }

    return providers[0];
  }

  /**
   * 执行 AI 请求
   */
  async executeRequest(provider, prompt, options, requestId) {
    const startTime = Date.now();

    try {
      this.logger.debug(`Executing AI request with ${provider.name}`, {
        requestId,
        promptLength: prompt.length
      });

      const response = await provider.instance.request(prompt, options);
      const duration = Date.now() - startTime;

      // 更新提供商统计
      const providerInfo = this.providers.get(provider.name);
      providerInfo.metadata.responseTimeHistory.push(duration);
      providerInfo.metadata.successCount++;

      // 限制历史记录长度
      if (providerInfo.metadata.responseTimeHistory.length > 100) {
        providerInfo.metadata.responseTimeHistory.shift();
      }

      return {
        ...response,
        provider: provider.name,
        duration,
        requestId
      };

    } catch (error) {
      // 更新错误统计
      const providerInfo = this.providers.get(provider.name);
      providerInfo.metadata.errorCount++;

      // 降低健康分数
      this.updateHealthScore(provider.name, false);

      throw error;
    }
  }

  /**
   * 处理故障转移
   */
  async handleFailover(prompt, options, originalError, requestId) {
    this.logger.warn('Attempting failover for AI request', {
      requestId,
      originalError: originalError.message
    });

    const fallbackProviders = this.getFallbackProviders(options.excludeProvider);

    for (const provider of fallbackProviders) {
      try {
        const response = await this.executeRequest(provider, prompt, {
          ...options,
          isFailover: true
        }, requestId);

        this.logger.info('Failover successful', {
          requestId,
          fallbackProvider: provider.name
        });

        return response;

      } catch (fallbackError) {
        this.logger.warn('Failover attempt failed', {
          requestId,
          provider: provider.name,
          error: fallbackError.message
        });
        continue;
      }
    }

    return null;
  }

  /**
   * 获取可用提供商
   */
  getAvailableProviders(options = {}) {
    const excludeProvider = options.excludeProvider;

    return Array.from(this.providers.entries())
      .filter(([name, info]) => {
        return info.metadata.status === 'active' &&
               info.metadata.healthScore > 20 &&
               name !== excludeProvider;
      })
      .map(([name, info]) => ({
        name,
        ...info
      }));
  }

  /**
   * 获取备用提供商
   */
  getFallbackProviders(excludeProvider) {
    return this.getAvailableProviders({ excludeProvider })
      .sort((a, b) => b.metadata.priority - a.metadata.priority);
  }

  /**
   * 更新使用统计
   */
  updateStats(providerName, success, duration, cost) {
    this.usageStats.totalRequests++;

    if (success) {
      this.usageStats.successfulRequests++;
      this.usageStats.totalCost += cost || 0;

      // 更新平均响应时间
      const totalTime = this.usageStats.averageResponseTime * (this.usageStats.successfulRequests - 1) + duration;
      this.usageStats.averageResponseTime = totalTime / this.usageStats.successfulRequests;

      if (providerName) {
        const providerStats = this.usageStats.providerUsage.get(providerName);
        providerStats.requests++;
        providerStats.successes++;
        providerStats.totalCost += cost || 0;

        const providerTotalTime = providerStats.averageResponseTime * (providerStats.successes - 1) + duration;
        providerStats.averageResponseTime = providerTotalTime / providerStats.successes;
      }
    } else {
      this.usageStats.failedRequests++;

      if (providerName) {
        const providerStats = this.usageStats.providerUsage.get(providerName);
        providerStats.requests++;
        providerStats.failures++;
      }
    }
  }

  /**
   * 更新健康分数
   */
  updateHealthScore(providerName, success) {
    const providerInfo = this.providers.get(providerName);
    if (!providerInfo) return;

    const currentScore = providerInfo.metadata.healthScore;

    if (success) {
      // 成功时缓慢恢复健康分数
      providerInfo.metadata.healthScore = Math.min(100, currentScore + 2);
    } else {
      // 失败时快速降低健康分数
      providerInfo.metadata.healthScore = Math.max(0, currentScore - 10);
    }

    // 如果健康分数过低，标记为不可用
    if (providerInfo.metadata.healthScore < 20) {
      providerInfo.metadata.status = 'unhealthy';
      this.emit('providerUnhealthy', { name: providerName, score: providerInfo.metadata.healthScore });
    }
  }

  /**
   * 推断请求能力需求
   */
  inferCapability(prompt) {
    const lowerPrompt = prompt.toLowerCase();

    if (lowerPrompt.includes('research') || lowerPrompt.includes('search') || lowerPrompt.includes('find')) {
      return 'research';
    }

    if (lowerPrompt.includes('analyze') || lowerPrompt.includes('complexity') || lowerPrompt.includes('evaluate')) {
      return 'analysis';
    }

    if (lowerPrompt.includes('generate') || lowerPrompt.includes('create') || lowerPrompt.includes('write')) {
      return 'generation';
    }

    if (lowerPrompt.includes('reason') || lowerPrompt.includes('logic') || lowerPrompt.includes('solve')) {
      return 'reasoning';
    }

    return 'analysis'; // 默认能力
  }

  /**
   * 估算 token 数量
   */
  estimateTokens(text) {
    // 简单的 token 估算：大约 4 个字符 = 1 个 token
    return Math.ceil(text.length / 4);
  }

  /**
   * 计算平均响应时间
   */
  calculateAverageResponseTime(history) {
    if (history.length === 0) return 0;
    return history.reduce((sum, time) => sum + time, 0) / history.length;
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(prompt, options) {
    const crypto = require('crypto');
    const key = JSON.stringify({ prompt, options: this.sanitizeOptionsForCache(options) });
    return crypto.createHash('sha256').update(key).digest('hex');
  }

  /**
   * 清理选项用于缓存
   */
  sanitizeOptionsForCache(options) {
    const { skipCache, cache, cacheTTL, ...cacheableOptions } = options;
    return cacheableOptions;
  }

  /**
   * 生成请求 ID
   */
  generateRequestId() {
    return `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查速率限制
   */
  async checkRateLimit(providerName) {
    const key = `ai_provider_${providerName}`;
    const limit = this.config.rateLimit || { requests: 100, window: 60000 }; // 100 requests per minute

    const allowed = await this.rateLimiter.checkLimit(key, limit.requests, limit.window);

    if (!allowed) {
      throw new Error(`Rate limit exceeded for provider ${providerName}`);
    }
  }

  /**
   * 设置健康检查
   */
  setupHealthChecks() {
    const interval = this.config.healthCheckInterval || 300000; // 5 minutes

    setInterval(async () => {
      for (const [name, info] of this.providers) {
        try {
          await info.instance.healthCheck();
          info.metadata.lastHealthCheck = new Date();

          // 恢复健康状态
          if (info.metadata.status === 'unhealthy') {
            info.metadata.status = 'active';
            info.metadata.healthScore = Math.min(100, info.metadata.healthScore + 10);
            this.emit('providerRecovered', { name });
          }

        } catch (error) {
          this.logger.warn(`Health check failed for provider ${name}:`, error.message);
          this.updateHealthScore(name, false);
        }
      }
    }, interval);
  }

  /**
   * 启动监控
   */
  startMonitoring() {
    const interval = this.config.monitoringInterval || 60000; // 1 minute

    setInterval(() => {
      this.emit('statsUpdate', this.getStats());
    }, interval);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.usageStats,
      providers: Object.fromEntries(
        Array.from(this.providers.entries()).map(([name, info]) => [
          name,
          {
            status: info.metadata.status,
            healthScore: info.metadata.healthScore,
            successCount: info.metadata.successCount,
            errorCount: info.metadata.errorCount,
            averageResponseTime: this.calculateAverageResponseTime(info.metadata.responseTimeHistory)
          }
        ])
      )
    };
  }

  /**
   * 关闭管理器
   */
  async close() {
    for (const [name, info] of this.providers) {
      try {
        if (info.instance.close) {
          await info.instance.close();
        }
      } catch (error) {
        this.logger.warn(`Error closing provider ${name}:`, error.message);
      }
    }

    this.providers.clear();
    this.emit('managerClosed');
  }
}

module.exports = AIProviderManager;
```

**文件重要性分析**：
- **AI 服务核心**: 统一管理所有 AI 提供商，是系统智能化的基础
- **智能路由**: 实现基于成本、性能、能力的智能路由策略
- **高可用性**: 提供故障转移、健康检查、负载均衡机制
- **成本优化**: 监控 API 使用量和成本，支持成本优化决策

---

## 文件间依赖关系分析

### 核心依赖关系图

```
                    ┌─────────────────────────────────────────┐
                    │              应用入口层                    │
                    │  ┌─────────────┐    ┌─────────────┐      │
                    │  │   cli.js    │    │   app.js    │      │
                    │  │ (CLI入口)    │    │ (API入口)    │      │
                    │  └─────────────┘    └─────────────┘      │
                    └─────────────┬───────────────┬─────────────┘
                                  │               │
                    ┌─────────────▼───────────────▼─────────────┐
                    │              命令处理层                    │
                    │  ┌─────────────────────────────────────┐  │
                    │  │        CommandRegistry.js          │  │
                    │  │         (命令注册器)                 │  │
                    │  └─────────────────┬───────────────────┘  │
                    │                    │                      │
                    │  ┌─────────────────▼───────────────────┐  │
                    │  │         BaseCommand.js              │  │
                    │  │         (命令基类)                   │  │
                    │  └─────────────────┬───────────────────┘  │
                    └────────────────────┼────────────────────────┘
                                         │
                    ┌────────────────────▼────────────────────────┐
                    │              具体命令层                      │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐       │
                    │  │Task     │ │Analysis │ │Workflow │       │
                    │  │Commands │ │Commands │ │Commands │       │
                    │  └─────────┘ └─────────┘ └─────────┘       │
                    └────────────────────┬────────────────────────┘
                                         │
                    ┌────────────────────▼────────────────────────┐
                    │              核心业务层                      │
                    │  ┌─────────────────────────────────────┐    │
                    │  │      AIProviderManager.js           │    │
                    │  │       (AI提供商管理)                 │    │
                    │  └─────────────────┬───────────────────┘    │
                    │                    │                        │
                    │  ┌─────────────────▼───────────────────┐    │
                    │  │       TaskManager.js                │    │
                    │  │       (任务管理器)                   │    │
                    │  └─────────────────┬───────────────────┘    │
                    │                    │                        │
                    │  ┌─────────────────▼───────────────────┐    │
                    │  │    DependencyAnalyzer.js            │    │
                    │  │     (依赖分析器)                     │    │
                    │  └─────────────────┬───────────────────┘    │
                    └────────────────────┼────────────────────────┘
                                         │
                    ┌────────────────────▼────────────────────────┐
                    │              数据访问层                      │
                    │  ┌─────────────────────────────────────┐    │
                    │  │       DatabaseManager.js            │    │
                    │  │       (数据库管理器)                 │    │
                    │  └─────────────────┬───────────────────┘    │
                    │                    │                        │
                    │  ┌─────────────────▼───────────────────┐    │
                    │  │        CacheManager.js              │    │
                    │  │        (缓存管理器)                  │    │
                    │  └─────────────────┬───────────────────┘    │
                    └────────────────────┼────────────────────────┘
                                         │
                    ┌────────────────────▼────────────────────────┐
                    │              基础设施层                      │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐       │
                    │  │Logger   │ │Validator│ │Metrics  │       │
                    │  │Utils    │ │Utils    │ │Collector│       │
                    │  └─────────┘ └─────────┘ └─────────┘       │
                    └─────────────────────────────────────────────┘
```

### 详细依赖关系分析

#### 1. 垂直依赖关系 (Vertical Dependencies)

**应用入口 → 命令处理 → 业务逻辑 → 数据访问 → 基础设施**

```javascript
// 依赖流向示例
cli.js
├── CommandRegistry.js
│   ├── BaseCommand.js
│   │   ├── TaskManager.js
│   │   │   ├── DatabaseManager.js
│   │   │   │   └── Logger.js
│   │   │   └── CacheManager.js
│   │   └── AIProviderManager.js
│   │       ├── ClaudeProvider.js
│   │       └── MetricsCollector.js
│   └── PluginManager.js
└── ConfigManager.js
```

#### 2. 水平依赖关系 (Horizontal Dependencies)

**同层级模块间的协作关系**

```javascript
// 核心业务层内部依赖
TaskManager.js ←→ DependencyAnalyzer.js
     ↕                    ↕
AIProviderManager.js ←→ WorkflowEngine.js
     ↕                    ↕
SecurityManager.js ←→ AnalysisEngine.js
```

#### 3. 循环依赖检测和解决

**潜在循环依赖**：
```javascript
// 问题：TaskManager ↔ DependencyAnalyzer
TaskManager.js → DependencyAnalyzer.js → TaskManager.js

// 解决方案：引入中介者模式
TaskManager.js → EventBus → DependencyAnalyzer.js
DependencyAnalyzer.js → EventBus → TaskManager.js
```

**依赖注入解决方案**：
```javascript
// src/core/DependencyInjector.js
class DependencyInjector {
  constructor() {
    this.dependencies = new Map();
    this.singletons = new Map();
  }

  register(name, factory, options = {}) {
    this.dependencies.set(name, {
      factory,
      singleton: options.singleton || false,
      dependencies: options.dependencies || []
    });
  }

  resolve(name) {
    const dep = this.dependencies.get(name);
    if (!dep) {
      throw new Error(`Dependency '${name}' not found`);
    }

    if (dep.singleton && this.singletons.has(name)) {
      return this.singletons.get(name);
    }

    // 解析依赖
    const resolvedDeps = dep.dependencies.map(depName => this.resolve(depName));
    const instance = dep.factory(...resolvedDeps);

    if (dep.singleton) {
      this.singletons.set(name, instance);
    }

    return instance;
  }
}

// 使用示例
const injector = new DependencyInjector();

injector.register('logger', () => new Logger(), { singleton: true });
injector.register('cache', () => new CacheManager(), { singleton: true });
injector.register('taskManager', (logger, cache) => new TaskManager(logger, cache), {
  singleton: true,
  dependencies: ['logger', 'cache']
});
```

### 模块协作模式

#### 1. 事件驱动协作 (Event-Driven Collaboration)

```javascript
// src/core/EventBus.js
class EventBus extends EventEmitter {
  constructor() {
    super();
    this.setupCoreEventHandlers();
  }

  setupCoreEventHandlers() {
    // 任务事件处理
    this.on('task:created', async (task) => {
      // 触发复杂度分析
      this.emit('analysis:complexity:request', task);
      // 触发依赖验证
      this.emit('dependency:validate:request', task);
      // 发送通知
      this.emit('notification:send', { type: 'task_created', task });
    });

    this.on('task:completed', async (task) => {
      // 更新统计
      this.emit('stats:update', { type: 'task_completed', task });
      // 检查依赖任务
      this.emit('dependency:check_unblocked', task);
      // 发送通知
      this.emit('notification:send', { type: 'task_completed', task });
    });

    // AI 服务事件处理
    this.on('ai:request:start', (requestInfo) => {
      this.emit('metrics:record', { type: 'ai_request_start', data: requestInfo });
    });

    this.on('ai:request:complete', (requestInfo) => {
      this.emit('metrics:record', { type: 'ai_request_complete', data: requestInfo });
      this.emit('cache:store', requestInfo);
    });
  }
}

// 全局事件总线实例
const eventBus = new EventBus();
module.exports = eventBus;
```

#### 2. 中间件协作模式 (Middleware Collaboration)

```javascript
// src/core/MiddlewareStack.js
class MiddlewareStack {
  constructor() {
    this.middleware = [];
  }

  use(middleware) {
    this.middleware.push(middleware);
    return this;
  }

  async execute(context, next) {
    let index = 0;

    const dispatch = async (i) => {
      if (i <= index) {
        throw new Error('next() called multiple times');
      }

      index = i;

      if (i === this.middleware.length) {
        return next ? await next() : undefined;
      }

      const middleware = this.middleware[i];
      return await middleware(context, () => dispatch(i + 1));
    };

    return await dispatch(0);
  }
}

// 使用示例
const middlewareStack = new MiddlewareStack();

// 认证中间件
middlewareStack.use(async (context, next) => {
  if (!context.user) {
    throw new Error('Authentication required');
  }
  await next();
});

// 权限检查中间件
middlewareStack.use(async (context, next) => {
  const hasPermission = await checkPermission(context.user, context.action);
  if (!hasPermission) {
    throw new Error('Insufficient permissions');
  }
  await next();
});

// 日志记录中间件
middlewareStack.use(async (context, next) => {
  const startTime = Date.now();
  try {
    await next();
    logger.info('Action completed', {
      action: context.action,
      duration: Date.now() - startTime
    });
  } catch (error) {
    logger.error('Action failed', {
      action: context.action,
      error: error.message,
      duration: Date.now() - startTime
    });
    throw error;
  }
});
```

#### 3. 观察者模式协作 (Observer Pattern Collaboration)

```javascript
// src/core/ObserverManager.js
class ObserverManager {
  constructor() {
    this.observers = new Map();
  }

  subscribe(event, observer) {
    if (!this.observers.has(event)) {
      this.observers.set(event, new Set());
    }
    this.observers.get(event).add(observer);
  }

  unsubscribe(event, observer) {
    const observers = this.observers.get(event);
    if (observers) {
      observers.delete(observer);
    }
  }

  async notify(event, data) {
    const observers = this.observers.get(event);
    if (!observers) return;

    const promises = Array.from(observers).map(observer =>
      observer.handle(event, data)
    );

    await Promise.allSettled(promises);
  }
}

// 观察者实现示例
class TaskAnalysisObserver {
  async handle(event, data) {
    if (event === 'task:created') {
      await this.analyzeTaskComplexity(data.task);
    }
  }

  async analyzeTaskComplexity(task) {
    const aiProvider = require('./AIProviderManager');
    const complexity = await aiProvider.request(
      `Analyze the complexity of this task: ${task.title}`,
      { capability: 'analysis' }
    );

    // 更新任务复杂度
    await updateTask(task.id, { complexity: complexity.score });
  }
}

// 使用示例
const observerManager = new ObserverManager();
observerManager.subscribe('task:created', new TaskAnalysisObserver());
```

---

## 使用场景和实际应用

### 典型调用链路分析

#### 场景 1：创建新任务的完整流程

```javascript
// 1. 用户执行命令
$ task-master add-task "实现用户认证功能" --priority=high --auto-expand

// 2. CLI 入口处理 (cli.js)
cli.js → CommandRegistry.get('add-task') → AddTaskCommand.execute()

// 3. 命令执行流程 (AddTaskCommand.js)
AddTaskCommand.execute() {
  // 3.1 参数验证
  await this.validateArguments(args, options);

  // 3.2 AI 分析任务
  const analysis = await this.analyzeTask(taskTitle);

  // 3.3 创建任务
  const task = await this.createTask(taskData);

  // 3.4 自动扩展（如果启用）
  if (options.autoExpand) {
    await this.expandTask(task);
  }

  // 3.5 显示结果
  await this.displayResult(task);
}

// 4. AI 分析调用链 (AIProviderManager.js)
AIProviderManager.request() {
  // 4.1 选择最佳提供商
  const provider = await this.selectProvider(prompt, options);

  // 4.2 检查缓存
  const cached = await this.cache.get(cacheKey);

  // 4.3 执行请求
  const response = await provider.instance.request(prompt);

  // 4.4 缓存结果
  await this.cache.set(cacheKey, response);

  return response;
}

// 5. 任务创建调用链 (TaskManager.js)
TaskManager.createTask() {
  // 5.1 验证任务数据
  await this.validateTaskData(taskData);

  // 5.2 生成任务 ID
  const taskId = this.generateTaskId();

  // 5.3 保存到数据库
  await this.database.save('tasks', task);

  // 5.4 触发事件
  this.eventBus.emit('task:created', task);

  return task;
}

// 6. 事件处理链 (EventBus)
eventBus.on('task:created') {
  // 6.1 复杂度分析
  emit('analysis:complexity:request', task);

  // 6.2 依赖验证
  emit('dependency:validate:request', task);

  // 6.3 发送通知
  emit('notification:send', { type: 'task_created', task });
}
```

#### 场景 2：智能工作流执行流程

```javascript
// 1. 用户执行智能工作流
$ task-master workflows/smart-flow morning

// 2. 工作流命令处理 (SmartWorkflowCommand.js)
SmartWorkflowCommand.execute() {
  // 2.1 分析当前上下文
  const context = await this.analyzeContext(options);

  // 2.2 获取推荐工作流
  const workflow = await this.getRecommendedWorkflow(context);

  // 2.3 执行工作流
  const result = await this.executeWorkflow(workflow, context);

  return result;
}

// 3. 上下文分析 (ContextAnalyzer.js)
ContextAnalyzer.analyzeContext() {
  return {
    timeOfDay: this.getTimeOfDay(),
    teamActivity: await this.getTeamActivity(),
    userBehavior: await this.getUserBehaviorPattern(),
    projectPhase: await this.getProjectPhase(),
    systemLoad: await this.getSystemLoad()
  };
}

// 4. 工作流推荐 (WorkflowEngine.js)
WorkflowEngine.getRecommendedWorkflow() {
  // 4.1 获取可用工作流
  const workflows = await this.getAvailableWorkflows();

  // 4.2 AI 评分
  const scoredWorkflows = await this.scoreWorkflows(workflows, context);

  // 4.3 选择最佳工作流
  return scoredWorkflows[0];
}

// 5. 工作流执行 (WorkflowEngine.js)
WorkflowEngine.executeWorkflow() {
  for (const step of workflow.steps) {
    // 5.1 执行步骤
    const result = await this.executeStep(step, context);

    // 5.2 更新上下文
    context = this.updateContext(context, result);

    // 5.3 检查条件
    if (!this.checkContinueCondition(step, result)) {
      break;
    }
  }
}
```

#### 场景 3：依赖分析和关键路径计算

```javascript
// 1. 用户执行依赖验证
$ task-master validate-dependencies --fix-auto

// 2. 依赖验证命令 (ValidateDependenciesCommand.js)
ValidateDependenciesCommand.execute() {
  // 2.1 获取所有任务
  const tasks = await this.taskManager.getAllTasks();

  // 2.2 构建依赖图
  const graph = await this.dependencyAnalyzer.buildGraph(tasks);

  // 2.3 验证依赖
  const validation = await this.dependencyAnalyzer.validate(graph);

  // 2.4 自动修复（如果启用）
  if (options.fixAuto && validation.errors.length > 0) {
    await this.fixDependencyErrors(validation.errors);
  }

  return validation;
}

// 3. 依赖图构建 (DependencyAnalyzer.js)
DependencyAnalyzer.buildGraph() {
  const graph = new DependencyGraph();

  // 3.1 添加任务节点
  for (const task of tasks) {
    graph.addTask(task);
  }

  // 3.2 添加依赖边
  for (const task of tasks) {
    for (const depId of task.dependencies || []) {
      graph.addDependency(task.id, depId);
    }
  }

  return graph;
}

// 4. 依赖验证 (DependencyAnalyzer.js)
DependencyAnalyzer.validate() {
  const validation = {
    valid: true,
    errors: [],
    warnings: [],
    criticalPath: null
  };

  // 4.1 检查循环依赖
  try {
    const sorted = graph.topologicalSort();
    validation.criticalPath = graph.calculateCriticalPath();
  } catch (error) {
    validation.valid = false;
    validation.errors.push({
      type: 'circular_dependency',
      message: error.message
    });
  }

  // 4.2 检查孤立任务
  const orphanTasks = this.findOrphanTasks(graph);
  if (orphanTasks.length > 0) {
    validation.warnings.push({
      type: 'orphan_tasks',
      tasks: orphanTasks
    });
  }

  return validation;
}
```

### 错误处理和边界情况

#### 1. AI 服务故障处理

```javascript
// AIProviderManager.js 中的故障处理
async handleAIServiceFailure(error, prompt, options) {
  this.logger.error('AI service failure:', error);

  // 1. 尝试备用提供商
  const fallbackProviders = this.getFallbackProviders();

  for (const provider of fallbackProviders) {
    try {
      const response = await provider.request(prompt, options);
      this.logger.info('Fallback successful', { provider: provider.name });
      return response;
    } catch (fallbackError) {
      this.logger.warn('Fallback failed', {
        provider: provider.name,
        error: fallbackError.message
      });
    }
  }

  // 2. 降级处理
  if (options.allowDegradedMode) {
    return this.handleDegradedMode(prompt, options);
  }

  // 3. 抛出最终错误
  throw new Error('All AI providers failed');
}

// 降级模式处理
handleDegradedMode(prompt, options) {
  // 使用本地规则引擎或缓存的响应
  return {
    content: 'AI service temporarily unavailable. Using fallback response.',
    degraded: true,
    confidence: 0.3
  };
}
```

#### 2. 数据库连接故障处理

```javascript
// DatabaseManager.js 中的连接故障处理
async handleConnectionFailure(error, operation, retryCount = 0) {
  const maxRetries = this.config.maxRetries || 3;
  const retryDelay = this.config.retryDelay || 1000;

  this.logger.error('Database connection failure:', {
    error: error.message,
    operation,
    retryCount
  });

  if (retryCount < maxRetries) {
    // 等待后重试
    await this.delay(retryDelay * Math.pow(2, retryCount));

    try {
      // 重新建立连接
      await this.reconnect();

      // 重试操作
      return await this.retryOperation(operation);
    } catch (retryError) {
      return this.handleConnectionFailure(retryError, operation, retryCount + 1);
    }
  }

  // 启用只读模式
  this.enableReadOnlyMode();
  throw new Error('Database unavailable. System in read-only mode.');
}
```

#### 3. 内存不足处理

```javascript
// MemoryManager.js 中的内存管理
class MemoryManager {
  constructor() {
    this.memoryThreshold = 0.85; // 85% 内存使用率阈值
    this.setupMemoryMonitoring();
  }

  setupMemoryMonitoring() {
    setInterval(() => {
      const usage = process.memoryUsage();
      const usagePercent = usage.heapUsed / usage.heapTotal;

      if (usagePercent > this.memoryThreshold) {
        this.handleHighMemoryUsage(usage);
      }
    }, 10000); // 每10秒检查一次
  }

  async handleHighMemoryUsage(usage) {
    this.logger.warn('High memory usage detected', usage);

    // 1. 清理缓存
    await this.clearNonEssentialCache();

    // 2. 强制垃圾回收
    if (global.gc) {
      global.gc();
    }

    // 3. 限制并发操作
    this.limitConcurrentOperations();

    // 4. 如果仍然过高，拒绝新请求
    const newUsage = process.memoryUsage();
    const newUsagePercent = newUsage.heapUsed / newUsage.heapTotal;

    if (newUsagePercent > 0.95) {
      throw new Error('System overloaded. Please try again later.');
    }
  }
}
```

---

## 配置和扩展指南

### 配置文件结构详解

#### 1. 主配置文件 (`config/default.json`)

```json
{
  "app": {
    "name": "Task Master AI",
    "version": "2.0.0",
    "environment": "development",
    "debug": true,
    "logLevel": "info"
  },
  "server": {
    "port": 3000,
    "host": "0.0.0.0",
    "cors": {
      "enabled": true,
      "origins": ["http://localhost:3000", "https://taskmaster.company.com"],
      "credentials": true
    },
    "rateLimit": {
      "enabled": true,
      "windowMs": 900000,
      "max": 100,
      "message": "Too many requests from this IP"
    },
    "compression": {
      "enabled": true,
      "threshold": 1024
    }
  },
  "database": {
    "type": "postgresql",
    "host": "localhost",
    "port": 5432,
    "database": "taskmaster",
    "username": "taskmaster",
    "password": "${DB_PASSWORD}",
    "ssl": false,
    "pool": {
      "min": 2,
      "max": 20,
      "acquireTimeoutMillis": 30000,
      "idleTimeoutMillis": 30000
    },
    "migrations": {
      "directory": "./migrations",
      "autoRun": true
    }
  },
  "cache": {
    "type": "redis",
    "host": "localhost",
    "port": 6379,
    "password": "${REDIS_PASSWORD}",
    "db": 0,
    "ttl": 3600,
    "keyPrefix": "taskmaster:",
    "cluster": {
      "enabled": false,
      "nodes": []
    }
  },
  "ai": {
    "defaultProvider": "claude",
    "routingStrategy": "capability",
    "fallbackEnabled": true,
    "cacheEnabled": true,
    "cacheTTL": 3600,
    "providers": {
      "claude": {
        "enabled": true,
        "apiKey": "${ANTHROPIC_API_KEY}",
        "model": "claude-3-5-sonnet-********",
        "maxTokens": 200000,
        "temperature": 0.7,
        "priority": 1,
        "costPerToken": 0.003,
        "capabilities": ["analysis", "generation", "reasoning"],
        "rateLimit": {
          "requests": 100,
          "window": 60000
        }
      },
      "openai": {
        "enabled": true,
        "apiKey": "${OPENAI_API_KEY}",
        "model": "gpt-4",
        "maxTokens": 128000,
        "temperature": 0.7,
        "priority": 2,
        "costPerToken": 0.03,
        "capabilities": ["generation", "analysis", "chat"],
        "rateLimit": {
          "requests": 60,
          "window": 60000
        }
      },
      "perplexity": {
        "enabled": true,
        "apiKey": "${PERPLEXITY_API_KEY}",
        "model": "llama-3.1-sonar-large-128k-online",
        "maxTokens": 32000,
        "temperature": 0.5,
        "priority": 3,
        "costPerToken": 0.001,
        "capabilities": ["research", "analysis"],
        "rateLimit": {
          "requests": 50,
          "window": 60000
        }
      }
    }
  },
  "integrations": {
    "jira": {
      "enabled": false,
      "baseUrl": "${JIRA_BASE_URL}",
      "username": "${JIRA_USERNAME}",
      "apiToken": "${JIRA_API_TOKEN}",
      "projectKey": "${JIRA_PROJECT_KEY}",
      "syncInterval": 300000,
      "fieldMapping": {
        "summary": "title",
        "description": "description",
        "assignee.displayName": "assignee",
        "priority.name": "priority"
      }
    },
    "slack": {
      "enabled": false,
      "botToken": "${SLACK_BOT_TOKEN}",
      "signingSecret": "${SLACK_SIGNING_SECRET}",
      "defaultChannel": "#taskmaster",
      "notifications": {
        "taskCreated": true,
        "taskCompleted": true,
        "taskBlocked": true,
        "sprintSummary": true
      }
    },
    "github": {
      "enabled": false,
      "token": "${GITHUB_TOKEN}",
      "owner": "${GITHUB_OWNER}",
      "repo": "${GITHUB_REPO}",
      "webhookSecret": "${GITHUB_WEBHOOK_SECRET}",
      "autoCreateIssues": true,
      "labelMapping": {
        "bug": "bug",
        "feature": "enhancement",
        "task": "task"
      }
    }
  },
  "security": {
    "authentication": {
      "enabled": true,
      "provider": "jwt",
      "jwtSecret": "${JWT_SECRET}",
      "tokenExpiry": "24h",
      "refreshTokenExpiry": "7d"
    },
    "authorization": {
      "enabled": true,
      "rbac": true,
      "defaultRole": "viewer"
    },
    "encryption": {
      "enabled": true,
      "algorithm": "aes-256-gcm",
      "key": "${ENCRYPTION_KEY}"
    },
    "audit": {
      "enabled": true,
      "logLevel": "info",
      "retentionDays": 90
    }
  },
  "monitoring": {
    "enabled": true,
    "metrics": {
      "enabled": true,
      "port": 9090,
      "path": "/metrics"
    },
    "healthCheck": {
      "enabled": true,
      "interval": 30000,
      "timeout": 5000
    },
    "logging": {
      "level": "info",
      "format": "json",
      "file": {
        "enabled": true,
        "path": "./logs/taskmaster.log",
        "maxSize": "100MB",
        "maxFiles": 10
      },
      "console": {
        "enabled": true,
        "colorize": true
      }
    }
  },
  "plugins": {
    "enabled": true,
    "directory": "./plugins",
    "autoLoad": true,
    "whitelist": [],
    "blacklist": []
  },
  "workflows": {
    "enabled": true,
    "defaultStrategy": "capability",
    "maxConcurrentWorkflows": 10,
    "timeoutMs": 300000
  }
}
```

#### 2. 环境特定配置

**开发环境配置 (`config/development.json`)**：
```json
{
  "app": {
    "debug": true,
    "logLevel": "debug"
  },
  "database": {
    "host": "localhost",
    "database": "taskmaster_dev",
    "ssl": false
  },
  "ai": {
    "cacheEnabled": false,
    "providers": {
      "claude": {
        "model": "claude-3-haiku-20240307"
      }
    }
  },
  "monitoring": {
    "logging": {
      "level": "debug",
      "console": {
        "colorize": true
      }
    }
  }
}
```

**生产环境配置 (`config/production.json`)**：
```json
{
  "app": {
    "debug": false,
    "logLevel": "warn"
  },
  "server": {
    "cors": {
      "origins": ["https://taskmaster.company.com"]
    }
  },
  "database": {
    "ssl": true,
    "pool": {
      "min": 5,
      "max": 50
    }
  },
  "cache": {
    "cluster": {
      "enabled": true,
      "nodes": [
        "redis-1.company.com:6379",
        "redis-2.company.com:6379",
        "redis-3.company.com:6379"
      ]
    }
  },
  "security": {
    "authentication": {
      "tokenExpiry": "8h"
    },
    "audit": {
      "logLevel": "warn"
    }
  },
  "monitoring": {
    "logging": {
      "level": "warn",
      "console": {
        "enabled": false
      }
    }
  }
}
```

### 自定义命令开发指南

#### 1. 创建自定义命令

```javascript
// src/commands/custom/CustomAnalysisCommand.js
const BaseCommand = require('../base/BaseCommand');
const { AIProviderManager } = require('../../core/ai/AIProviderManager');
const { TaskManager } = require('../../core/data/TaskManager');

class CustomAnalysisCommand extends BaseCommand {
  constructor() {
    super('custom-analysis', '执行自定义任务分析');

    // 添加命令选项
    this.addOption('-t, --type <type>', '分析类型 (complexity|risk|timeline)', 'complexity')
      .addOption('-f, --format <format>', '输出格式 (json|table|chart)', 'table')
      .addOption('--include-subtasks', '包含子任务分析', false)
      .addOption('--ai-provider <provider>', '指定 AI 提供商', 'claude');

    // 添加命令参数
    this.addArgument('<task-ids...>', '要分析的任务 ID 列表');

    // 添加验证器
    this.addHook('beforeValidation', this.validateTaskIds.bind(this));
    this.addHook('afterExecution', this.saveAnalysisResult.bind(this));
  }

  /**
   * 验证任务 ID
   */
  async validateTaskIds(context) {
    const { args } = context;
    const taskManager = new TaskManager();

    for (const taskId of args) {
      const task = await taskManager.getTask(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }
    }
  }

  /**
   * 主要执行逻辑
   */
  async execute(args, options, context) {
    const taskIds = args;
    const analysisType = options.type;
    const format = options.format;

    // 获取任务数据
    const tasks = await this.getTasks(taskIds, options.includeSubtasks);

    // 执行分析
    const analysisResult = await this.performAnalysis(tasks, analysisType, options);

    // 格式化结果
    const formattedResult = await this.formatResult(analysisResult, format);

    return formattedResult;
  }

  /**
   * 获取任务数据
   */
  async getTasks(taskIds, includeSubtasks) {
    const taskManager = new TaskManager();
    const tasks = [];

    for (const taskId of taskIds) {
      const task = await taskManager.getTask(taskId);
      tasks.push(task);

      if (includeSubtasks && task.subtasks) {
        const subtasks = await taskManager.getSubtasks(taskId);
        tasks.push(...subtasks);
      }
    }

    return tasks;
  }

  /**
   * 执行分析
   */
  async performAnalysis(tasks, analysisType, options) {
    const aiProvider = new AIProviderManager();

    switch (analysisType) {
      case 'complexity':
        return await this.analyzeComplexity(tasks, aiProvider, options);
      case 'risk':
        return await this.analyzeRisk(tasks, aiProvider, options);
      case 'timeline':
        return await this.analyzeTimeline(tasks, aiProvider, options);
      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }
  }

  /**
   * 复杂度分析
   */
  async analyzeComplexity(tasks, aiProvider, options) {
    const results = [];

    for (const task of tasks) {
      const prompt = `
        分析以下任务的复杂度，考虑技术难度、业务复杂性、集成要求和测试需求：

        任务标题：${task.title}
        任务描述：${task.description || '无描述'}
        预估工时：${task.estimatedHours || '未设置'}

        请提供：
        1. 总体复杂度评分 (1-10)
        2. 各维度分析 (技术、业务、集成、测试)
        3. 复杂度原因说明
        4. 降低复杂度的建议
      `;

      const response = await aiProvider.request(prompt, {
        provider: options.aiProvider,
        capability: 'analysis'
      });

      results.push({
        taskId: task.id,
        taskTitle: task.title,
        complexity: this.parseComplexityScore(response.content),
        analysis: response.content,
        recommendations: this.extractRecommendations(response.content)
      });
    }

    return {
      type: 'complexity',
      tasks: results,
      summary: this.generateComplexitySummary(results),
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * 风险分析
   */
  async analyzeRisk(tasks, aiProvider, options) {
    const results = [];

    for (const task of tasks) {
      const prompt = `
        分析以下任务的风险因素：

        任务：${task.title}
        描述：${task.description || '无描述'}
        依赖：${task.dependencies?.length || 0} 个依赖任务
        截止日期：${task.dueDate || '未设置'}

        请识别：
        1. 技术风险
        2. 时间风险
        3. 依赖风险
        4. 资源风险
        5. 风险等级 (低/中/高)
        6. 风险缓解建议
      `;

      const response = await aiProvider.request(prompt, {
        provider: options.aiProvider,
        capability: 'analysis'
      });

      results.push({
        taskId: task.id,
        taskTitle: task.title,
        riskLevel: this.parseRiskLevel(response.content),
        risks: this.extractRisks(response.content),
        mitigations: this.extractMitigations(response.content)
      });
    }

    return {
      type: 'risk',
      tasks: results,
      summary: this.generateRiskSummary(results),
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * 时间线分析
   */
  async analyzeTimeline(tasks, aiProvider, options) {
    const prompt = `
      分析以下任务的时间安排和依赖关系：

      ${tasks.map(task => `
        任务 ${task.id}: ${task.title}
        预估工时: ${task.estimatedHours || '未设置'}
        依赖: ${task.dependencies?.join(', ') || '无'}
        优先级: ${task.priority || '中'}
      `).join('\n')}

      请提供：
      1. 建议的执行顺序
      2. 关键路径分析
      3. 时间优化建议
      4. 并行执行可能性
      5. 里程碑建议
    `;

    const response = await aiProvider.request(prompt, {
      provider: options.aiProvider,
      capability: 'analysis'
    });

    return {
      type: 'timeline',
      tasks: tasks.map(task => ({
        taskId: task.id,
        taskTitle: task.title,
        estimatedHours: task.estimatedHours,
        dependencies: task.dependencies
      })),
      analysis: response.content,
      recommendations: this.extractTimelineRecommendations(response.content),
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * 格式化结果
   */
  async formatResult(result, format) {
    switch (format) {
      case 'json':
        return JSON.stringify(result, null, 2);
      case 'table':
        return this.formatAsTable(result);
      case 'chart':
        return this.formatAsChart(result);
      default:
        return result;
    }
  }

  /**
   * 表格格式化
   */
  formatAsTable(result) {
    const Table = require('cli-table3');

    if (result.type === 'complexity') {
      const table = new Table({
        head: ['任务 ID', '任务标题', '复杂度', '主要风险'],
        colWidths: [10, 40, 10, 40]
      });

      result.tasks.forEach(task => {
        table.push([
          task.taskId,
          task.taskTitle,
          task.complexity,
          task.recommendations.slice(0, 2).join('; ')
        ]);
      });

      return table.toString();
    }

    // 其他格式的表格实现...
    return result;
  }

  /**
   * 保存分析结果
   */
  async saveAnalysisResult(context) {
    const { result } = context;
    const fs = require('fs').promises;
    const path = require('path');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `analysis-${result.type}-${timestamp}.json`;
    const filepath = path.join(process.cwd(), '.taskmaster', 'analysis', filename);

    // 确保目录存在
    await fs.mkdir(path.dirname(filepath), { recursive: true });

    // 保存结果
    await fs.writeFile(filepath, JSON.stringify(result, null, 2));

    this.logger.info(`Analysis result saved to: ${filepath}`);
  }

  /**
   * 解析复杂度分数
   */
  parseComplexityScore(content) {
    const match = content.match(/复杂度[：:]?\s*(\d+)/);
    return match ? parseInt(match[1]) : 5;
  }

  /**
   * 提取建议
   */
  extractRecommendations(content) {
    const lines = content.split('\n');
    const recommendations = [];

    let inRecommendations = false;
    for (const line of lines) {
      if (line.includes('建议') || line.includes('推荐')) {
        inRecommendations = true;
        continue;
      }

      if (inRecommendations && line.trim()) {
        recommendations.push(line.trim());
      }
    }

    return recommendations;
  }

  /**
   * 显示格式化结果
   */
  displayFormattedResult(result, options) {
    if (options.format === 'json') {
      console.log(result);
    } else if (options.format === 'table') {
      console.log(result);
    } else {
      console.log('分析完成！');
      console.log(`分析类型: ${result.type}`);
      console.log(`任务数量: ${result.tasks.length}`);
      console.log(`生成时间: ${result.generatedAt}`);
    }
  }
}

module.exports = CustomAnalysisCommand;
```

#### 2. 注册自定义命令

```javascript
// src/commands/index.js
const CustomAnalysisCommand = require('./custom/CustomAnalysisCommand');

// 在 CommandRegistry 中注册
const registry = new CommandRegistry();
registry.register(new CustomAnalysisCommand(), {
  category: 'analysis',
  permissions: ['task:read', 'analysis:execute'],
  aliases: ['ca', 'custom-analyze']
});
```

### 插件开发指南

#### 1. 插件基础结构

```javascript
// plugins/time-tracking/index.js
const BasePlugin = require('../core/BasePlugin');
const TimeTrackingCommand = require('./commands/TimeTrackingCommand');
const TimeReportCommand = require('./commands/TimeReportCommand');

class TimeTrackingPlugin extends BasePlugin {
  constructor() {
    super('time-tracking', '1.0.0', {
      description: '时间跟踪插件',
      author: 'Task Master Team',
      dependencies: ['core'],
      permissions: ['time:read', 'time:write']
    });
  }

  async initialize(context) {
    // 注册命令
    this.registerCommand('start-timer', TimeTrackingCommand);
    this.registerCommand('time-report', TimeReportCommand);

    // 注册事件监听器
    this.registerEventHandler('task:started', this.onTaskStarted.bind(this));
    this.registerEventHandler('task:completed', this.onTaskCompleted.bind(this));

    // 注册 API 路由
    this.registerAPIRoute('POST', '/api/v1/time/start', this.startTimer.bind(this));
    this.registerAPIRoute('POST', '/api/v1/time/stop', this.stopTimer.bind(this));

    // 初始化数据库表
    await this.initializeDatabase();

    this.logger.info('Time Tracking Plugin initialized');
  }

  async onTaskStarted(event) {
    const { task, user } = event;
    await this.startTimer({ taskId: task.id, userId: user.id });
  }

  async onTaskCompleted(event) {
    const { task, user } = event;
    await this.stopTimer({ taskId: task.id, userId: user.id });
  }

  async startTimer(params) {
    const { taskId, userId } = params;

    // 停止其他正在运行的计时器
    await this.stopAllActiveTimers(userId);

    // 开始新的计时器
    const timer = {
      id: this.generateTimerId(),
      taskId,
      userId,
      startTime: new Date(),
      endTime: null,
      duration: null,
      status: 'active'
    };

    await this.database.save('time_entries', timer);

    this.emit('timer:started', { timer, taskId, userId });

    return timer;
  }

  async stopTimer(params) {
    const { taskId, userId } = params;

    const activeTimer = await this.database.findOne('time_entries', {
      taskId,
      userId,
      status: 'active'
    });

    if (!activeTimer) {
      throw new Error('No active timer found for this task');
    }

    const endTime = new Date();
    const duration = endTime - new Date(activeTimer.startTime);

    await this.database.update('time_entries', activeTimer.id, {
      endTime,
      duration,
      status: 'completed'
    });

    this.emit('timer:stopped', {
      timer: activeTimer,
      duration,
      taskId,
      userId
    });

    return { duration, hours: duration / (1000 * 60 * 60) };
  }

  async initializeDatabase() {
    const schema = `
      CREATE TABLE IF NOT EXISTS time_entries (
        id VARCHAR(255) PRIMARY KEY,
        task_id VARCHAR(255) NOT NULL,
        user_id VARCHAR(255) NOT NULL,
        start_time TIMESTAMP NOT NULL,
        end_time TIMESTAMP,
        duration INTEGER,
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX IF NOT EXISTS idx_time_entries_task_user
      ON time_entries(task_id, user_id);

      CREATE INDEX IF NOT EXISTS idx_time_entries_status
      ON time_entries(status);
    `;

    await this.database.execute(schema);
  }

  async cleanup() {
    // 停止所有活动计时器
    await this.database.update('time_entries',
      { status: 'active' },
      { status: 'interrupted', endTime: new Date() }
    );

    this.logger.info('Time Tracking Plugin cleaned up');
  }
}

module.exports = TimeTrackingPlugin;
```

#### 2. 插件配置文件

```json
// plugins/time-tracking/plugin.json
{
  "name": "time-tracking",
  "version": "1.0.0",
  "description": "时间跟踪插件，支持任务计时和时间报告",
  "author": "Task Master Team",
  "license": "MIT",
  "main": "index.js",
  "dependencies": {
    "core": ">=2.0.0"
  },
  "permissions": [
    "time:read",
    "time:write",
    "task:read",
    "user:read"
  ],
  "config": {
    "autoStart": true,
    "autoStop": true,
    "reminderInterval": 1800000,
    "maxDailyHours": 12
  },
  "api": {
    "routes": [
      {
        "method": "POST",
        "path": "/time/start",
        "handler": "startTimer",
        "permissions": ["time:write"]
      },
      {
        "method": "POST",
        "path": "/time/stop",
        "handler": "stopTimer",
        "permissions": ["time:write"]
      },
      {
        "method": "GET",
        "path": "/time/report",
        "handler": "getTimeReport",
        "permissions": ["time:read"]
      }
    ]
  },
  "commands": [
    {
      "name": "start-timer",
      "description": "开始任务计时",
      "permissions": ["time:write"]
    },
    {
      "name": "stop-timer",
      "description": "停止任务计时",
      "permissions": ["time:write"]
    },
    {
      "name": "time-report",
      "description": "生成时间报告",
      "permissions": ["time:read"]
    }
  ],
  "events": [
    {
      "name": "timer:started",
      "description": "计时器开始事件"
    },
    {
      "name": "timer:stopped",
      "description": "计时器停止事件"
    }
  ]
}
```

### 最佳实践和注意事项

#### 1. 性能优化最佳实践

```javascript
// 1. 使用连接池
const pool = new Pool({
  max: 20,
  min: 5,
  acquireTimeoutMillis: 30000,
  idleTimeoutMillis: 30000
});

// 2. 实现缓存策略
const cache = new CacheManager({
  strategy: 'lru',
  maxSize: 1000,
  ttl: 3600
});

// 3. 批量操作
async function batchUpdateTasks(updates) {
  const batchSize = 100;
  const batches = chunk(updates, batchSize);

  for (const batch of batches) {
    await Promise.all(batch.map(update => updateTask(update)));
  }
}

// 4. 懒加载
const lazyLoadModule = () => {
  return import('./heavy-module.js');
};
```

#### 2. 安全最佳实践

```javascript
// 1. 输入验证
const validator = require('validator');

function validateTaskInput(input) {
  return {
    title: validator.escape(input.title),
    description: validator.escape(input.description),
    priority: validator.isIn(input.priority, ['low', 'medium', 'high'])
  };
}

// 2. SQL 注入防护
const query = 'SELECT * FROM tasks WHERE id = $1';
const result = await db.query(query, [taskId]);

// 3. API 密钥安全存储
const crypto = require('crypto');

function encryptAPIKey(apiKey, masterKey) {
  const cipher = crypto.createCipher('aes-256-gcm', masterKey);
  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}
```

#### 3. 错误处理最佳实践

```javascript
// 1. 统一错误处理
class TaskMasterError extends Error {
  constructor(message, code, statusCode = 500) {
    super(message);
    this.name = 'TaskMasterError';
    this.code = code;
    this.statusCode = statusCode;
  }
}

// 2. 错误分类
const ErrorCodes = {
  TASK_NOT_FOUND: 'TM001',
  INVALID_INPUT: 'TM002',
  AI_SERVICE_ERROR: 'TM003',
  DATABASE_ERROR: 'TM004',
  PERMISSION_DENIED: 'TM005'
};

// 3. 优雅降级
async function getTaskWithFallback(taskId) {
  try {
    return await getTaskFromDatabase(taskId);
  } catch (error) {
    logger.warn('Database error, trying cache', error);
    try {
      return await getTaskFromCache(taskId);
    } catch (cacheError) {
      logger.error('Both database and cache failed', cacheError);
      throw new TaskMasterError(
        'Task temporarily unavailable',
        ErrorCodes.DATABASE_ERROR,
        503
      );
    }
  }
}
```

这个详细的代码结构解读文档为开发者提供了全面的项目理解，包括完整的目录结构、核心文件分析、依赖关系图、使用场景示例，以及配置和扩展指南。通过这个文档，新开发者可以快速理解项目架构，掌握核心组件的工作原理，并学会如何扩展和自定义系统功能。
```
