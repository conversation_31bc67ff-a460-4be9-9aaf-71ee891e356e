import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Rate, 
  Button, 
  Input, 
  Avatar, 
  Space, 
  Typography, 
  Divider,
  Progress,
  Tag,
  Image,
  message,
  Modal,
  Form,
  Upload,
  Empty
} from 'antd';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  MessageOutlined,
  PictureOutlined,
  UserOutlined,
  StarFilled
} from '@ant-design/icons';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';
import { reviewsService } from '../../services/reviews';
import LoadingState from '../ui/LoadingState';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

const ReviewContainer = styled.div`
  .review-summary {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    
    .summary-header {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-bottom: 20px;
      
      .rating-display {
        text-align: center;
        
        .rating-number {
          font-size: 3rem;
          font-weight: 800;
          color: #faad14;
          line-height: 1;
        }
        
        .rating-stars {
          margin: 8px 0;
        }
        
        .rating-count {
          color: #8c8c8c;
          font-size: 14px;
        }
      }
      
      .rating-breakdown {
        flex: 1;
        
        .rating-row {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 8px;
          
          .rating-label {
            width: 60px;
            font-size: 14px;
          }
          
          .rating-progress {
            flex: 1;
          }
          
          .rating-count {
            width: 40px;
            text-align: right;
            font-size: 14px;
            color: #8c8c8c;
          }
        }
      }
    }
  }
  
  .review-actions {
    margin-bottom: 24px;
    
    .write-review-btn {
      background: linear-gradient(135deg, #1677ff, #4096ff);
      border: none;
      border-radius: 8px;
      height: 40px;
      font-weight: 600;
      
      &:hover {
        background: linear-gradient(135deg, #0958d9, #1677ff);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
      }
    }
  }
  
  .review-list {
    .review-item {
      padding: 20px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .review-header {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 16px;
        
        .user-avatar {
          flex-shrink: 0;
        }
        
        .review-meta {
          flex: 1;
          
          .user-name {
            font-weight: 600;
            margin-bottom: 4px;
          }
          
          .review-rating {
            margin-bottom: 8px;
          }
          
          .review-date {
            color: #8c8c8c;
            font-size: 13px;
          }
        }
      }
      
      .review-content {
        margin-bottom: 16px;
        
        .review-title {
          font-weight: 600;
          margin-bottom: 8px;
          font-size: 16px;
        }
        
        .review-text {
          line-height: 1.6;
          color: #262626;
        }
        
        .review-images {
          margin-top: 12px;
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
          
          .review-image {
            border-radius: 8px;
            overflow: hidden;
          }
        }
      }
      
      .review-actions {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .action-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border: none;
          background: none;
          color: #8c8c8c;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.3s ease;
          
          &:hover {
            background: #f5f5f5;
            color: #1677ff;
          }
          
          &.active {
            color: #1677ff;
            background: #e6f4ff;
          }
        }
      }
      
      .review-replies {
        margin-top: 16px;
        padding-left: 56px;
        
        .reply-item {
          background: #fafafa;
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 8px;
          
          .reply-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            
            .reply-author {
              font-weight: 600;
              font-size: 14px;
            }
            
            .reply-date {
              color: #8c8c8c;
              font-size: 12px;
            }
          }
          
          .reply-content {
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }
`;

const WriteReviewModal = styled(Modal)`
  .ant-modal-content {
    border-radius: 16px;
  }
  
  .review-form {
    .form-section {
      margin-bottom: 24px;
      
      .section-title {
        font-weight: 600;
        margin-bottom: 12px;
        color: #262626;
      }
    }
    
    .rating-section {
      text-align: center;
      
      .rating-input {
        font-size: 32px;
        margin-bottom: 8px;
      }
      
      .rating-desc {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
    
    .upload-section {
      .ant-upload-list {
        .ant-upload-list-item {
          border-radius: 8px;
        }
      }
    }
  }
`;

interface Review {
  id: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
  };
  rating: number;
  title?: string;
  content: string;
  images: string[];
  helpful_count: number;
  user_helpful: boolean | null;
  created_at: string;
  replies?: Review[];
}

interface ReviewStats {
  total_reviews: number;
  average_rating: number;
  rating_distribution: Record<string, number>;
}

interface ReviewSystemProps {
  bookId: string;
  className?: string;
}

const ReviewSystem: React.FC<ReviewSystemProps> = ({ bookId, className }) => {
  const { isAuthenticated, user } = useAuthStore();
  
  const [loading, setLoading] = useState(true);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [writeModalVisible, setWriteModalVisible] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  const [form] = Form.useForm();

  useEffect(() => {
    loadReviews();
    loadStats();
  }, [bookId]);

  const loadReviews = async () => {
    try {
      const response = await reviewsService.getBookReviews(bookId);
      if (response.success) {
        setReviews(response.data.reviews);
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    }
  };

  const loadStats = async () => {
    try {
      const response = await reviewsService.getBookReviewStats(bookId);
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('加载评论统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleWriteReview = async (values: any) => {
    try {
      setSubmitting(true);
      
      const response = await reviewsService.createReview({
        book_id: bookId,
        rating: values.rating,
        title: values.title,
        content: values.content,
        images: values.images?.map((file: any) => file.response?.url).filter(Boolean) || []
      });

      if (response.success) {
        message.success('评论发布成功');
        setWriteModalVisible(false);
        form.resetFields();
        loadReviews();
        loadStats();
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || '发布评论失败');
    } finally {
      setSubmitting(false);
    }
  };

  const handleHelpful = async (reviewId: string, isHelpful: boolean) => {
    if (!isAuthenticated) {
      message.warning('请先登录');
      return;
    }

    try {
      await reviewsService.markHelpful(reviewId, isHelpful);
      loadReviews();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const getRatingDesc = (rating: number) => {
    const descs = ['', '很差', '较差', '一般', '很好', '非常好'];
    return descs[rating] || '';
  };

  if (loading) {
    return <LoadingState loading={true} />;
  }

  return (
    <ReviewContainer className={className}>
      {/* 评论统计 */}
      {stats && (
        <Card className="review-summary" bordered={false}>
          <div className="summary-header">
            <div className="rating-display">
              <div className="rating-number">{stats.average_rating}</div>
              <div className="rating-stars">
                <Rate disabled value={stats.average_rating} allowHalf />
              </div>
              <div className="rating-count">{stats.total_reviews} 条评论</div>
            </div>
            
            <div className="rating-breakdown">
              {[5, 4, 3, 2, 1].map(rating => {
                const count = stats.rating_distribution[rating] || 0;
                const percentage = stats.total_reviews > 0 ? (count / stats.total_reviews) * 100 : 0;
                
                return (
                  <div key={rating} className="rating-row">
                    <div className="rating-label">
                      {rating} <StarFilled style={{ color: '#faad14', fontSize: '12px' }} />
                    </div>
                    <Progress 
                      className="rating-progress"
                      percent={percentage} 
                      showInfo={false}
                      strokeColor="#faad14"
                    />
                    <div className="rating-count">{count}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
      )}

      {/* 写评论按钮 */}
      <div className="review-actions">
        {isAuthenticated ? (
          <Button 
            type="primary" 
            className="write-review-btn"
            icon={<MessageOutlined />}
            onClick={() => setWriteModalVisible(true)}
          >
            写评论
          </Button>
        ) : (
          <Text type="secondary">登录后可以发表评论</Text>
        )}
      </div>

      {/* 评论列表 */}
      <div className="review-list">
        {reviews.length > 0 ? (
          reviews.map(review => (
            <div key={review.id} className="review-item">
              <div className="review-header">
                <Avatar 
                  className="user-avatar"
                  src={review.user.avatar} 
                  icon={<UserOutlined />}
                  size={48}
                />
                <div className="review-meta">
                  <div className="user-name">{review.user.username}</div>
                  <div className="review-rating">
                    <Rate disabled value={review.rating} size="small" />
                  </div>
                  <div className="review-date">
                    {new Date(review.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="review-content">
                {review.title && (
                  <div className="review-title">{review.title}</div>
                )}
                <div className="review-text">{review.content}</div>
                
                {review.images.length > 0 && (
                  <div className="review-images">
                    {review.images.map((image, index) => (
                      <Image
                        key={index}
                        className="review-image"
                        src={image}
                        width={80}
                        height={80}
                        style={{ objectFit: 'cover' }}
                      />
                    ))}
                  </div>
                )}
              </div>

              <div className="review-actions">
                <button
                  className={`action-btn ${review.user_helpful === true ? 'active' : ''}`}
                  onClick={() => handleHelpful(review.id, true)}
                >
                  {review.user_helpful === true ? <LikeFilled /> : <LikeOutlined />}
                  有用 ({review.helpful_count})
                </button>
                
                <button
                  className={`action-btn ${review.user_helpful === false ? 'active' : ''}`}
                  onClick={() => handleHelpful(review.id, false)}
                >
                  {review.user_helpful === false ? <DislikeFilled /> : <DislikeOutlined />}
                  无用
                </button>
              </div>

              {/* 回复 */}
              {review.replies && review.replies.length > 0 && (
                <div className="review-replies">
                  {review.replies.map(reply => (
                    <div key={reply.id} className="reply-item">
                      <div className="reply-header">
                        <span className="reply-author">{reply.user.username}</span>
                        <span className="reply-date">
                          {new Date(reply.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="reply-content">{reply.content}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        ) : (
          <Empty description="暂无评论" />
        )}
      </div>

      {/* 写评论弹窗 */}
      <WriteReviewModal
        title="写评论"
        open={writeModalVisible}
        onCancel={() => setWriteModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          className="review-form"
          onFinish={handleWriteReview}
          layout="vertical"
        >
          <div className="form-section rating-section">
            <div className="section-title">评分</div>
            <Form.Item
              name="rating"
              rules={[{ required: true, message: '请选择评分' }]}
            >
              <Rate className="rating-input" />
            </Form.Item>
            <Form.Item dependencies={['rating']} noStyle>
              {({ getFieldValue }) => {
                const rating = getFieldValue('rating');
                return rating ? (
                  <div className="rating-desc">{getRatingDesc(rating)}</div>
                ) : null;
              }}
            </Form.Item>
          </div>

          <div className="form-section">
            <div className="section-title">标题（可选）</div>
            <Form.Item name="title">
              <Input placeholder="简短描述您的评价" />
            </Form.Item>
          </div>

          <div className="form-section">
            <div className="section-title">评论内容</div>
            <Form.Item
              name="content"
              rules={[{ required: true, message: '请输入评论内容' }]}
            >
              <TextArea 
                rows={4} 
                placeholder="分享您的使用体验..."
                showCount
                maxLength={500}
              />
            </Form.Item>
          </div>

          <div className="form-section upload-section">
            <div className="section-title">上传图片（可选）</div>
            <Form.Item name="images">
              <Upload
                listType="picture-card"
                action="/api/upload/image"
                accept="image/*"
                maxCount={5}
              >
                <div>
                  <PictureOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              </Upload>
            </Form.Item>
          </div>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setWriteModalVisible(false)}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={submitting}
              >
                发布评论
              </Button>
            </Space>
          </div>
        </Form>
      </WriteReviewModal>
    </ReviewContainer>
  );
};

export default ReviewSystem;
