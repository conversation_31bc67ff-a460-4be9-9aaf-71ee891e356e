List all tasks including their subtasks in a hierarchical view.

This command shows all tasks with their nested subtasks, providing a complete project overview.

## Execution

Run the Task Master list command with subtasks flag:
```bash
task-master list --with-subtasks
```

## Enhanced Display

I'll organize the output to show:
- Parent tasks with clear indicators
- Nested subtasks with proper indentation
- Status badges for quick scanning
- Dependencies and blockers highlighted
- Progress indicators for tasks with subtasks

## Smart Filtering

Based on the task hierarchy:
- Show completion percentage for parent tasks
- Highlight blocked subtask chains
- Group by functional areas
- Indicate critical path items

This gives you a complete tree view of your project structure.